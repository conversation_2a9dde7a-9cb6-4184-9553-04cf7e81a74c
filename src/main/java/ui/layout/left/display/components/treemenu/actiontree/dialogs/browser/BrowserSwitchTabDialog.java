package ui.layout.left.display.components.treemenu.actiontree.dialogs.browser;

import com.alibaba.fastjson2.JSON;
import sdk.base.operation.Operation;
import sdk.constants.methods.CommonMethods;
import ui.layout.left.display.components.treemenu.actiontree.action_mgmt.BrowserAction;
import ui.layout.left.display.components.treemenu.actiontree.dialogs.ActionPopupDialog;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

/**
 * 浏览器切换标签页对话框
 */
public class BrowserSwitchTabDialog extends ActionPopupDialog<BrowserAction> {

    private volatile static BrowserSwitchTabDialog dialog;
    private final JTextField tabNameTextField;
    private final JButton switchTabButton;

    public static BrowserSwitchTabDialog getInstance(MainModel mainModel) {
        if (dialog == null) {
            dialog = new BrowserSwitchTabDialog(mainModel);
        }
        return dialog;
    }

    private BrowserSwitchTabDialog(MainModel mainModel) {
        super(mainModel);
        tabNameTextField = new JTextField(30);
        switchTabButton = new JButton("切换标签页");
        createView();
        createActions();
    }

    public BrowserSwitchTabDialog() {
        this(null);
    }

    @Override
    public void createView() {
        super.createView();
        setTitle("切换标签页");
        add(assemblePanel(), BorderLayout.NORTH);
    }

    @Override
    public boolean checkUserInput() {
        if (tabNameTextField.getText().isEmpty()) {
            SwingUtil.showWarningDialog(this, "标签页为空");
            return false;
        }
        return true;
    }

    @Override
    public void createActions() {
        super.createActions();
        switchTabButton.addActionListener(e -> assembleOperationAndExecute());
    }

    @Override
    public JPanel assemblePanel() {
        JPanel assemblePanel = new JPanel(new BorderLayout());
        Box box = Box.createHorizontalBox();
        box.add(new JLabel("标签页:"));
        box.add(tabNameTextField);
        box.add(switchTabButton);

        assemblePanel.add(box, BorderLayout.NORTH);
        return assemblePanel;
    }

    private void buildUI(BrowserAction browserAction) {
        tabNameTextField.setText(browserAction.getTabName());
    }


    @Override
    public void buildUIFromOperation(Operation injectedOperation) {
        BrowserAction browserAction = JSON.to(BrowserAction.class, injectedOperation.getOperationObject());
        buildUI(browserAction);
    }

    @Override
    public BrowserAction assembleOperationObject() {
        BrowserAction browserAction = new BrowserAction();
        browserAction.setTabName(tabNameTextField.getText());
        browserAction.setAction(BrowserAction.Action.SWITCH_TO_TAB.name());
        return browserAction;
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        injectedOperation.setOperationMethod(CommonMethods.browserSwitchToTab);
        injectedOperation.setOperationObject(assembleOperationObject());
        return injectedOperation;
    }

}
