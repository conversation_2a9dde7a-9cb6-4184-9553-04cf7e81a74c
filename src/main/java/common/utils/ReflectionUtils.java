package common.utils;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.net.JarURLConnection;
import java.net.URL;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.stream.Collectors;

/**
 * 反射工具类
 */
@Slf4j
public class ReflectionUtils {

    @Data
    public static class MethodReflection implements Comparable<MethodReflection> {

        private Class<?> returnType;
        private Class<?>[] paramTypes;

        @Override
        public int compareTo(MethodReflection methodReflection) {
            return methodReflection.getParamTypes().length - paramTypes.length;
        }

        @Override
        public String toString() {
            return String.format("MethodReflection(paramTypes=%s,returnType=%s)",
                    Arrays.stream(paramTypes).map(Class::getSimpleName).collect(Collectors.toList()), returnType.getSimpleName());
        }

    }

    /**
     * 定义类集合（用于存放所有加载的类）
     */
    private static final Set<Class<?>> CLASS_SET;

    static {
        //指定加载包路径
        CLASS_SET = getClassSet("com.desaysv.workserver");
    }

    /**
     * 获取类加载器
     *
     * @return ClassLoader
     */
    public static ClassLoader getClassLoader() {
        return Thread.currentThread().getContextClassLoader();
    }

    static class A {
        private int name;
        private int age;
        private int kkk;
    }

    public static Map<String, Object> getStaticProperties(Class<?> clazz) throws Exception {
        Map<String, Object> map = new LinkedHashMap<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            int mod = field.getModifiers();
            if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
                map.put(field.getName(), getStaticProperty(clazz, field.getName()));
            }
        }
        return map;
    }

    public static Object getStaticProperty(Class<?> clazz, String fieldName)
            throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(null);
    }


    public static String[] getFieldNamesIncludingSuper(Object object) {
        return getFieldNamesIncludingSuper(object.getClass());
    }

    public static String[] getFieldNamesIncludingSuper(Class<?> clazz) {
        Field[] fields = getDeclaredFieldIncludingSuper(clazz);
        String[] fieldNames = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            fieldNames[i] = fields[i].getName();
        }
        return fieldNames;
    }

    public static Field[] getDeclaredFieldIncludingSuper(Object object) {
        Class<?> clazz = object.getClass();
        return getDeclaredFieldIncludingSuper(clazz);
    }

    public static Field[] getDeclaredFieldIncludingSuper(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        for (; clazz != null && clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }

    public static String[] getFiledNames(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        String[] fieldNames = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            fieldNames[i] = fields[i].getName();
        }
        return fieldNames;
    }

    public static List<Object> getFieldValues(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        List<Object> fieldValues = new ArrayList<>();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                if (Modifier.isStatic(field.getModifiers()))
                    fieldValues.add(field.get(clazz));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return fieldValues;
    }

    public static String toElegantString(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        StringBuilder sb = new StringBuilder();
        try {
            sb.append("\n");
            for (Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                Object value = field.get(o);
                sb.append(String.format("%s=%s,%n", name, value));
            }
            sb.deleteCharAt(sb.lastIndexOf(","));
            return sb.toString();
        } catch (IllegalAccessException e) {
            return String.valueOf(o);
        }
    }

    public static Object getFieldValueByName(Object o, String fieldName) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter);
            return method.invoke(o);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static Object getFiledType(String fieldName, Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (Objects.equals(fieldName, field.getName())) {
                return field.getType();
            }
        }
        return null;
    }

    public static Object getPropertyValue(Object obj, String propertyName) throws IllegalAccessException {
        Class<?> clazz = obj.getClass();
        Field field;
        if ((field = getField(clazz, propertyName)) == null)
            return null;
        field.setAccessible(true);
        return field.get(clazz);
    }

    public static Field getField(Class<?> clazz, String propertyName) {
        if (clazz == null)
            return null;
        try {
            return clazz.getDeclaredField(propertyName);
        } catch (NoSuchFieldException e) {
            return getField(clazz.getSuperclass(), propertyName);
        }
    }


    /**
     * 加载类
     *
     * @param className     类全限定名称
     * @param isInitialized 是否在加载完成后执行静态代码块
     * @return
     */
    public static Class<?> loadClass(String className, boolean isInitialized) {
        Class<?> cls;
        try {
            cls = Class.forName(className, isInitialized, getClassLoader());
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        return cls;
    }

    public static Class<?> loadClass(String className) {
        return loadClass(className, true);
    }

    /**
     * 获取指定包下所有类
     *
     * @param packageName
     * @return
     */
    public static Set<Class<?>> getClassSet(String packageName) {
        Set<Class<?>> classSet = new HashSet<>();
        try {
            Enumeration<URL> urls = getClassLoader().getResources(packageName.replace(".", "/"));
            while (urls.hasMoreElements()) {
                URL url = urls.nextElement();
                if (url != null) {
                    String protocol = url.getProtocol();
                    if (protocol.equals("file")) {
                        String packagePath = url.getPath().replace("%20", "");
                        addClass(classSet, packagePath, packageName);
                    } else if (protocol.equals("jar")) {
                        JarURLConnection jarURLConnection = (JarURLConnection) url.openConnection();
                        if (jarURLConnection != null) {
                            JarFile jarFile = jarURLConnection.getJarFile();
                            if (jarFile != null) {
                                Enumeration<JarEntry> jarEntries = jarFile.entries();
                                while (jarEntries.hasMoreElements()) {
                                    JarEntry jarEntry = jarEntries.nextElement();
                                    String jarEntryName = jarEntry.getName();
                                    if (jarEntryName.endsWith(".class")) {
                                        String className = jarEntryName.substring(0, jarEntryName.lastIndexOf(".")).replaceAll("/", ".");
                                        doAddClass(classSet, className);
                                    }
                                }
                            }
                        }
                    }
                }
            }


        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return classSet;
    }

    private static void doAddClass(Set<Class<?>> classSet, String className) {
        Class<?> cls = loadClass(className, false);
        classSet.add(cls);
    }

    private static void addClass(Set<Class<?>> classSet, String packagePath, String packageName) {
        final File[] files = new File(packagePath).listFiles(file -> (file.isFile() && file.getName().endsWith(".class")) || file.isDirectory());
        assert files != null;
        for (File file : files) {
            String fileName = file.getName();
            if (file.isFile()) {
                String className = fileName.substring(0, fileName.lastIndexOf("."));
                if (StringUtils.isNotEmpty(packageName)) {
                    className = packageName + "." + className;
                }
                doAddClass(classSet, className);
            } else {
                String subPackagePath = fileName;
                if (StringUtils.isNotEmpty(packagePath)) {
                    subPackagePath = packagePath + "/" + subPackagePath;
                }
                String subPackageName = fileName;
                if (StringUtils.isNotEmpty(packageName)) {
                    subPackageName = packageName + "." + subPackageName;
                }
                addClass(classSet, subPackagePath, subPackageName);
            }
        }
    }


    public static Set<Class<?>> getClassSet() {
        return CLASS_SET;
    }

    /**
     * 获取应用包名下某父类（或接口）的所有子类（或实现类）
     *
     * @param superClass
     * @return
     */
    public static Set<Class<?>> getClassSetBySuper(Class<?> superClass) {
        Set<Class<?>> classSet = new HashSet<>();
        for (Class<?> cls : CLASS_SET) {
            if (superClass.isAssignableFrom(cls) && !superClass.equals(cls)) {
                classSet.add(cls);
            }
        }
        return classSet;
    }

    /**
     * 获取应用包名下带有某注解的类
     *
     * @param annotationClass
     * @return
     */
    public static Set<Class<?>> getClassSetByAnnotation(Class<? extends Annotation> annotationClass) {
        Set<Class<?>> classSet = new HashSet<>();
        for (Class<?> cls : CLASS_SET) {
            if (cls.isAnnotationPresent(annotationClass)) {
                classSet.add(cls);
            }
        }
        return classSet;
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredMethod
     *
     * @param object         : 子类对象
     * @param methodName     : 父类中的方法名
     * @param parameterTypes : 父类中的方法参数类型
     * @return 父类中的方法对象
     */
    public static Method getDeclaredMethod(Object object, String methodName, Class<?>... parameterTypes) {
        Method method;

        for (Class<?> clazz = object.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                method = clazz.getDeclaredMethod(methodName, parameterTypes);
                return method;
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return null;
    }

    /**
     * 判断对象是否有方法签名
     *
     * @param object         : 子类对象
     * @param methodName     : 父类中的方法名
     * @param parameterTypes : 父类中的方法参数类型
     * @return
     */
    public static boolean hasMethod(Object object,
                                    String methodName,
                                    Class<?>[] parameterTypes) {
        return getDeclaredMethod(object, methodName, parameterTypes) != null;
    }

    public static boolean hasEmptyParamMethod(Object object, String methodName) {
        return getDeclaredMethod(object, methodName) != null;
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredField
     *
     * @param object    : 子类对象
     * @param fieldName : 父类中的属性名
     * @return 父类中的属性对象
     */
    public static Field getDeclaredField(Object object, String fieldName) {
        Field field;
        Class<?> clazz = object.getClass();

        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                field = clazz.getDeclaredField(fieldName);
                return field;
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return null;
    }


    /**
     * 直接设置对象属性值, 忽略 private/protected 修饰符, 也不经过 setter
     *
     * @param object    : 子类对象
     * @param fieldName : 父类中的属性名
     * @param value     : 将要设置的值
     */
    public static void setFieldValue(Object object, String fieldName, Object value) {
        //根据 对象和属性名通过反射 调用上面的方法获取Field对象
        Field field = getDeclaredField(object, fieldName);
        if (field != null) {
            //抑制Java对其的检查
            field.setAccessible(true);

            try {
                //将 object 中 field 所代表的值 设置为 value
                field.set(object, value);
            } catch (IllegalArgumentException | IllegalAccessException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 直接读取对象的属性值, 忽略 private/protected 修饰符, 也不经过 getter
     *
     * @param object    : 子类对象
     * @param fieldName : 父类中的属性名
     * @return : 父类中的属性值
     */
    public static Object getFieldValue(Object object, String fieldName) {
        //根据 对象和属性名通过反射 调用上面的方法获取Field对象
        Field field = getDeclaredField(object, fieldName);
        if (field != null) {
            //抑制Java对其的检查
            field.setAccessible(true);

            try {
                //获取 object 中 field 所代表的属性值
                return field.get(object);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }


    public static List<MethodReflection> getReflectionByMethodName(Object object, String methodName) {
        List<MethodReflection> methodReflectionList = new ArrayList<>();

        // 要获取类的信息，首先要获取类的类型
        Class<?> clazz = object.getClass();
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                MethodReflection methodReflection = new MethodReflection();
                Class<?> returnType = method.getReturnType();
                methodReflection.setReturnType(returnType);
                // 获取参数类型
                Class<?>[] paramTypes = method.getParameterTypes();
                methodReflection.setParamTypes(paramTypes);
                methodReflectionList.add(methodReflection);
            }
        }
        Collections.sort(methodReflectionList);
        return methodReflectionList;
    }


    public static void main(String[] args) {
        Set<String> keySet = new HashSet<>();
        keySet.add("name");
        keySet.add("age");
        Class<?> clazz = A.class;
        List<String> fieldNames = Arrays.asList(ReflectionUtils.getFieldNamesIncludingSuper(clazz));
        System.out.println(fieldNames);
        if (!new HashSet<>(fieldNames).containsAll(keySet)) {
            System.out.println("none!!!");
        }
    }


}