package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.DcCollectorDevice;
import ui.layout.right.components.operate.operateview.base.BaudRateDeviceOperatePanel;
import ui.model.MainModel;

/**
 * <AUTHOR> lhy
 * @date : Created in 2025/2/16 15:34
 * @description :
 * @modified By :
 * @since : 2025/2/16
 **/
public class DcCollectorOperatePanel extends BaudRateDeviceOperatePanel {

    public DcCollectorOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        DcCollectorDevice httpClient = DcCollectorDevice.getDevice(deviceModel);
        setDeviceClient(httpClient);
        createView();
        createActions();
        restoreView();
        setBaudRate(115200);
    }
}
