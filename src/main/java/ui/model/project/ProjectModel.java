package ui.model.project;

import sdk.domain.Project;
import ui.base.BaseModel;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 14:38
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
public class ProjectModel implements BaseModel, ModelObservable, ProjectEventObserver {
    private final List<ModelObserver> modelObservers = new ArrayList<>();

    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    public void projectLoaded(Project project) {
        for (ModelObserver observer : modelObservers) {
            ((ProjectEventObserver) observer).projectLoaded(project);
        }
    }
}
