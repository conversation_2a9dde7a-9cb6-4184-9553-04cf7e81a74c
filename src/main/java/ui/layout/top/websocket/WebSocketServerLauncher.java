package ui.layout.top.websocket;


import lombok.extern.slf4j.Slf4j;
import org.glassfish.tyrus.server.Server;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/9/10 10:00
 */
@Slf4j
public class WebSocketServerLauncher {

    public static void main(String[] args) {
        Map<String, Object> properties = new HashMap<>();
        Server server = new Server("localhost", 8080, "/websocket", properties, WebSocketServer.class);

        try {
            server.start();
            System.out.println("WebSocket 服务器已启动 ws://localhost:8080/websocket");
            // 阻塞主线程以保持服务器运行
            System.in.read();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            server.stop();
            WebSocketServer.shutdown();
        }
    }
}