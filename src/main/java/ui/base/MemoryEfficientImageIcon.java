package ui.base;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.lang.ref.WeakReference;

public class MemoryEfficientImageIcon extends ImageIcon {
    private WeakReference<Component> lastComponent = new WeakReference<>(null);
    private BufferedImage scaledInstance;
    private Dimension lastSize;
    private final boolean constrained;
    private final Object scaleLock = new Object();
    
    public static MemoryEfficientImageIcon createAutoAdjustIcon(Image image, boolean constrained) {
        return new MemoryEfficientImageIcon(image, constrained);
    }
    
    private MemoryEfficientImageIcon(Image image, boolean constrained) {
        super(image);
        this.constrained = constrained;
    }
    
    @Override
    public synchronized void paintIcon(Component c, Graphics g, int x, int y) {
        if (c == null || g == null) return;
        
        Dimension currentSize = c.getSize();
        Component lastComp = lastComponent.get();
        
        // 检查是否需要重新缩放
        boolean needsRescale = scaledInstance == null || 
                             lastComp != c || 
                             !currentSize.equals(lastSize) ||
                             scaledInstance.getWidth() != currentSize.width ||
                             scaledInstance.getHeight() != currentSize.height;
        
        if (needsRescale) {
            // 清理旧的缓存图像
            clearOldScale();
            
            // 计算新的绘制参数
            Point startPoint = new Point(0, 0);
            Dimension targetSize = new Dimension(currentSize);
            
            if (constrained) {
                // 计算等比例缩放尺寸
                double ratio = 1.0 * getIconWidth() / getIconHeight();
                targetSize.width = (int) Math.min(currentSize.width, ratio * currentSize.height);
                targetSize.height = (int) (targetSize.width / ratio);
                startPoint.x = (int) (c.getAlignmentX() * (currentSize.width - targetSize.width));
                startPoint.y = (int) (c.getAlignmentY() * (currentSize.height - targetSize.height));
            }
            
            // 创建新的缓存图像
            synchronized (scaleLock) {
                if (targetSize.width > 0 && targetSize.height > 0) {
                    scaledInstance = createScaledImage(getImage(), targetSize);
                    lastSize = currentSize;
                    lastComponent = new WeakReference<>(c);
                }
            }
        }
        
        // 绘制缓存的图像
        if (scaledInstance != null) {
            Graphics2D g2d = (Graphics2D) g.create();
            try {
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, 
                                   RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, 
                                   RenderingHints.VALUE_RENDER_QUALITY);
                                   
                int drawX = constrained ? (currentSize.width - scaledInstance.getWidth()) / 2 : x;
                int drawY = constrained ? (currentSize.height - scaledInstance.getHeight()) / 2 : y;
                
                g2d.drawImage(scaledInstance, drawX, drawY, null);
            } finally {
                g2d.dispose();
            }
        }
    }
    
    private void clearOldScale() {
        if (scaledInstance != null) {
            scaledInstance.flush();
            scaledInstance = null;
        }
//        System.gc(); // 在开发测试时可以使用，生产环境建议移除
    }
    
    private BufferedImage createScaledImage(Image img, Dimension size) {
        BufferedImage scaledImage = new BufferedImage(size.width, size.height, 
                                                    BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();
        try {
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                               RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(img, 0, 0, size.width, size.height, null);
        } finally {
            g2d.dispose();
        }
        return scaledImage;
    }
    
    @Override
    protected void finalize() throws Throwable {
        clearOldScale();
        super.finalize();
    }
}