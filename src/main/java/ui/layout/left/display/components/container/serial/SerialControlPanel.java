package ui.layout.left.display.components.container.serial;

import common.constant.DeviceConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.base.operation.OperationTarget;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.MessageText;
import sdk.domain.serial.SerialJudgeParameter;
import sdk.domain.serial.WaitFilterParameter;
import sdk.entity.SerialDevice;
import ui.base.BaseView;
import ui.base.PlaceholderTextField;
import ui.config.json.devices.serial.SerialConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.model.MainModel;
import ui.model.device.DeviceSendDataObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * 串口控制面板类，继承自DeviceControlPanel，实现BaseView和DeviceSendDataObserver接口
 * 提供串口设备的控制界面，包括日志设置、过程检测等功能
 */
@Slf4j
public class SerialControlPanel extends DeviceControlPanel implements BaseView, DeviceSendDataObserver {
    // 各种UI组件声明
    private final JCheckBox isHexCheckBox;
    private final JCheckBox autoScrollCheckBox;
    private final JCheckBox autoLogCheckBox;
    private final JButton showLogButton;  // 显示日志按钮
    private final PlaceholderTextField waitFilterTextField;
    private final JSpinner waitFilterTimeoutSpinner;
    private final JComboBox<String> judgeComboBox;
    private final JSpinner judgeTimeoutSpinner;
    private final JTextField judgeTextField;
    private final JTextField mustExistTextField;
    private final JTextField forbidExistTextField;
    private final JButton waitFilterAddToScriptButton;
    private final JButton mustExistAddToScriptButton;
    private final JButton forbidExistAddToScriptButton;
    private final JButton judgeAddToScriptButton;

    private final JComboBox<String> logSaveTypeCombo; // 日志保存方式下拉框
    private final JTextField logMaxFileSizeField; // 日志文件最大大小输入框
    private final JLabel logMaxFileSizeLabel; // 日志文件最大大小标签
    private final JComboBox<String> logSaveTimePeriodCombo; // 日志保存时间间隔下拉框

    private final SerialConfig serialConfig;
    private final SerialDevice serialDevice;
    private final SendAndMatchPanel sendAndMatchPanel;
    private final MainModel mainModel;
    private final ClientView clientView;

    public SerialControlPanel(DeviceContainer deviceContainer, MainModel mainModel, SerialConfig serialConfig, ClientView clientView) {
        super(deviceContainer, mainModel);
        this.mainModel = mainModel;
        this.clientView = clientView;
        this.serialConfig = serialConfig;
        sendAndMatchPanel = new SendAndMatchPanel(this);
        serialDevice = (SerialDevice) deviceContainer.getDevice();
        isHexCheckBox = new JCheckBox("十六进制发送/接收");
        autoScrollCheckBox = new JCheckBox("自动滚动");
        autoScrollCheckBox.setSelected(true);
        autoLogCheckBox = new JCheckBox("自动保存日志");
        showLogButton = new JButton("打开日志文件夹");

        logSaveTypeCombo = new JComboBox<>(new String[]{"按时间保存", "按大小保存", "按脚本保存"});
        logMaxFileSizeField = new JTextField(5);
        logMaxFileSizeLabel = new JLabel("MB");
        logSaveTimePeriodCombo = new JComboBox<>(new String[]{"天", "小时"});

        waitFilterTextField = new PlaceholderTextField("输入等待过滤文字...");
        mustExistTextField = new JTextField();
        waitFilterAddToScriptButton = addNewScriptButton();
        mustExistAddToScriptButton = addNewScriptButton();
        forbidExistTextField = new JTextField();
        forbidExistAddToScriptButton = addNewScriptButton();
        waitFilterTimeoutSpinner = new JSpinner(new SpinnerNumberModel(1, 1, Integer.MAX_VALUE, 1));
        judgeComboBox = new JComboBox<>(new String[]{"=", ">", "<", ">=", "<="});
        judgeTimeoutSpinner = new JSpinner(new SpinnerNumberModel(50, 1, Integer.MAX_VALUE, 1));
        judgeTextField = new JTextField();
        judgeAddToScriptButton = addNewScriptButton();
        createView();
        createActions();
        restoreView();
        registerModelObservers();
    }


    /**
     * 注册模型观察者
     */
    @Override
    public void registerModelObservers() {
        getMainModel().getDeviceSendDataModel().registerObserver(this);
    }

    /**
     * 发送消息到串口设备
     * @param messageText 要发送的消息文本
     */
    @Override
    public void sendMessage(MessageText messageText) {
        serialDevice.send(messageText);
    }

    /**
     * 检查是否使用十六进制模式
     * @return true表示使用十六进制，false表示不使用
     */
    public boolean isHex() {
        return isHexCheckBox.isSelected();
    }


    /**
     * 检查是否启用自动滚动
     * @return true表示启用自动滚动，false表示不启用
     */
    public boolean isAutoScroll() {
        return autoScrollCheckBox.isSelected();
    }


    /**
     * 控制显示状态
     * @param isDeviceConnected 设备是否已连接
     */
    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        sendAndMatchPanel.controlDisplay(isDeviceConnected);
    }

    /**
     * 创建视图布局
     */
    @Override
    public void createView() {
        setLayout(new GridLayout(1, 1));
        Box mainBox = Box.createVerticalBox();

        JPanel procedureCheckPanel = new JPanel(new GridLayout(4, 1));
        Box waitFilterBox = Box.createHorizontalBox();
        waitFilterBox.add(new JLabel("等待出现:"));
        waitFilterBox.add(waitFilterTextField);
        waitFilterBox.add(new JLabel("等该超时(秒):"));
        waitFilterBox.add(waitFilterTimeoutSpinner);
        waitFilterBox.add(waitFilterAddToScriptButton);

        Box mustAppearBox = Box.createHorizontalBox();
        mustAppearBox.add(new JLabel("必须出现:"));
        mustAppearBox.add(mustExistTextField);
        mustAppearBox.add(mustExistAddToScriptButton);

        Box forbidAppearBox = Box.createHorizontalBox();
        forbidAppearBox.add(new JLabel("禁止出现:"));
        forbidAppearBox.add(forbidExistTextField);
        forbidAppearBox.add(forbidExistAddToScriptButton);

        Box compareAppearBox = Box.createHorizontalBox();
        compareAppearBox.add(new JLabel("比较结果:"));
        compareAppearBox.add(judgeComboBox);
        compareAppearBox.add(judgeTextField);
        compareAppearBox.add(new JLabel("等待超时(秒):"));
        compareAppearBox.add(judgeTimeoutSpinner);
        compareAppearBox.add(judgeAddToScriptButton);

        procedureCheckPanel.setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), "过程检测", TitledBorder.LEADING, TitledBorder.TOP, null, null));

        procedureCheckPanel.add(waitFilterBox);
        procedureCheckPanel.add(mustAppearBox);
        procedureCheckPanel.add(forbidAppearBox);
        procedureCheckPanel.add(compareAppearBox);

        Box box1 = Box.createHorizontalBox();

        // 创建并配置日志控制面板 包含各种日志相关的控制组件
        logSaveTypeCombo.setSelectedIndex(0);
        JPanel logPanel = new JPanel();
        logPanel.add(isHexCheckBox); // 十六进制显示复选框
        logPanel.add(autoScrollCheckBox); // 自动滚动复选框
        logPanel.add(autoLogCheckBox); // 自动记录复选框
        logPanel.add(showLogButton);    // 显示日志路径按钮
        logPanel.add(new JLabel("保存日志方式:"));
        logPanel.add(logSaveTypeCombo); // 自动保存日志方式标签和下拉框
        logPanel.add(logMaxFileSizeField); // 日志文件最大大小输入框
        logPanel.add(logMaxFileSizeLabel); // 标签
        logPanel.add(logSaveTimePeriodCombo); // 日志保存时间间隔下拉框

        box1.add(logPanel);



        mainBox.add(box1);
        mainBox.add(procedureCheckPanel);
        mainBox.add(sendAndMatchPanel);
        add(mainBox);
    }

    /**
     * 恢复视图状态
     */
    @Override
    public void restoreView() {
        isHexCheckBox.setSelected(serialConfig.isSendOrReceiveWithHex());
        autoLogCheckBox.setSelected(serialConfig.isSaveLogFlag());

        logSaveTypeCombo.setSelectedIndex(serialConfig.getLogSaveType());
        if (serialConfig.getLogSaveTimePeriod() != null) {
            logSaveTimePeriodCombo.setSelectedItem(serialConfig.getLogSaveTimePeriod().equals("hour") ? "小时" : "天");
        }
        if (serialConfig.getLogMaxFileSize() != null) {
            if (serialConfig.getLogMaxFileSize().endsWith("MB")) {
                logMaxFileSizeField.setText(serialConfig.getLogMaxFileSize().substring(0, serialConfig.getLogMaxFileSize().length() - 2));
            }
        }

        updateLogMaxFileSizeVisibility();
        updateLogSaveTimePeriodVisibility();
    }

    /**
     * 创建事件监听
     */
    @Override
    public void createActions() {
        isHexCheckBox.addItemListener(e -> {
            serialConfig.setSendOrReceiveWithHex(isHexCheckBox.isSelected());
            serialDevice.updateConfig(serialConfig);
            sendAndMatchPanel.changeDocumentFilter(isHexCheckBox.isSelected());
        });
        mustExistAddToScriptButton.addActionListener(e -> mustExistAddToScript());
        forbidExistAddToScriptButton.addActionListener(e -> forbidExistAddToScript());
        // 自动保存日志
        autoLogCheckBox.addActionListener(e -> {
            // 处理保存日志配置的变更
            handleAutoLogConfig();
        });

        // 显示日志按钮的点击事件
        showLogButton.addActionListener(e -> {
            // 展示当前日志路径
            showCurrentLogPath();
        });
        waitFilterAddToScriptButton.addActionListener(e -> waitFilter(waitFilterTextField.getText(), (Integer) waitFilterTimeoutSpinner.getValue()));
        judgeAddToScriptButton.addActionListener(e -> judgeText());

        logSaveTypeCombo.addActionListener(e -> {
            int selectedIndex = logSaveTypeCombo.getSelectedIndex();
            logSaveTimePeriodCombo.setVisible(selectedIndex != 2);
            updateLogMaxFileSizeVisibility();
            updateLogSaveTimePeriodVisibility();
            serialConfig.setLogSaveType(selectedIndex);
            serialDevice.updateConfig(serialConfig);
        });
        logSaveTimePeriodCombo.addActionListener(e -> {
            String selectedPeriod = Objects.requireNonNull(logSaveTimePeriodCombo.getSelectedItem()).toString().equals("小时") ? "hour" : "day";
            serialConfig.setLogSaveTimePeriod(selectedPeriod);
            serialDevice.updateConfig(serialConfig);
        });

        logMaxFileSizeField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                try {
                    int size = Integer.parseInt(logMaxFileSizeField.getText());
                    serialConfig.setLogMaxFileSize(size + "MB");
                    serialDevice.updateConfig(serialConfig);
                } catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(clientView, "请输入有效的文件大小。", "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        });

    }

    /**
     * 展示当前日志路径
     */
    private void showCurrentLogPath() {
        // 此处编写点击按钮后的逻辑
        String customLogPath = serialConfig.getCustomLogPath();
        if (StringUtils.isBlank(customLogPath)) {
            // 获取默认路径
            customLogPath = getDefaultLogPath();
        }

        // 打开目录
        try {
            File logDir = new File(customLogPath);
            if (logDir.exists()) {
                Desktop.getDesktop().open(logDir);  // 使用系统默认文件管理器打开目录
            } else {
                log.info("日志目录不存在: {}", customLogPath);
            }
        } catch (IOException e) {
            log.error("打开日志目录失败: {}", customLogPath);
        }
    }

    /**
     * 处理保存日志配置的变更
     */
    private void handleAutoLogConfig() {
        // 勾选状态进入方法
        if (autoLogCheckBox.isSelected()) {
            // 弹框询问是否使用默认路径 自定义对话框选项
            Object[] options = {"是", "自定义"};
            int option = JOptionPane.showOptionDialog(
                    null,
                    "是否使用默认日志路径?",
                    "路径选择",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.QUESTION_MESSAGE,
                    null,
                    options,
                    options[0]
            );
            // 根据用户选择执行相应操作
            if (option == JOptionPane.YES_OPTION) {
                // 默认路劲
                serialConfig.setCustomLogPath(getDefaultLogPath());
            } else {
                // 用户选择路径 未选择不执行下一步
                if (showLogPathSelectionDialog()) {
                    return;
                }
            }
        }
        // 更新复选框状态到配置
        serialConfig.setSaveLogFlag(autoLogCheckBox.isSelected());

        // 将最终配置应用到设备
        serialDevice.updateConfig(serialConfig);
    }

    /**
     * 用户选择路径 未选择不执行下一步
     */
    private boolean showLogPathSelectionDialog() {
        // 弹出文件选择窗口让用户选择路径
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("选择日志保存目录");
        fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        fileChooser.setAcceptAllFileFilterUsed(false);

        int returnVal = fileChooser.showOpenDialog(SwingUtilities.getWindowAncestor(this));
        if (returnVal == JFileChooser.APPROVE_OPTION) {
            String selectedPath = fileChooser.getSelectedFile().getAbsolutePath();
            serialConfig.setCustomLogPath(selectedPath);
        } else {
            // 用户取消选择，不启用自动保存
            autoLogCheckBox.setSelected(false);
            return true;
        }
        return false;
    }

    /**
     * 获取默认路劲
     */
    private String getDefaultLogPath() {
        return String.format("%s\\%s\\database\\fileDB\\serialLogs",
                DeviceConstants.PROJECT_PATH,
                mainModel.getAppInfo().getProject());
    }

    /**
     * 添加文本比较操作到脚本
     */
    private void judgeText() {
        String text = judgeTextField.getText();
        if (StringUtils.isBlank(text)) {
            SwingUtil.showWarningDialog(this, "检测文字不能为空");
            return;
        }
        Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
        operation.setOperationMethod(DeviceMethods.judgeText);
        SerialJudgeParameter serialJudgeParameter = new SerialJudgeParameter();
        serialJudgeParameter.setOperator(judgeComboBox.getSelectedIndex());
        serialJudgeParameter.setContent(text);
        serialJudgeParameter.setTimeout((Integer) judgeTimeoutSpinner.getValue());
        operation.setOperationObject(serialJudgeParameter);
        getMainModel().getOperationModel().updateOperation(operation);
    }

    /**
     * 添加等待过滤操作到脚本
     * @param text 要过滤的文本
     * @param timeout 超时时间(秒)
     */
    private void waitFilter(String text, int timeout) {
        Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
        operation.setOperationMethod(DeviceMethods.waitFilter);
        WaitFilterParameter waitFilterParameter = new WaitFilterParameter();
        waitFilterParameter.setText(text);
        waitFilterParameter.setTimeout(timeout);
        operation.setOperationObject(waitFilterParameter);
        getMainModel().getOperationModel().updateOperation(operation);
    }

    /**
     * 添加必须存在检测到脚本
     */
    private void mustExistAddToScript() {
        Operation operation = Operation.buildOperation(getDeviceContainer().getDevice());
        OperationMethod operationMethod = null;
        String text = mustExistTextField.getText().trim();
        if (!StringUtils.isBlank(text)) {
            operationMethod = DeviceMethods.mustExistMonitorStart;
        } else {
            SwingUtil.showWarningDialog(this, "检测文字不能为空");
        }
        operation.setOperationObject(text);
        operation.setOperationMethod(operationMethod);
        getMainModel().getOperationModel().updatePairedOperation(operation);
    }

    /**
     * 更新日志文件大小限制相关控件的可见性
     */
    private void forbidExistAddToScript() {
        Operation operation = new Operation();
        OperationTarget operationTarget = OperationTarget.ofDevice(getDeviceContainer().getDevice());
        operation.setOperationTarget(operationTarget);
        OperationMethod operationMethod = null;
        String text = forbidExistTextField.getText().trim();
        if (!StringUtils.isBlank(text)) {
            operationMethod = DeviceMethods.forbidExistMonitorStart;
        } else {
            SwingUtil.showWarningDialog(this, "检测文字不能为空");
        }
        operation.setOperationObject(text);
        operation.setOperationMethod(operationMethod);
        getMainModel().getOperationModel().updatePairedOperation(operation);
    }

    /**
     * 根据日志保存类型更新文件大小限制相关控件的可见性
     * 当选择"按大小保存"(下拉框选中索引为1)时显示文件大小输入框和标签
     * 其他情况隐藏这些控件
     */
    private void updateLogMaxFileSizeVisibility() {
        boolean isSizeBased = logSaveTypeCombo.getSelectedIndex() == 1;
        logMaxFileSizeField.setVisible(isSizeBased);
        logMaxFileSizeLabel.setVisible(isSizeBased);
    }

    /**
     * 根据日志保存类型更新时间周期下拉框的可见性
     * 当选择"按时间保存"(下拉框选中索引为0)时显示时间周期选择框
     * 其他情况隐藏该控件
     */
    private void updateLogSaveTimePeriodVisibility() {
        boolean isTimeBased = logSaveTypeCombo.getSelectedIndex() == 0;
        logSaveTimePeriodCombo.setVisible(isTimeBased);
    }
}
