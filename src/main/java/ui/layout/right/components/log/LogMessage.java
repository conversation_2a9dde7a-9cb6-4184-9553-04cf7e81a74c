package ui.layout.right.components.log;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class LogMessage {

    private String level; //日志等级
    private String message; //日志消息

    public static LogMessage info(String message) {
        return new LogMessage("info", message);
    }

    public static LogMessage error(String errorMessage) {
        return new LogMessage("error", errorMessage);
    }
}
