package ui.layout.left.display.components.container.power.kikusui.pulseSequence;

import ui.base.BaseView;
import ui.config.xml.device.BuiltinWaveConfig;
import ui.config.xml.device.KikusuiDeviceConfig;
import ui.config.xml.device.WaveConfig;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 创建或修改波形序列对话框
 */
public class CreateOrChangePulseSequenceDialog extends JDialog implements BaseView {

    public interface PulseSequenceOperation {
        void selectWavePulse(WaveConfig waveConfig);

    }

    private static class SequenceListNotSelectedException extends Exception {

    }


    private final JButton selectBuiltinWavePulseButton; //选择内置脉冲序列
    private final JButton selectUserWavePulseButton; //选择用户脉冲序列
    private final JButton createWavePulseButton; //创建脉冲序列
    private final JButton amendWavePulseButton; //修改脉冲系列
    private final JButton deleteWavePulseButton; //删除脉冲序列
    private final JList<String> userSequenceNameList; //序列名称列表
    private final DefaultListModel<String> userListModel;

    private final JList<String> builtinSequenceNameList; //内置序列名称列表

    private final DefaultListModel<String> builtinListModel;
    private final Font font = new Font(null, Font.PLAIN, 30);
    private final PulseSequenceOperation pulseSequenceOperation;

    private final KikusuiDeviceConfig kikusuiDeviceConfig;


    public CreateOrChangePulseSequenceDialog(JComponent component, String title,
                                             KikusuiDeviceConfig kikusuiDeviceConfig, PulseSequenceOperation pulseSequenceOperation) {
        this.pulseSequenceOperation = pulseSequenceOperation;
        this.kikusuiDeviceConfig = kikusuiDeviceConfig;
        setTitle(title);
        userListModel = new DefaultListModel<>();
        userSequenceNameList = new JList<>(userListModel);
        selectUserWavePulseButton = new JButton("选择脉冲序列");
        createWavePulseButton = new JButton("创建脉冲序列");
        amendWavePulseButton = new JButton("修改脉冲序列");
        deleteWavePulseButton = new JButton("删除脉冲序列");

        builtinListModel = new DefaultListModel<>();
        builtinSequenceNameList = new JList<>(builtinListModel);
        selectBuiltinWavePulseButton = new JButton("选择脉冲序列");
        setModal(true);
        createView();
        createActions();
        restoreView();
    }

    public void showDialog() {
        setVisible(true);
    }

    private JPanel getUserPanel() {
        JPanel userPanel = new JPanel();
        userPanel.setLayout(new BorderLayout());

        userSequenceNameList.setFixedCellHeight(50);
        userSequenceNameList.setBorder(BorderFactory.createTitledBorder("用户定义脉冲序列"));

        JPanel panelLeft = new JPanel(new BorderLayout());
        JPanel panelRight = new JPanel(new GridLayout(4, 1, 0, 5));
        panelLeft.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        panelRight.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        panelLeft.add(userSequenceNameList);

        JScrollPane scrollPane = new JScrollPane();
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setViewportView(panelLeft);

        panelRight.add(selectUserWavePulseButton);
        panelRight.add(createWavePulseButton);
        panelRight.add(amendWavePulseButton);
        panelRight.add(deleteWavePulseButton);
        selectUserWavePulseButton.setFont(font);
        createWavePulseButton.setFont(font);
        amendWavePulseButton.setFont(font);
        deleteWavePulseButton.setFont(font);

        Box hBox = Box.createHorizontalBox();
        hBox.add(scrollPane);
        hBox.add(panelRight);
        userPanel.add(hBox, BorderLayout.CENTER);
        return userPanel;
    }

    private JPanel getBuiltinPanel() {
        JPanel builtinPanel = new JPanel(new BorderLayout());

        JPanel panelLeft = new JPanel(new BorderLayout());
        panelLeft.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        builtinSequenceNameList.setFixedCellHeight(50);
        builtinSequenceNameList.setBorder(BorderFactory.createTitledBorder("系统内置脉冲序列"));
        panelLeft.add(builtinSequenceNameList);
        JScrollPane scrollPane = new JScrollPane();
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setViewportView(panelLeft);

        JPanel panelRight = new JPanel(new GridLayout(1, 1, 0, 5));
        panelRight.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        selectBuiltinWavePulseButton.setFont(font);
        panelRight.add(selectBuiltinWavePulseButton);

        builtinPanel.add(panelLeft,BorderLayout.CENTER);
        builtinPanel.add(panelRight,BorderLayout.SOUTH);
        return builtinPanel;
    }

    @Override
    public void createView() {
        JTabbedPane tabbedPane = new JTabbedPane();
        tabbedPane.setPreferredSize(new Dimension(1000, 650));
        tabbedPane.add("用户定义脉冲序列", getUserPanel());
        tabbedPane.add("系统内置脉冲序列", getBuiltinPanel());
        add(tabbedPane);
        SwingUtil.centerInScreen(this);
    }

    @Override
    public void restoreView() {
        String[] sequenceNames = kikusuiDeviceConfig.getUserWavePulseSequenceNames();
        for (String sequenceName : sequenceNames) {
            userListModel.addElement(sequenceName);
        }
        List<String> builtinNames = BuiltinWaveConfig.allWaveConfigs.stream().map(WaveConfig::getFriendlyName).collect(Collectors.toList());
        for (String name : builtinNames) {
            builtinListModel.addElement(name);
        }
    }

    public List<String> getConfigNames() {
        return Arrays.stream(userListModel.toArray()).map(o -> (String) o).collect(Collectors.toList());
    }

    private int getListSelected() throws SequenceListNotSelectedException {
        int selectedListIndex = userSequenceNameList.getSelectedIndex();
        if (selectedListIndex == -1) {
            SwingUtil.showWarningDialog(this, "请选择一个脉冲序列");
            throw new SequenceListNotSelectedException();
        }
        return selectedListIndex;
    }


    /**
     * 选择用户定义波形脉冲系列
     */
    private void selectUserWavePulseSequence() {
        try {
            int index = getListSelected();
            String sequenceName = userListModel.elementAt(index);
            WaveConfig waveConfig = kikusuiDeviceConfig.getWavePulseSequenceOfUser(sequenceName);
            pulseSequenceOperation.selectWavePulse(waveConfig);
            dispose();
        } catch (SequenceListNotSelectedException ignored) {

        }
    }

    /**
     * 选择系统内置波形脉冲系列
     */
    private void selectBuiltinWavePulseSequence() {
        int selectedListIndex = builtinSequenceNameList.getSelectedIndex();
        if (selectedListIndex == -1) {
            SwingUtil.showWarningDialog(this, "请选择一个脉冲序列");
            return;
        }
        WaveConfig waveConfig = BuiltinWaveConfig.allWaveConfigs.get(selectedListIndex);
        pulseSequenceOperation.selectWavePulse(waveConfig);
        dispose();
    }

    /**
     * 创建波形脉冲系列
     */
    private void createWavePulseSequence() {
        String sequenceName;
        while (true) {
            sequenceName = JOptionPane.showInputDialog("请输入一个脉冲序列名称（eg.按车厂分类）");
            if (sequenceName == null) {
                return;
            }
            sequenceName = sequenceName.trim();
            if (sequenceName.isEmpty()) {
                SwingUtil.showWarningDialog(this, "脉冲序列名不允许为空");
                continue;
            }
            break;
        }
        if (getConfigNames().contains(sequenceName)) {
            SwingUtil.showWarningDialog(this, String.format("存在相同脉冲序列名称%s，请重新命名", sequenceName));
            return;
        }


        if (PulseSequenceSetupDialog.of(sequenceName, kikusuiDeviceConfig).build().isConfirmed()) {
            userListModel.addElement(sequenceName);
            userSequenceNameList.setSelectedValue(sequenceName, true);
        }
    }

    /**
     * 修改脉冲序列
     */
    private void amendWavePulseSequence() {
        try {
            int index = getListSelected();
            String sequenceName = userListModel.elementAt(index);
            PulseSequenceSetupDialog.of(sequenceName, kikusuiDeviceConfig).loadFromJsonConfig().build();
        } catch (SequenceListNotSelectedException ignored) {

        }
    }

    /**
     * 删除波形脉冲序列
     */
    private void deleteWavePulseSequence() {
        try {
            int index = getListSelected();
            String sequenceName = userListModel.elementAt(index);
            int answer = JOptionPane.showConfirmDialog(this, String.format("确定删除\"%s\"脉冲序列", sequenceName),
                    "删除脉冲序列", JOptionPane.YES_NO_OPTION);
            if (answer == JOptionPane.YES_OPTION) {
                if (kikusuiDeviceConfig.deleteWavePulseSequenceOfUser(sequenceName)) {
                    userListModel.remove(index);
                }
            }
        } catch (SequenceListNotSelectedException ignored) {

        }
    }

    @Override
    public void createActions() {
        selectUserWavePulseButton.addActionListener(e -> selectUserWavePulseSequence());
        createWavePulseButton.addActionListener(e -> createWavePulseSequence());
        amendWavePulseButton.addActionListener(e -> amendWavePulseSequence());
        deleteWavePulseButton.addActionListener(e -> deleteWavePulseSequence());
        selectBuiltinWavePulseButton.addActionListener(e -> selectBuiltinWavePulseSequence());

        builtinSequenceNameList.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if(e.getClickCount() == 2){
                    int clickIndex = builtinSequenceNameList.locationToIndex(e.getPoint());
                    WaveConfig waveConfig = BuiltinWaveConfig.allWaveConfigs.get(clickIndex);
                    pulseSequenceOperation.selectWavePulse(waveConfig);
                    dispose();
                }
            }
        });
    }
}
