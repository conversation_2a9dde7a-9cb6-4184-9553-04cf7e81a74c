package ui.layout.left.display.components.container.picture;

import sdk.base.operation.OperationResult;
import sdk.domain.Device;
import sdk.entity.CameraDevice;
import ui.config.json.devices.camera.BackTrackConfig;
import ui.config.json.devices.camera.CameraConfig;
import ui.config.json.devices.camera.FailVideoConfig;
import ui.config.json.devices.camera.TakePhotoConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;
import ui.model.testScript.TestFailObserver;
import ui.model.testScript.TestPassObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.EtchedBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.io.File;
import java.io.IOException;

/**
 * @author: <PERSON><PERSON>ao
 * @description:
 * @date: 2024/9/29 18:59
 */
public class VideoRecordingContainer extends <PERSON>ceContainer implements TestFailObserver, TestPassObserver {
    private final MainModel mainModel;
    private final Device device;
    private final JSlider backtrackSlider;
    private final JLabel backtrackLabel;
    private final JCheckBox backtrackCheckBox;
    private final JTextField backtrackTextField;
    private final JButton backtrackButton;
    private final JButton backtrackFileButton;
    private final JSlider failSlider;
    private final JLabel failLabel;
    private final JCheckBox failCheckBox;
    private final JSpinner failSpinner;
    private final JSlider failFpsSlider;
    private final JButton failButton;
    private final JButton failFileButton;
    private final JTextField failTextField;
    private final JSpinner backtrackSpinner;
    private final JSlider backtrackFpsSlider;
    private final JCheckBox savePassImageCheckBox;
    private final JTextField imagePathTextField;
    private final JButton imageSavePathButton;
    private final JButton openImagesFolderButton;
    private final JComboBox<String> savePathTypeComboBox;
    private final CameraConfig cameraConfig;


    public VideoRecordingContainer(ClientView clientView, MainModel mainModel, Device device, CameraConfig cameraConfig) {
        super(clientView, mainModel, device);
        this.mainModel = mainModel;
        this.cameraConfig = cameraConfig;
        this.device = device;
        backtrackSlider = new JSlider();
        backtrackLabel = new JLabel("");
        backtrackFpsSlider = SwingUtil.fpsJSlider(1, 120, 30);
        backtrackSpinner = new JSpinner();
        backtrackCheckBox = new JCheckBox("启动捕获");
        backtrackTextField = new JTextField();
        backtrackTextField.setEditable(false);
        backtrackFileButton = new JButton("打开文件夹");
        backtrackButton = new JButton("选择");
        failSlider = new JSlider();
        failLabel = new JLabel("");
        failCheckBox = new JCheckBox("启动失败录像");
        failFpsSlider = SwingUtil.fpsJSlider(1, 120, 30);
        failSpinner = new JSpinner();
        failButton = new JButton("选择");
        failTextField = new JTextField();
        failTextField.setEditable(false);
        failFileButton = new JButton("打开文件夹");
        savePassImageCheckBox = new JCheckBox("保存通过图片");
        imagePathTextField = new JTextField();
        imageSavePathButton = new JButton("选择");
        openImagesFolderButton = new JButton("打开文件夹");
        savePathTypeComboBox = new JComboBox<>(new String[]{"用例保存", "按时间保存"});


        createView();
        loadConfig(cameraConfig);
        mainModel.getTestFailModel().registerObserver(this);
        mainModel.getTestPassModel().registerObserver(this);
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(4, 1, 0, 0));

        JPanel panel = new JPanel();
        panel.setBorder(new TitledBorder(new EtchedBorder(EtchedBorder.LOWERED, new Color(255, 255, 255), new Color(160, 160, 160)), "捕获", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));
        add(panel);
        panel.setLayout(new GridLayout(3, 1, 10, 0));

        JPanel panel_1 = new JPanel();
        FlowLayout flowLayout = (FlowLayout) panel_1.getLayout();
        flowLayout.setAlignment(FlowLayout.LEFT);
        panel.add(panel_1);

        panel_1.add(new JLabel("捕获失败前："));
        backtrackSlider.setValue(8);
        backtrackSlider.setMinimum(1);
        backtrackSlider.setMaximum(20);

        panel_1.add(backtrackSlider);

        backtrackLabel.setText(backtrackSlider.getValue() + "");
        panel_1.add(backtrackLabel);

        panel_1.add(new JLabel("S录像"));
        panel_1.add(new JLabel("|"));

        panel_1.add(backtrackCheckBox);

        JPanel panel_2 = new JPanel();
        FlowLayout flowLayout_1 = (FlowLayout) panel_2.getLayout();
        flowLayout_1.setAlignment(FlowLayout.LEFT);
        panel.add(panel_2);

        panel_2.add(new JLabel("视频帧率："));
        panel_2.add(backtrackFpsSlider);
        backtrackSpinner.setModel(new SpinnerNumberModel(30, 1, 120, 10));
        panel_2.add(backtrackSpinner);
        panel_2.add(new JLabel("FPS  | 注：低帧率为慢动作，高帧率为快动作"));

        JPanel panel_3 = new JPanel();
        FlowLayout flowLayout_2 = (FlowLayout) panel_3.getLayout();
        flowLayout_2.setAlignment(FlowLayout.LEFT);
        panel.add(panel_3);

        panel_3.add(new JLabel("捕获录像保存路径："));


        panel_3.add(backtrackTextField);
        backtrackTextField.setColumns(25);
        panel_3.add(backtrackButton);
        panel_3.add(backtrackFileButton);

        JPanel panel_5 = new JPanel();
        panel_5.setBorder(new TitledBorder(new EtchedBorder(EtchedBorder.LOWERED, new Color(255, 255, 255), new Color(160, 160, 160)), "失败后录像", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));
        add(panel_5);
        panel_5.setLayout(new GridLayout(3, 1, 10, 0));

        JPanel panel_1_1 = new JPanel();
        FlowLayout flowLayout_3 = (FlowLayout) panel_1_1.getLayout();
        flowLayout_3.setAlignment(FlowLayout.LEFT);
        panel_5.add(panel_1_1);

        panel_1_1.add(new JLabel("录制时长："));

        failSlider.setValue(8);
        failSlider.setMinimum(1);
        failSlider.setMaximum(20);
        panel_1_1.add(failSlider);

        failLabel.setText(failSlider.getValue() + "");
        panel_1_1.add(failLabel);

        panel_1_1.add(new JLabel("S"));
        panel_1_1.add(new JLabel("|"));


        panel_1_1.add(failCheckBox);

        JPanel panel_2_1 = new JPanel();
        FlowLayout flowLayout_4 = (FlowLayout) panel_2_1.getLayout();
        flowLayout_4.setAlignment(FlowLayout.LEFT);
        panel_5.add(panel_2_1);

        panel_2_1.add(new JLabel("视频帧率："));

        panel_2_1.add(failFpsSlider);
        failSpinner.setModel(new SpinnerNumberModel(30, 1, 120, 10));
        panel_2_1.add(failSpinner);

        JLabel lblNewLabel_3_1 = new JLabel("FPS| 注：低帧率为慢动作，高帧率为快动作");
        panel_2_1.add(lblNewLabel_3_1);

        JPanel panel_3_1 = new JPanel();
        FlowLayout flowLayout_5 = (FlowLayout) panel_3_1.getLayout();
        flowLayout_5.setAlignment(FlowLayout.LEFT);
        panel_5.add(panel_3_1);

        panel_3_1.add(new JLabel("失败录像保存路径："));

        failTextField.setColumns(25);
        panel_3_1.add(failTextField);
        panel_3_1.add(failButton);
        panel_3_1.add(failFileButton);

        JPanel panel_4 = new JPanel();
        panel_4.setBorder(new TitledBorder(null, "保存图片与录像", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        add(panel_4);
        panel_4.setLayout(new GridLayout(3, 1, 0, 0));

        JPanel panel_6 = new JPanel();
        FlowLayout flowLayout_6 = (FlowLayout) panel_6.getLayout();
        flowLayout_6.setAlignment(FlowLayout.LEFT);
        panel_4.add(panel_6);
        panel_6.add(savePassImageCheckBox);

        JPanel panel_7 = new JPanel();
        FlowLayout flowLayout_7 = (FlowLayout) panel_7.getLayout();
        flowLayout_7.setAlignment(FlowLayout.LEFT);
        panel_4.add(panel_7);
        panel_7.add(new JLabel("保存类型："));
        panel_7.add(savePathTypeComboBox);


        JPanel panel_3_1_1 = new JPanel();
        FlowLayout flowLayout_8 = (FlowLayout) panel_3_1_1.getLayout();
        flowLayout_8.setAlignment(FlowLayout.LEFT);
        panel_4.add(panel_3_1_1);
        panel_3_1_1.add(new JLabel("图片保存路径："));

        imagePathTextField.setColumns(25);
        imagePathTextField.setEditable(false);
        panel_3_1_1.add(imagePathTextField);
        panel_3_1_1.add(imageSavePathButton);
        panel_3_1_1.add(openImagesFolderButton);

        backtrackSlider.addChangeListener(e -> {
            backtrackLabel.setText(backtrackSlider.getValue() + "");
        });
        failSlider.addChangeListener(e -> {
            failLabel.setText(failSlider.getValue() + "");
        });
        backtrackFpsSlider.addChangeListener(e -> {
            backtrackSpinner.setValue(backtrackFpsSlider.getValue());
        });
        failFpsSlider.addChangeListener(e -> {
            failSpinner.setValue(failFpsSlider.getValue());
        });
        backtrackSpinner.addChangeListener(e -> {
            backtrackFpsSlider.setValue((Integer) backtrackSpinner.getValue());
        });
        failSpinner.addChangeListener(e -> {
            failFpsSlider.setValue((Integer) failSpinner.getValue());
        });
        backtrackFileButton.addActionListener(e -> openFolder(backtrackTextField));
        failFileButton.addActionListener(e -> openFolder(failTextField));
        openImagesFolderButton.addActionListener(e -> openFolder(imagePathTextField));

        backtrackButton.addActionListener(e -> selectFolder(backtrackTextField));
        failButton.addActionListener(e -> selectFolder(failTextField));
        imageSavePathButton.addActionListener(e -> selectFolder(imagePathTextField));
        backtrackCheckBox.addActionListener(e -> updateRecordingConfig());
        failCheckBox.addActionListener(e -> updateFailVideoConfig());
        savePassImageCheckBox.addActionListener(e -> updateTakePhotoConfig());

    }


    private void openFolder(JTextField textField) {
        File file = new File(textField.getText());
        if (file.exists()) {
            try {
                Desktop.getDesktop().open(file);
            } catch (IOException ex) {
                SwingUtil.showWarningDialog(this, "文件夹不存在");
            }
        }
    }

    private void selectFolder(JTextField textField) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            textField.setText(fileChooser.getSelectedFile().getAbsolutePath());
            updateConfigBasedOnTextField(textField);
        }
    }

    private void updateConfigBasedOnTextField(JTextField textField) {
        if (textField == backtrackTextField) {
            updateRecordingConfig();
        } else if (textField == failTextField) {
            updateFailVideoConfig();
        } else if (textField == imagePathTextField) {
            updateTakePhotoConfig();
        }
    }

    private void loadConfig(CameraConfig cameraConfig) {
        BackTrackConfig backTrackConfig = cameraConfig.getBackTrackConfig();
        if (backTrackConfig != null) {
            boolean isEnabled = backTrackConfig.isBacktrack();
            backtrackCheckBox.setSelected(isEnabled);
            backtrackSlider.setValue(backTrackConfig.getBacktrackTime());
            backtrackSpinner.setValue(backTrackConfig.getBackResolution());
            backtrackTextField.setText(backTrackConfig.getBackSavePath());
            setComponentsDisabled(isEnabled, backtrackButton, backtrackTextField, backtrackSpinner, backtrackSlider, backtrackFpsSlider);
        }

        FailVideoConfig failVideoConfig = cameraConfig.getFailVideoConfig();
        if (failVideoConfig != null) {
            boolean isEnabled = failVideoConfig.isFailVideo();
            failCheckBox.setSelected(isEnabled);
            failSlider.setValue(failVideoConfig.getFailVideoTime());
            failSpinner.setValue(failVideoConfig.getFailVideoResolution());
            failTextField.setText(failVideoConfig.getFailVideoSavePath());
            setComponentsDisabled(isEnabled, failButton, failTextField, failSpinner, failSlider, failFpsSlider);
        }

        TakePhotoConfig takePhotoConfig = cameraConfig.getTakePhotoConfig();
        if (takePhotoConfig != null) {
            boolean isTakePhotoEnabled = takePhotoConfig.isTakePhoto();
            savePassImageCheckBox.setSelected(isTakePhotoEnabled);
            imagePathTextField.setText(takePhotoConfig.getSavePath());
            savePathTypeComboBox.setSelectedIndex(takePhotoConfig.getType());
            updateTakePhotoUIState(isTakePhotoEnabled);
        }
    }

    private void setComponentsDisabled(boolean enabled, Component... components) {
        for (Component component : components) {
            component.setEnabled(!enabled);
        }
    }

    private void updateTakePhotoUIState(boolean isTakePhotoEnabled) {
        setComponentsDisabled(isTakePhotoEnabled, savePathTypeComboBox, imageSavePathButton);
    }

    private void updateRecordingConfig() {
        if (backtrackTextField.getText().isEmpty()) {
            backtrackCheckBox.setSelected(false);
            SwingUtil.showWarningDialog(this, "请选择保存文件夹");
            return;
        }
        setComponentsDisabled(backtrackCheckBox.isSelected(), backtrackButton, backtrackTextField, backtrackSpinner, backtrackSlider, backtrackFpsSlider);
        cameraConfig.setProject(mainModel.getAppInfo().getProject());
        BackTrackConfig backTrackConfig = BackTrackConfig.builder()
                .backtrackTime(backtrackSlider.getValue())
                .backtrack(backtrackCheckBox.isSelected())
                .backResolution((int) backtrackSpinner.getValue())
                .backSavePath(backtrackTextField.getText())
                .build();
        cameraConfig.setBackTrackConfig(backTrackConfig);
        getDevice().updateConfig(cameraConfig);
    }

    private void updateFailVideoConfig() {
        if (failTextField.getText().isEmpty()) {
            failCheckBox.setSelected(false);
            SwingUtil.showWarningDialog(this, "请选择保存文件夹");

        }
        setComponentsDisabled(failCheckBox.isSelected(), failButton, failTextField, failSpinner, failSlider, failFpsSlider);
        cameraConfig.setProject(mainModel.getAppInfo().getProject());
        FailVideoConfig failVideoConfig = FailVideoConfig.builder()
                .failVideo(failCheckBox.isSelected())
                .failVideoTime(failSlider.getValue())
                .failVideoResolution((int) failSpinner.getValue())
                .failVideoSavePath(failTextField.getText())
                .build();
        cameraConfig.setFailVideoConfig(failVideoConfig);
        getDevice().updateConfig(cameraConfig);
    }

    public void updateTakePhotoConfig() {
        if (imagePathTextField.getText().isEmpty()) {
            savePassImageCheckBox.setSelected(false);
            SwingUtil.showWarningDialog(this, "请选择保存文件夹");
            return;
        }
        savePathTypeComboBox.setEnabled(!savePassImageCheckBox.isSelected());
        imageSavePathButton.setEnabled(!savePassImageCheckBox.isSelected());
        cameraConfig.setProject(mainModel.getAppInfo().getProject());
        TakePhotoConfig takePhotoConfig = TakePhotoConfig.builder()
                .savePath(imagePathTextField.getText())
                .type(savePathTypeComboBox.getSelectedIndex())
                .takePhoto(savePassImageCheckBox.isSelected())
                .build();
        cameraConfig.setTakePhotoConfig(takePhotoConfig);
        getDevice().updateConfig(cameraConfig);
    }

    @Override
    public OperationResult failVideo() {
        if (!failCheckBox.isSelected()) {
            return OperationResult.staticOk();
        } else {
            return executeDeviceOperation(CameraDevice::failVideo);
        }
    }

    @Override
    public OperationResult startBacktrackVideo() {
        if (!backtrackCheckBox.isSelected()) {
            return OperationResult.staticOk();
        } else {
            return executeDeviceOperation(CameraDevice::startBacktrackVideo);
        }
    }

    @Override
    public OperationResult stopBacktrackVideo() {
        if (!backtrackCheckBox.isSelected()) {
            return OperationResult.staticOk();
        } else {
            return executeDeviceOperation(CameraDevice::stopBacktrackVideo);
        }
    }

    @Override
    public OperationResult takePhoto(String caseName) {
        if (!savePassImageCheckBox.isSelected()) {
            return OperationResult.staticOk();
        } else {
            return executeDeviceOperation(cameraDevice -> cameraDevice.screenShot(caseName));
        }
    }

    /**
     * 通用方法：执行设备操作
     *
     * @param operation 操作函数，接受 CameraDevice 并返回 OperationResult
     * @return 操作结果
     */
    private OperationResult executeDeviceOperation(java.util.function.Function<CameraDevice, OperationResult> operation) {
        OperationResult operationResult = new OperationResult();
        if (device == null) {
            return operationResult.fail();
        }
        if (device.isConnected()) {
            try {
                CameraDevice cameraDevice = (CameraDevice) device;
                return operation.apply(cameraDevice);
            } catch (ClassCastException e) {
                return operationResult.fail("设备类型不匹配");
            }
        }
        return operationResult.fail("设备未连接");
    }


    //测试捕获功能使用，测试完成后删除
//
//        JButton backtrackStartButton = new JButton("开始捕获");
//        panel_1.add(backtrackStartButton);
//        backtrackStartButton.addActionListener(e -> {
//            Operation operation = Operation.buildOperation(getDevice());
//            operation.setOperationMethod(DeviceMethods.startBacktrackVideo);
//            executeOperation(operation);
//        });
//        JButton backtrackStopButton = new JButton("停止捕获");
//        panel_1.add(backtrackStopButton);
//        backtrackStopButton.addActionListener(e -> {
//            Operation operation = Operation.buildOperation(getDevice());
//            operation.setOperationMethod(DeviceMethods.stopBacktrackVideo);
//            executeOperation(operation);
//        });


    @Override
    public String getFailVideoPath() {
        return cameraConfig.getFailVideoConfig().getFailVideoSavePath();
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
        backtrackSlider.setEnabled(isDeviceConnected);
        backtrackCheckBox.setEnabled(isDeviceConnected);
        backtrackFpsSlider.setEnabled(isDeviceConnected);
        backtrackSpinner.setEnabled(isDeviceConnected);
        backtrackButton.setEnabled(isDeviceConnected);
        backtrackFileButton.setEnabled(isDeviceConnected);
        failSlider.setEnabled(isDeviceConnected);
        failCheckBox.setEnabled(isDeviceConnected);
        failFpsSlider.setEnabled(isDeviceConnected);
        failSpinner.setEnabled(isDeviceConnected);
        failButton.setEnabled(isDeviceConnected);
        failFileButton.setEnabled(isDeviceConnected);
        if (!isDeviceConnected) {
            failCheckBox.setSelected(false);
            backtrackCheckBox.setSelected(false);
        }
    }
}
