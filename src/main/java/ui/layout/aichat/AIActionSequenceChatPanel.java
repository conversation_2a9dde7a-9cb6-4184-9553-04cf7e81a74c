package ui.layout.aichat;

import com.google.gson.Gson;
import com.google.gson.stream.JsonReader;
import llm.ActionSequenceLlmScriptGenerator;
import llm.LLMResponse;
import lombok.extern.slf4j.Slf4j;
import org.fife.ui.rsyntaxtextarea.RSyntaxTextArea;
import org.fife.ui.rsyntaxtextarea.SyntaxConstants;
import org.fife.ui.rtextarea.RTextScrollPane;
import sdk.base.operation.Operation;
import sdk.domain.TestScriptFileContent;
import ui.model.MainModel;
import ui.model.app.AppObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.text.BadLocationException;
import javax.swing.text.Style;
import javax.swing.text.StyleConstants;
import javax.swing.text.StyledDocument;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.StringReader;

@Slf4j
public class AIActionSequenceChatPanel extends JPanel implements AppObserver {
    private static volatile AIActionSequenceChatPanel instance;
    private final static Gson gson = new Gson();
    private final JTextPane chatArea;
    private final JScrollPane chatScrollPane;
    private final JTextField inputField;
    private final JButton sendButton;
    private final MainModel mainModel;
    private boolean isSending = false;
    private static final String THINKING_MESSAGE = "AI: 思考中...\n";
    private int loadingMessagePosition = -1; // 初始化为 -1

    /**
     * 获取AIActionSequenceChatPanel的单例实例
     *
     * @param mainModel 主数据模型
     * @return AIActionSequenceChatPanel实例
     */
    public static AIActionSequenceChatPanel getInstance(MainModel mainModel) {
        if (instance == null) {
            synchronized (AIActionSequenceChatPanel.class) {
                if (instance == null) {
                    instance = new AIActionSequenceChatPanel(mainModel);
                }
            }
        }
        return instance;
    }

    /**
     * 构造函数：初始化聊天面板的UI组件和事件监听器
     *
     * @param mainModel 主数据模型
     */
    public AIActionSequenceChatPanel(MainModel mainModel) {
        this.mainModel = mainModel;
        setLayout(new BorderLayout());

        chatArea = new JTextPane();
        chatArea.setEditable(false);
        chatArea.setBackground(new Color(240, 240, 240));

        chatScrollPane = new JScrollPane(chatArea);
        chatScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        chatScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);

        inputField = new JTextField();
        sendButton = new JButton("发送");
        sendButton.setBackground(new Color(0, 120, 215));
        sendButton.setForeground(Color.WHITE);

        JPanel inputPanel = new JPanel(new BorderLayout());
        inputPanel.add(inputField, BorderLayout.CENTER);
        inputPanel.add(sendButton, BorderLayout.EAST);

        add(chatScrollPane, BorderLayout.CENTER);
        add(inputPanel, BorderLayout.SOUTH);

        setupListeners();
        showWelcomeMessage(); // 在构造函数中显示欢迎语

        // Add popup menu
        JPopupMenu popupMenu = new JPopupMenu();
        JMenuItem clearMenuItem = new JMenuItem("清空聊天窗口");
        JMenuItem refreshLLMemoryMenuItem = new JMenuItem("清除AI记忆");
        clearMenuItem.addActionListener(e -> clearChat());
        refreshLLMemoryMenuItem.addActionListener(e -> refreshLLMemory());
        popupMenu.add(clearMenuItem);
        popupMenu.add(refreshLLMemoryMenuItem);

        // Add mouse listener for right-click
        chatArea.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseReleased(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    popupMenu.show(e.getComponent(), e.getX(), e.getY());
                }
            }

            @Override
            public void mousePressed(MouseEvent e) {
                if (e.isPopupTrigger()) {
                    popupMenu.show(e.getComponent(), e.getX(), e.getY());
                }
            }
        });
        mainModel.getAppModel().registerObserver(this);
    }

    /**
     * 设置按钮和输入框的事件监听器
     */
    private void setupListeners() {
        sendButton.addActionListener(e -> {
            if (!isSending) {
                sendMessage();
            }
        });
        inputField.addActionListener(e -> {
            if (!isSending) {
                sendMessage();
            }
        });
    }

    /**
     * 发送消息到AI并处理响应
     * 包含发送状态控制和异步处理
     */
    private synchronized void sendMessage() {
        if (isSending) {
            return; // 如果已经在发送中，直接返回
        }
        isSending = true;
        String message = inputField.getText().trim();
        if (!message.isEmpty()) {
            SwingUtil.invokeLater(() -> {
                appendToChat("-----------------------------------------------------------", Color.gray);
                appendToChat("You: " + message, new Color(0, 0, 100));
                // 添加"思考中..."提示
                showThinkingMessage();
                inputField.setText("");
                sendButton.setEnabled(false);
            });

            SwingWorker<LLMResponse, Void> chatSwingWorker = new SwingWorker<LLMResponse, Void>() {
                @Override
                protected LLMResponse doInBackground() throws Exception {
                    ActionSequenceLlmScriptGenerator actionSequenceLlmScriptGenerator = ActionSequenceLlmScriptGenerator.getInstance(mainModel);
                    if (actionSequenceLlmScriptGenerator == null) {
                        LLMResponse llmResponse = new LLMResponse();
                        llmResponse.setResult("测试会话设置出问题了，请联系开发者解决");
                        return llmResponse;
                    }
                    return actionSequenceLlmScriptGenerator.generateSingleActionSequence(message);
                }

                @Override
                protected void done() {
                    try {
                        LLMResponse llmResponse = get();
                        // 移除"思考中..."提示并显示回复
                        removeThinkingMessage();
                        displayReplyMessage(llmResponse.getResult());
                    } catch (Exception e) {
                        removeThinkingMessage();
                        displayReplyMessage(String.format("oops，服务器出小差了:%s", e.getMessage()));
                    } finally {
                        sendButton.setEnabled(true);
                        isSending = false;
                    }
                }
            };
            chatSwingWorker.execute();
        }
    }

    /**
     * 显示欢迎消息
     * 仅在聊天区域为空时显示
     */
    private void showWelcomeMessage() {
        StyledDocument doc = chatArea.getStyledDocument();
        try {
            if (doc.getLength() == 0) { // 仅在聊天区域为空时显示欢迎消息
                String welcomeMessage = getWelcomeMessage();
                Style style = chatArea.addStyle("Welcome Style", null);
                StyleConstants.setForeground(style, Color.BLACK);
                doc.insertString(0, welcomeMessage + "\n", style);
            }
        } catch (BadLocationException e) {
            log.error("Error updating welcome message: {}", e.getMessage(), e);
        }
    }

    /**
     * 清空聊天窗口内容
     * 清空后重新显示欢迎消息
     */
    private void clearChat() {
        StyledDocument doc = chatArea.getStyledDocument();
        try {
            // Remove all content
            doc.remove(0, doc.getLength());
            // 清空后显示欢迎语
            showWelcomeMessage();
        } catch (BadLocationException e) {
            log.error("Error clearing chat: {}", e.getMessage(), e);
        }
    }

    /**
     * 刷新LLM的记忆
     * 重置AI的上下文状态
     */
    private void refreshLLMemory() {
        ActionSequenceLlmScriptGenerator actionSequenceLlmScriptGenerator = ActionSequenceLlmScriptGenerator.getInstance(mainModel);
        if (actionSequenceLlmScriptGenerator != null) {
            actionSequenceLlmScriptGenerator.reset();
        }
    }

    /**
     * 显示AI思考中的提示消息
     */
    private void showThinkingMessage() {
        StyledDocument doc = chatArea.getStyledDocument();
        Style style = chatArea.addStyle("Thinking Style", null);
        StyleConstants.setForeground(style, new Color(128, 128, 128));
        StyleConstants.setItalic(style, true);
        try {
            loadingMessagePosition = doc.getLength(); // 记录插入位置
            doc.insertString(loadingMessagePosition, THINKING_MESSAGE, style);
        } catch (BadLocationException e) {
            log.error(e.getMessage(), e);
        }
        scrollToBottom();
    }

    /**
     * 移除AI思考中的提示消息
     */
    private void removeThinkingMessage() {
        StyledDocument doc = chatArea.getStyledDocument();
        try {
            int start = loadingMessagePosition;
            int length = THINKING_MESSAGE.length();
            if (start >= 0 && start + length <= doc.getLength()) {
                doc.remove(start, length);
            }
            loadingMessagePosition = -1; // 重置位置
        } catch (BadLocationException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 向聊天区域添加新的文本消息
     * @param text 要添加的文本内容
     * @param color 文本颜色
     */
    private void appendToChat(String text, Color color) {
        StyledDocument doc = chatArea.getStyledDocument();
        Style style = chatArea.addStyle("User Style", null);
        StyleConstants.setForeground(style, color);
        StyleConstants.setBold(style, true);
        try {
            doc.insertString(doc.getLength(), text + "\n", style);
        } catch (BadLocationException e) {
            log.error(e.getMessage(), e);
        }
        scrollToBottom();
    }

    /**
     * 获取欢迎消息文本
     *
     * @return 欢迎消息字符串
     */
    private String getWelcomeMessage() {
        return "您好！我是IC_ST_PTV AT自主研发的智能助手，很高兴为您提供动作序列编写服务，让我们一起开始吧";
    }

    /**
     * 插入动作序列代码
     * 解析JSON格式的代码并更新操作模型
     * @param code 要插入的代码字符串
     */
    private void insertCode(String code) {
        try {
            String jsonContent = code.replaceFirst("json\n", "");
            // 创建JsonReader并启用宽松模式，支持注释
            JsonReader reader = new JsonReader(new StringReader(jsonContent));
            reader.setLenient(true);

            // 解析JSON并转换为Java对象
            TestScriptFileContent testScriptFileContent = gson.fromJson(reader, TestScriptFileContent.class);
            for (Operation operation : testScriptFileContent.getOperationList()) {
                mainModel.getOperationModel().updateOperation(operation);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            SwingUtil.showWarningDialog(null, e.getMessage());
        }
    }

    /**
     * 显示AI回复消息
     * 支持文本和代码块的混合显示
     * @param reply AI的回复内容
     */
    private void displayReplyMessage(String reply) {
        System.out.println(reply);
        SwingUtil.invokeLater(() -> {
            int position = 0;
            int length = reply.length();
            boolean isFirstMessage = true; // Flag to check if it's the first message

            while (position < length) {
                int codeStart = reply.indexOf("```", position);
                if (codeStart == -1) {
                    // No more code blocks, append the rest as text
                    String text = reply.substring(position).trim();
                    if (!text.isEmpty()) {
                        if (isFirstMessage) {
                            appendToChat("AI: " + text, new Color(0, 100, 0));
                        } else {
                            appendToChat(text, new Color(0, 100, 0));
                        }
                    }
                    break;
                } else {
                    // Append text before code block
                    String text = reply.substring(position, codeStart).trim();
                    if (!text.isEmpty()) {
                        if (isFirstMessage) {
                            appendToChat("AI: " + text, new Color(0, 100, 0));
                            isFirstMessage = false;
                        } else {
                            appendToChat(text, new Color(0, 100, 0));
                        }
                    }
                    // Find end of code block
                    int codeEnd = reply.indexOf("```", codeStart + 3);
                    if (codeEnd == -1) {
                        // No matching closing ```, append the rest as code
                        String code = reply.substring(codeStart + 3).trim();
                        insertCodePanel(code);
                        break;
                    } else {
                        // Extract code block
                        String code = reply.substring(codeStart + 3, codeEnd).trim();
                        insertCodePanel(code);
                        // Update position
                        position = codeEnd + 3;
                    }
                }
            }
        });
    }

    /**
     * 滚动聊天区域到底部
     */
    private void scrollToBottom() {
        SwingUtilities.invokeLater(() -> {
            JScrollBar vertical = chatScrollPane.getVerticalScrollBar();
            vertical.setValue(vertical.getMaximum());
        });
    }

    /**
     * 插入代码面板
     * 创建可编辑的代码显示区域，包含复制和插入功能
     * @param code 要显示的代码内容
     */
    private void insertCodePanel(String code) {
        // 计算代码行数
        String[] codeLines = code.split("\n");
        int numLines = codeLines.length;

        // 设置最小和最大行数
        int minRows = 5;
        int maxRows = 20;
        int rows = Math.min(Math.max(numLines, minRows), maxRows);

        // 创建具有动态行数的RSyntaxTextArea
        RSyntaxTextArea codeArea = new RSyntaxTextArea(rows, 60);
        codeArea.setSyntaxEditingStyle(SyntaxConstants.SYNTAX_STYLE_JAVA);
        codeArea.setCodeFoldingEnabled(true);
        codeArea.setText(code);
        codeArea.setEditable(true); // Made codeArea editable as per requirement

        RTextScrollPane codeScrollPane = new RTextScrollPane(codeArea);

        JPanel codePanel = new JPanel(new BorderLayout());
        codePanel.add(codeScrollPane, BorderLayout.CENTER);

        // Adjusted spacing
        codePanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton insertButton = new JButton("插入");
        JButton copyButton = new JButton("复制");

        buttonPanel.add(insertButton);
        buttonPanel.add(copyButton);
        codePanel.add(buttonPanel, BorderLayout.NORTH);

        try {
            StyledDocument doc = chatArea.getStyledDocument();
            // Insert code panel into the chat area
            chatArea.setCaretPosition(doc.getLength());
            chatArea.insertComponent(codePanel);
            doc.insertString(doc.getLength(), "\n", null); // Added extra newlines for spacing
        } catch (BadLocationException e) {
            log.error(e.getMessage(), e);
        }
        scrollToBottom();

        copyButton.addActionListener(e -> {
            StringSelection stringSelection = new StringSelection(codeArea.getText());
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            clipboard.setContents(stringSelection, null);
            JOptionPane.showMessageDialog(this, "Code copied to clipboard!", "Copy Successful", JOptionPane.INFORMATION_MESSAGE);
        });

        insertButton.addActionListener(e -> insertCode(codeArea.getText()));
    }

    /**
     * 主方法：用于测试聊天面板
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("AI Chat Panel");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(300, 400);

            AIActionSequenceChatPanel chatPanel = new AIActionSequenceChatPanel(new MainModel());
            frame.add(chatPanel);

            frame.setVisible(true);
        });
    }
}
