package sdk.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;

@EqualsAndHashCode(callSuper = true)
@Data
public class MessageText extends OperationObject {

    private boolean hex;

    @JsonAlias(value = "text")
    private String sendText;

    private String matchText;
    private float receiveTimeout;

    private int timeoutRetry;

    @Override
    public String getFriendlyString() {
        return String.format("%s%s", sendText, hex ? "(十六进制)" : "");
    }

}
