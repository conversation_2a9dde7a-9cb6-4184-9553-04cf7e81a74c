package sdk.entity;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.BaseHttpClient;
import sdk.base.operation.SmokingTestKit;
import sdk.base.operation.TestSuiteKit;
import sdk.constants.UrlConstants;
import sdk.websocket.WebSocketDataMonitor;

import java.io.IOException;

/**
 * 操作目标单例管理器
 */
@Getter
@Slf4j
public class OperationTargetHolder extends BaseHttpClient {
    @Getter
    private static final PowerDevice powerDeviceManager;
    //    @Getter
//    private static final ElectricRelayDevice electricRelayDeviceManager;
    @Getter
    private static final CameraDevice cameraDeviceManager;
    @Getter
    private static final QnxDevice qnxDevice = new QnxDevice();
    @Getter
    private static final AndroidDevice androidDeviceManager;
    @Getter
    private static final AdbHudDevice adbHudDeviceManager;
    @Getter
    private static final SerialDevice serialDeviceManager;
    @Getter
    private static final UsbSwitchDevice usbSwitchDeviceManager;
    @Getter
    private static final AutoClickerDevice autoClickerDeviceManager;
    @Getter
    private static final UsbPlugDevice usbPlugDeviceManager;
    @Getter
    private static final TestBoxDevice testBoxDeviceManager;
    //    @Getter
//    private static final LightTestBoxDevice lightTestBoxDeviceManager;
    @Getter
    private static final I2CDevice i2cDeviceManager;
    @Getter
    private static final ResistanceDevice resistanceManager;
    @Getter
    private static final RemoteResistanceDevice remoteResistanceManager;
    @Getter
    private static final ResistanceQRDevice resistanceQRDeviceManager;
    @Getter
    private static final TcpServerDevice tcpServerDeviceManager;
    @Getter
    private static final VideoCaptureDevice videoCaptureDeviceManager;

    @Getter
    private static final TestClientKit testClientKit;
    @Getter
    private static final TestProjectKit testProjectKit;
    @Getter
    private static final TestScriptKit testScriptKit;
    @Getter
    private static final ExecutorKit executorKit;
    @Getter
    private static final ExcelKit excelKit;
    @Getter
    private static final OperationGroupKit operationGroupKit;
    @Getter
    private static final ScreenKit screenKit;
    @Getter
    private static final ActionSequenceKit actionSequenceKit;
    @Getter
    private static final RoiKit roiKit;
    @Getter
    private static final TestSuiteKit testSuiteKit;
    @Getter
    private static final TestCaseKit testCaseKit;
    @Getter
    private static final ConfigKit configKit;
    @Getter
    private static final SmokingTestKit smokingTestKit;
    @Getter
    private static final FdxConfigKit fdxConfigKit;
    @Getter
    private static final UdpConfigKit udpConfigKit;
    @Getter
    private static final LightTestBoxConfigKit lightConfigKit;
    @Getter
    private static final OscilloscopeDevice oscilloscopeManager;
    @Getter
    private static final SignalGeneratorDevice signalGeneratorManager;
    @Getter
    private static final WebSocketDataMonitor websocketDataMonitor;


    static {
        powerDeviceManager = new PowerDevice();
        autoClickerDeviceManager = new AutoClickerDevice();
        testBoxDeviceManager = new TestBoxDevice();
//        lightTestBoxDeviceManager = new LightTestBoxDevice();
        usbSwitchDeviceManager = new UsbSwitchDevice();
        androidDeviceManager = new AndroidDevice();
        adbHudDeviceManager = new AdbHudDevice();
        cameraDeviceManager = new CameraDevice();
        serialDeviceManager = new SerialDevice();
        usbPlugDeviceManager = new UsbPlugDevice();
//        electricRelayDeviceManager = new ElectricRelayDevice();
        i2cDeviceManager = new I2CDevice();
        resistanceManager = new ResistanceDevice();
        remoteResistanceManager = new RemoteResistanceDevice();
        resistanceQRDeviceManager = new ResistanceQRDevice();
        tcpServerDeviceManager = new TcpServerDevice();
        oscilloscopeManager = new OscilloscopeDevice();
        signalGeneratorManager = new SignalGeneratorDevice();
        videoCaptureDeviceManager = new VideoCaptureDevice();
        executorKit = new ExecutorKit();
        testClientKit = new TestClientKit();
        testProjectKit = new TestProjectKit();
        testScriptKit = new TestScriptKit();
        excelKit = new ExcelKit();
        operationGroupKit = new OperationGroupKit();
        screenKit = new ScreenKit();
        testSuiteKit = new TestSuiteKit();
        actionSequenceKit = new ActionSequenceKit();
        roiKit = new RoiKit();
        testCaseKit = new TestCaseKit();
        configKit = new ConfigKit();
        smokingTestKit = new SmokingTestKit();
        fdxConfigKit = new FdxConfigKit();
        udpConfigKit = new UdpConfigKit();
        lightConfigKit = new LightTestBoxConfigKit();
        websocketDataMonitor = new WebSocketDataMonitor();
    }

    public static void unregisterAllDevices() {
        try {
            delete(UrlConstants.DeviceUrls.UNREGISTER_ALL_DEVICES);
            //把treeDevicesConfig.json文件中所有的设备connectStatus置为false
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

}
