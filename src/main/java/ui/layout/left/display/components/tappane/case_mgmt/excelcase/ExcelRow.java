package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import lombok.Data;
import org.apache.commons.lang3.SerializationUtils;
import sdk.base.operation.Operation;
import ui.base.table.Copyable;

import java.util.Arrays;
import java.util.List;

@Data
public class ExcelRow implements Copyable {
    private boolean isSelected;
    private List<String> data;
    private List<Operation> operations;
    private int rowNumber;

    @SuppressWarnings("MethodDoesntCallSuperMethod")
    @Override
    public ExcelRow clone() {
        return SerializationUtils.clone(this);
    }

    public static void main(String[] args) {
        ExcelRow excelRow = new ExcelRow();
        excelRow.setSelected(true);
        excelRow.setRowNumber(1);
        excelRow.setOperations(Arrays.asList(new Operation(), new Operation()));
        ExcelRow excelRow1 = excelRow.clone();
        System.out.println();
    }
}
