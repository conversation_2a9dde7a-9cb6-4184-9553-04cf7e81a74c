package ui.layout.right.components.operate.operateview.panel;

import sdk.domain.Device;
import sdk.entity.SoundDevice;
import ui.config.json.AllDeviceConfig;
import ui.layout.right.components.operate.header.SampleRateHeaderHookComponent;
import ui.layout.right.components.operate.operateview.base.SampleRateDeviceOperatePanel;
import ui.model.MainModel;

/**
 * 声音采集连接面板
 */
public class SoundOperatePanel extends SampleRateDeviceOperatePanel {

    public SoundOperatePanel(MainModel mainModel, String deviceModel) {
        super(mainModel);
        SoundDevice httpClient = SoundDevice.getDevice(deviceModel);
        setLaterOpen(false);
        setDeviceClient(httpClient);
        createView();
        createActions();
        restoreView();
        setSampleRate(44100);
    }
    @Override
    public void restoreView() {
        AllDeviceConfig allDeviceConfig = getMainModel().getAllDeviceConfig();
        Device device = allDeviceConfig.getDevice(
                getDeviceClient().getDeviceType(),
                getDeviceClient().getDeviceModel(),
                0);
        if (device != null) {
            ((SampleRateHeaderHookComponent) getHookComponent()).getSampleRateComboBox().setSelectedItem(device.getSampleRate());
            getDeviceOperateHeader().getDeviceOperateHeaderManagerPanel().configDeviceOperationParameters();
        }
    }
}
