package ui.layout.right.components.operate.operateview.base;

import ui.base.BaseView;
import ui.layout.right.components.operate.header.HeaderHookComponent;
import ui.layout.right.components.operate.header.SampleRateHeaderHookComponent;
import ui.model.MainModel;

/**
 * <AUTHOR> lpf
 * @date : Created in 2024-10-15
 * @description :
 * @modified By :
 * @since : 2024-10-15
 */
public class SampleRateDeviceOperatePanel extends DeviceOperatePanel implements BaseView {

    public SampleRateDeviceOperatePanel(MainModel mainModel) {
        super(mainModel);
        registerModelObservers();
    }

    @Override
    protected HeaderHookComponent newHeaderHookComponent() {
        SampleRateHeaderHookComponent sampleRateHeaderHookComponent = new SampleRateHeaderHookComponent();
        setHookComponent(sampleRateHeaderHookComponent);
        return sampleRateHeaderHookComponent;
    }

    public void setSampleRate(int sampleRate) {
        setSampleRate(sampleRate, true);
    }

    public void setSampleRate(int sampleRate, boolean isEnabled) {
        SampleRateHeaderHookComponent sampleRateHeaderHookComponent = (SampleRateHeaderHookComponent) getHookComponent();
        sampleRateHeaderHookComponent.setSampleRate(sampleRate);
        sampleRateHeaderHookComponent.getSampleRateComboBox().setEnabled(isEnabled);
    }

//    @Override
//    public void deviceConnected(Device device, boolean autoOpenChannel) {
//        SampleRateHeaderHookComponent sampleRateHeaderHookComponent = (SampleRateHeaderHookComponent) getHookComponent();
//        sampleRateHeaderHookComponent.getSampleRateComboBox().setEnabled(false);
//        super.deviceConnected(device, autoOpenChannel);
//    }
//
//    @Override
//    public void deviceDisconnected(Device device) {
//        SampleRateHeaderHookComponent sampleRateHeaderHookComponent = (SampleRateHeaderHookComponent) getHookComponent();
//        sampleRateHeaderHookComponent.getSampleRateComboBox().setEnabled(true);
//        super.deviceDisconnected(device);
//    }


}
