package ui.layout.left.display.components.container.can;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class  DbcFileManager {
    private final static DbcFileManager dbcFileManager = new DbcFileManager();
    private final Map<String, List<DbcFileObserver>> observersMap = new HashMap<>();
    private final Map<String, Boolean> notifyingMap = new HashMap<>();

    public static DbcFileManager getInstance() {
        return dbcFileManager;
    }

    private DbcFileManager() {

    }

    public void addObserver(int channel, String deviceName, DbcFileObserver observer) {
        String key = getKey(channel, deviceName);
        observersMap.computeIfAbsent(key, k -> new ArrayList<>()).add(observer);
    }


    public void addAllObserver(DbcFileObserver observer) {
        for (Map.Entry<String, List<DbcFileObserver>> entry : observersMap.entrySet()) {
            entry.getValue().add(observer);
        }
    }

    public void removeObserver(int channel, String deviceName, DbcFileObserver observer) {
        String key = getKey(channel, deviceName);
        List<DbcFileObserver> observers = observersMap.get(key);
        if (observers != null) {
            observers.remove(observer);
        }
    }

    public void notifyObservers(int channel, String deviceName, List<String> dbcFilePaths) {
        String key = getKey(channel, deviceName);
        if (notifyingMap.getOrDefault(key, false)) {
            return;
        }

        notifyingMap.put(key, true);

        List<DbcFileObserver> observers = observersMap.get(key);
        if (observers != null) {
            for (DbcFileObserver observer : observers) {
                observer.onDbcFileChanged(dbcFilePaths);
            }
        }

        notifyingMap.put(key, false);
    }
    /**
     * 构建由通道和设备名拼接的键
     */
    private String getKey(int channel, String deviceName) {
        return channel + "_" + deviceName;
    }
}



