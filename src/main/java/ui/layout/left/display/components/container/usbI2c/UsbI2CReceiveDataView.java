package ui.layout.left.display.components.container.usbI2c;

import common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import sdk.domain.serial.SerialReceiveMessage;
import ui.base.BaseView;
import ui.config.json.devices.serial.SerialConfig;
import ui.layout.left.display.components.container.base.DeviceReceiveDataView;
import ui.model.MainModel;
import ui.model.device.DeviceReceiveDataObserver;

import javax.swing.*;
import javax.swing.text.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

@Slf4j
public class UsbI2CReceiveDataView extends JTextPane implements BaseView, DeviceReceiveDataObserver, DeviceReceiveDataView {

    private final static int MAX_LINE_LIMIT = 5000;

    private static class Config {
        public static int SIZE = 12;
    }

    private Document document;
    private SimpleAttributeSet attrSet;

    private final MainModel mainModel;

    private final JPopupMenu popupMenu;
    private final UsbI2CContainer usbI2CContainer;
    private final SerialConfig serialConfig;

    public UsbI2CReceiveDataView(UsbI2CContainer usbI2CContainer, MainModel mainModel, SerialConfig serialConfig) {
        this.serialConfig = serialConfig;
        this.usbI2CContainer = usbI2CContainer;
        this.mainModel = mainModel;
        popupMenu = new JPopupMenu();
        createView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void createView() {
        config();
        addMenu();
    }

    private class ClearActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            clear();
        }
    }


    private void addMenu() {
        JMenuItem clearMenuItem = new JMenuItem("清空");
        clearMenuItem.addActionListener(new UsbI2CReceiveDataView.ClearActionListener());
        popupMenu.add(clearMenuItem);
        setComponentPopupMenu(popupMenu);
    }

    @Override
    public void createActions() {

    }

    @Override
    public void registerModelObservers() {
        mainModel.getDeviceReceiveDataModel().registerObserver(this);
    }

    private void config() {
//        setFont(new Font(null, Font.PLAIN, Config.SIZE));
        attrSet = new SimpleAttributeSet();
//        StyleConstants.setFontSize(attrSet, Config.SIZE);
        document = getDocument();
    }

    public String insertText(String text, AttributeSet attributeSet) throws BadLocationException {
        document.insertString(document.getLength(), text, attributeSet);
        return text;
    }

    public String insertText(String text) throws BadLocationException {
        return insertText(text, attrSet);
    }

    public String insertText(String text, Color color) throws BadLocationException {
        SimpleAttributeSet simpleAttributeSet = new SimpleAttributeSet();
        StyleConstants.setForeground(simpleAttributeSet, color);
        return insertText(text, simpleAttributeSet);
    }

    public int getLineCount() {
        return getText().split("\n").length;
    }

    public void clear() {
        setText("");
    }

    private void clearContentIfMax() {
        if (getLineCount() > MAX_LINE_LIMIT) {
            clear();
        }
    }

    public void insertText(String message, boolean isSend) {
        try {
            if (!message.endsWith("\n")) {
                message += "\n";
            }
            if (isSend) {
                insertText(message, Color.BLUE);
            } else {
                insertText(message);
            }
        } catch (BadLocationException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void receiveMessage(String message) {
        clearContentIfMax();
        if (usbI2CContainer.getUsbI2CControlPanel().isHex()) {
            message = StringUtils.byteArrayToHexString(message.getBytes());
            message += '\n';
        }
        insertText(message, false);
        if (usbI2CContainer.getUsbI2CControlPanel().isAutoScroll()) {
            setCaretPosition(getDocument().getLength());
        }
    }

    @Override
    public void receiveMessage(SerialReceiveMessage message) {
        if (!message.getDeviceAliasName().equals(usbI2CContainer.getDevice().getAliasName())) {
            return;
        }
        clearContentIfMax();
        String text;
        if (usbI2CContainer.getUsbI2CControlPanel().isHex()) {
            text = StringUtils.byteArrayToHexString(message.getData());
            text += '\n';
        } else {
            text = new String(message.getData());
        }
        insertText(text, message.isSend());
        if (usbI2CContainer.getUsbI2CControlPanel().isAutoScroll()) {
            setCaretPosition(getDocument().getLength());
        }
    }
}
