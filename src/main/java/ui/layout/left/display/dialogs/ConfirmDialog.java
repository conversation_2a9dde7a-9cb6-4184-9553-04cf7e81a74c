package ui.layout.left.display.dialogs;

import common.constant.UiConstants;
import lombok.Getter;
import lombok.Setter;
import ui.base.BaseView;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

/**
 * 确认对话框
 */
public abstract class ConfirmDialog extends JDialog implements BaseView {
    @Setter
    private JComponent parentComponent;

    @Getter
    private JButton buttonConfirm;
    private JButton buttonCancel;
    @Setter
    @Getter
    private boolean confirmed = false;
    private final JPanel bottomPanel;

    @Getter
    private Component centerPanel;

    public ConfirmDialog() {
        parentComponent = null;
        bottomPanel = getBottomPanel();
    }

    public ConfirmDialog(JComponent parentComponent) {
        this();
        this.parentComponent = parentComponent;
    }

    public ConfirmDialog(JComponent parentComponent, String title) {
        this(parentComponent);
        setTitle(title);
    }

    public void addBottomComponent(JComponent component) {
        bottomPanel.add(component);
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        setLocationRelativeTo(parentComponent);
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);
        setModal(true);
        centerPanel = makeCenterPanel();
        if (centerPanel != null) {
            add(centerPanel, BorderLayout.CENTER);
        }
        add(bottomPanel, BorderLayout.SOUTH);
        getRootPane().setDefaultButton(buttonConfirm);
        SwingUtil.centerInScreen(this);
    }

    @Override
    public void createActions() {
        buttonConfirm.addActionListener(e -> confirm());
        buttonCancel.addActionListener(e -> cancel());
    }

    public void confirm() {
        if (performConfirm()) {
            confirmed = true;
            dispose();
        }
    }

    public void cancel() {
        confirmed = false;
        dispose();
    }

    protected boolean performConfirm() {
        return true;
    }

    public String okText() {
        return "确定";
    }

    public abstract Component makeCenterPanel();

    private JPanel getBottomPanel() {
        JPanel panelDown = new JPanel();
        panelDown.setLayout(new FlowLayout(FlowLayout.RIGHT, UiConstants.MAIN_H_GAP, 15));
        String ok = okText();
        buttonConfirm = new JButton(ok);
        String cancel = "取消";
        buttonCancel = new JButton(cancel);
        panelDown.add(buttonConfirm);
        panelDown.add(buttonCancel);
        return panelDown;
    }

}
