package ui.layout.left.display.components.treemenu.actiontree.action_mgmt;


import ui.entry.ClientView;
import ui.layout.left.display.components.treemenu.actiontree.ActionTree;
import ui.layout.left.display.components.treemenu.actiontree.group.ActionGroup;
import ui.model.MainModel;

import javax.swing.*;

public class ActionListMgmtPane extends JTabbedPane {
    private final MainModel mainModel;
    private final ClientView clientView;

    public ActionListMgmtPane(ClientView clientView, MainModel mainModel) {
        super();
        this.clientView = clientView;
        this.mainModel = mainModel;
        addTab("动作列表", new JScrollPane(new ActionTree(mainModel)));
        addTab("动作组合", new JScrollPane(new ActionGroup(mainModel)));
    }

}
