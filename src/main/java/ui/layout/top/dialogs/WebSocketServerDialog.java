package ui.layout.top.dialogs;

import com.alibaba.fastjson2.JSON;
import common.constant.AppConstants;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.execution.RemoteOperation;
import sdk.base.execution.RemoteOperationCommand;
import sdk.constants.UrlConstants;
import sdk.websocket.RemoteMonitorWebSocketClient;
import ui.base.BaseView;
import ui.layout.left.display.dialogs.ConfirmDialog;
import ui.model.MainModel;
import ui.model.testScript.NotificationScriptStatusObserver;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.EtchedBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

import static sdk.base.execution.RemoteOperationStatus.CLEAR_SCRIPT_COMPLETED;
import static sdk.base.execution.RemoteOperationStatus.IMPORT_COMPLETED;

/**
 * APP配置对话框
 */
@Slf4j
public class WebSocketServerDialog extends ConfirmDialog implements BaseView, RemoteMonitorWebSocketClient.MessageMonitorListener, NotificationScriptStatusObserver {
    private final JTextField ipTextField;
    private final JTextArea textArea;
    private final MainModel mainModel;
    private JButton btnNewButton;
    @Getter
    private RemoteMonitorWebSocketClient remoteMonitorWebSocketClient;

    @FunctionalInterface
    interface OperationMethodConsumer<T> {
        void accept(T t) throws Exception;
    }

    private final Map<RemoteOperationCommand, OperationMethodConsumer<RemoteOperation>> operationMethodMap = new HashMap<>();


    public WebSocketServerDialog(JComponent parentComponent, MainModel mainModel) {
        super(parentComponent);
        this.mainModel = mainModel;
        ipTextField = new JTextField();
        textArea = new JTextArea();
        setTitle("远程操作");
        initOperationMap();
        createView();
        createActions();
        restoreView();
        registerModelObservers();
    }

    private void initOperationMap() {
        operationMethodMap.put(RemoteOperationCommand.START_SCRIPT_TEST, this::startScriptTest);
        operationMethodMap.put(RemoteOperationCommand.STOP_SCRIPT_TEST, this::stopScriptTest);
        operationMethodMap.put(RemoteOperationCommand.IMPORT_SCRIPT_CASE, this::importScriptCase);
        operationMethodMap.put(RemoteOperationCommand.CLEAR_ALL_SCRIPT, this::clearAllScriptCase);
        operationMethodMap.put(RemoteOperationCommand.CHECK_STATUS, this::checkTestStatus);
        operationMethodMap.put(RemoteOperationCommand.SWITCH_TEST_SCRIPT, this::switchTestScript);
        operationMethodMap.put(RemoteOperationCommand.START_EXCEL_CASE_TEST, this::startExcelCaseTest);
        operationMethodMap.put(RemoteOperationCommand.STOP_EXCEL_CASE_TEST, this::stopExcelCaseTest);
        operationMethodMap.put(RemoteOperationCommand.IMPORT_EXCEL_CASE_FILE, this::importExcelCaseFile);
    }

    @Override
    public void createView() {
        super.createView();
        setSize(800, 500);
        setLocationRelativeTo(null);
    }

    @Override
    public void registerModelObservers() {
        mainModel.getNotificationScriptStatusModel().registerObserver(this);
    }

    @Override
    public JPanel makeCenterPanel() {
        JPanel centerPanel = new JPanel();
        JPanel contentPanel = new JPanel();
        centerPanel.setLayout(new BorderLayout());
        contentPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
        centerPanel.add(contentPanel, BorderLayout.NORTH);
        contentPanel.setLayout(new GridLayout(1, 1, 0, 0));
        JPanel panel = new JPanel();
        FlowLayout flowLayout = (FlowLayout) panel.getLayout();
        flowLayout.setAlignment(FlowLayout.LEFT);
        contentPanel.add(panel);

        JLabel lblNewLabel = new JLabel("IP地址：");
        panel.add(lblNewLabel);

        ipTextField.setText("127.0.0.1");
        panel.add(ipTextField);
        ipTextField.setColumns(12);

        panel.add(new JLabel("端口号："));

        JSpinner spinner = new JSpinner();
        spinner.setModel(new SpinnerNumberModel(12399, 1, null, 1));
        spinner.setPreferredSize(new Dimension(200, 35));
        panel.add(spinner);

        btnNewButton = new JButton("连接测试");
        panel.add(btnNewButton);
        btnNewButton.addActionListener(e -> startWebSocketServer());
        JPanel panel_1 = new JPanel();
        panel_1.setBorder(new TitledBorder(new EtchedBorder(EtchedBorder.LOWERED, new Color(255, 255, 255), new Color(160, 160, 160)), "\u8FDE\u63A5\u6D4B\u8BD5\u65E5\u5FD7", TitledBorder.LEADING, TitledBorder.TOP, null, new Color(0, 0, 0)));
        centerPanel.add(panel_1, BorderLayout.CENTER);
        panel_1.setLayout(new GridLayout(0, 1, 0, 0));
        panel_1.add(textArea);
        return centerPanel;

    }

    private boolean startWebSocketServer() {
        try {
            if (remoteMonitorWebSocketClient != null) {
                if (remoteMonitorWebSocketClient.isClosed()) {
                    log.info("重新连接remote Monitor websocket");
                    remoteMonitorWebSocketClient.reconnectBlocking();
                    log.info("重新连接remote Monitor websocket成功");
                } else {
                    log.info("remote Monitor websocket已连接");
                }
                return true;
            }
            log.info("正在连接remote Monitor websocket...");
            remoteMonitorWebSocketClient = new RemoteMonitorWebSocketClient(UrlConstants.WebSocketUrls.REMOTE_MONITOR_URL);
            remoteMonitorWebSocketClient.addMessageMonitorListener(this);
            remoteMonitorWebSocketClient.setConnectionLostTimeout(AppConstants.WEBSOCKET_TIMEOUT);
            remoteMonitorWebSocketClient.connectBlocking();
            if (remoteMonitorWebSocketClient.isOpen()) {
                log.info("连接remote Monitor websocket成功");
                return true;
            } else {
                log.warn("连接remote Monitor websocket失败");
                return false;
            }
        } catch (URISyntaxException | InterruptedException e) {
            log.error("连接连接remote Monitor websocket失败:", e);
            return false;
        }

    }

    @Override
    public void setVisible(boolean b) {
        super.setVisible(b);
    }


    @Override
    protected boolean performConfirm() {
        remoteMonitorWebSocketClient.onClose(0, "close", true);
        startWebSocketServer();
        return super.performConfirm();
    }

    @Override
    public void operationCommand(String message) {
        try {
            RemoteOperation remoteOperation = JSON.parseObject(message, RemoteOperation.class);
            OperationMethodConsumer<RemoteOperation> method = operationMethodMap.get(remoteOperation.getCommand());
            if (method != null) {
                method.accept(remoteOperation);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void showMessage(String message) {
        textArea.append(message + "\n");
        textArea.setCaretPosition(textArea.getDocument().getLength());
    }


    private void startScriptTest(RemoteOperation remoteOperation) {
        mainModel.getRemoteOperationModel().startExecution(remoteOperation);
    }

    private void stopScriptTest(RemoteOperation remoteOperation) {
        mainModel.getRemoteOperationModel().stopExecution(remoteOperation);
    }


    private void importScriptCase(RemoteOperation remoteOperation) {
        boolean importResult = mainModel.getRemoteOperationModel().importTestCase(remoteOperation);
        if (importResult) {
            notification(new RemoteOperation(IMPORT_COMPLETED));
        }
    }

    private void clearAllScriptCase(RemoteOperation remoteOperation) {
        boolean clearResult = mainModel.getRemoteOperationModel().clearAllScriptCase(remoteOperation);
        if (clearResult) {
            notification(new RemoteOperation(CLEAR_SCRIPT_COMPLETED));
        }
    }

    private void checkTestStatus(RemoteOperation remoteOperation) {
        mainModel.getRemoteOperationModel().checkTestStatus(remoteOperation);
    }

    private void switchTestScript(RemoteOperation remoteOperation) {
        mainModel.getRemoteOperationModel().switchTestScript(remoteOperation);
    }

    private void startExcelCaseTest(RemoteOperation remoteOperation) {
        mainModel.getRemoteOperationModel().startExcelCaseTest(remoteOperation);
    }

    private void stopExcelCaseTest(RemoteOperation remoteOperation) {
        mainModel.getRemoteOperationModel().stopExcelCaseTest(remoteOperation);
    }

    private void importExcelCaseFile(RemoteOperation remoteOperation) {
        mainModel.getRemoteOperationModel().importExcelCaseFile(remoteOperation);
    }


    @Override
    public void notification(RemoteOperation operation) {
        if (remoteMonitorWebSocketClient != null) {
            operation.setCommand(RemoteOperationCommand.REPLY_RESULT);
            remoteMonitorWebSocketClient.send(JSON.toJSONString(operation));
        }
    }

    // 用于触发 btnNewButton 的点击事件 连接测试
    public void triggerConnectButton() {
        btnNewButton.doClick();
    }
}
