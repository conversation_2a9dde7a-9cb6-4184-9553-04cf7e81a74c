package ui.layout.left.display.components.container.robot.ui;

import common.constant.ResourceConstant;
import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationMethod;
import sdk.constants.methods.DeviceMethods;
import sdk.entity.RobotDevice;
import ui.layout.left.display.components.container.robot.SwipeQuicklyPoint;
import ui.layout.left.display.dialogs.LongTouchSettingDialog;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

/**
 * @projectName: aitestxclient
 * @package: ui.layout.left.display.dialogs
 * @className DirectionDialog
 * @author: <PERSON><PERSON>ao
 * @description: TODO
 * @date: 2024/3/13 17:09
 * @version: 1.0
 */
public class DirectionDialog extends JDialog {
    private int returnValue = -1;
    private final JLabel selectedArrow = new JLabel();
    private final JSpinner dynSpinner;
    private final SwipeQuicklyPoint swipeQuicklyPoint;
    private final MainModel mainModel;
    private final RobotDevice robotDevice;
    private static volatile DirectionDialog directionDialog;

    public static DirectionDialog getInstance(MainModel mainModel, RobotDevice robotDevice, String coordinateName) {
        if (directionDialog == null) {
            synchronized (LongTouchSettingDialog.class) {
                if (directionDialog == null) {
                    directionDialog = new DirectionDialog(mainModel, robotDevice);
                }
            }
        }
        directionDialog.swipeQuicklyPoint.setCoordinateName(coordinateName);
        return directionDialog;
    }

    public DirectionDialog(MainModel mainModel, RobotDevice robotDevice) {
        this.mainModel = mainModel;
        this.robotDevice = robotDevice;
        swipeQuicklyPoint = new SwipeQuicklyPoint();
        dynSpinner = new JSpinner(new SpinnerNumberModel(1, 1, 8, 1));
        setTitle("选择滑动方向");
        setLayout(new BorderLayout());
        add(combinedLayout(DeviceMethods.quickSwipe));
        pack();
        SwingUtil.centerInScreen(this, false);

    }

    private JPanel combinedLayout(OperationMethod operationMethod) {
        JPanel jPanel = new JPanel(new GridLayout(2, 1));
        JPanel directionPanel = new JPanel(new GridLayout(3, 3, 5, 5));
        jPanel.add(directionPanel, BorderLayout.CENTER);

        JButton nwButton = createButton("↖", 7);
        JButton nButton = createButton("↑", 8);
        JButton neButton = createButton("↗", 9);
        JButton wButton = createButton("←", 4);
        JButton eButton = createButton("→", 6);
        JButton swButton = createButton("↙", 1);
        JButton sButton = createButton("↓", 2);
        JButton seButton = createButton("↘", 3);

        directionPanel.add(nwButton);
        directionPanel.add(nButton);
        directionPanel.add(neButton);
        directionPanel.add(wButton);
        directionPanel.add(new JLabel());
        directionPanel.add(eButton);
        directionPanel.add(swButton);
        directionPanel.add(sButton);
        directionPanel.add(seButton);
        JPanel controlPanel = new JPanel(new GridLayout(2, 1));
        JPanel displayPanel = new JPanel();
        displayPanel.add(new JLabel("滑动方向:"));
        displayPanel.add(selectedArrow);
        displayPanel.add(new JLabel("滑动力度"));
        displayPanel.add(dynSpinner);
        JPanel buttonPanel = new JPanel();

        float ratio = 8.0f;
        JButton debugButton = new JButton(SwingUtil.getResourceAsImageIcon(ResourceConstant.LeftLayout.debugIconPath, ratio));
        JButton addToScriptButton = SwingUtil.getAddToScriptButton();
        buttonPanel.add(debugButton);
        buttonPanel.add(addToScriptButton);
        controlPanel.add(displayPanel);
        controlPanel.add(buttonPanel);
        jPanel.add(controlPanel, BorderLayout.SOUTH);
        debugButton.addActionListener(e -> {
            if (returnValue != -1) {
                swipeQuicklyPoint.setDirection(returnValue);
                swipeQuicklyPoint.setDynamics((Integer) dynSpinner.getValue());
                Operation operation = Operation.buildOperation(robotDevice);
                operation.setOperationMethod(operationMethod);
                operation.setOperationObject(swipeQuicklyPoint);
                BaseHttpClient.executeOperation(operation);
            } else {
                SwingUtil.showWarningDialog(this, "请选择滑动的方向");
            }
        });
        addToScriptButton.addActionListener(e -> {
            if (returnValue != -1) {
                swipeQuicklyPoint.setDirection(returnValue);
                swipeQuicklyPoint.setDynamics((Integer) dynSpinner.getValue());
                Operation operation = Operation.buildOperation(robotDevice);
                operation.setOperationMethod(operationMethod);
                operation.setOperationObject(swipeQuicklyPoint);
                mainModel.getOperationModel().updateOperation(operation);
                dispose();
            } else {
                SwingUtil.showWarningDialog(this, "请选择滑动的方向");

            }
        });
        return jPanel;
    }

    private JButton createButton(String text, int returnValue) {
        JButton button = new JButton(text);
        button.setActionCommand(String.valueOf(returnValue));
        button.addActionListener(e -> {
            DirectionDialog.this.returnValue = Integer.parseInt(e.getActionCommand());
            selectedArrow.setText(button.getText());
        });
        return button;
    }

}