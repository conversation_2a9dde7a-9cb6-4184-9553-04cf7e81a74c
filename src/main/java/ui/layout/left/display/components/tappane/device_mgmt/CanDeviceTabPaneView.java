package ui.layout.left.display.components.tappane.device_mgmt;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.can.CanContainer;
import ui.layout.left.display.components.tappane.base.DeviceTabPaneView;
import ui.model.MainModel;
import ui.service.CanDbcReceiverControlService;

/**
 * CAN设备控制界面
 */
public class CanDeviceTabPaneView extends DeviceTabPaneView {

    public CanDeviceTabPaneView(ClientView clientView, MainModel mainModel) {
        super(clientView, mainModel);
    }

    @Override
    public void addTabHook(String tabName, Device device) {
        CanContainer canContainer = new CanContainer(getClientView(), getMainModel(), device);
        setDeviceContainer(tabName, canContainer);
        CanDbcReceiverControlService.getInstance().registerCanContainer(device.getAliasName(), canContainer);
    }

    @Override
    public void removeTabHook(String tabName, Device device) {
        remove(indexOfTab(tabName));
        CanDbcReceiverControlService.getInstance().unregisterCanContainer(device.getAliasName());
    }
}
