package ui.config.json;

import com.alibaba.fastjson2.JSON;
import common.constant.DeviceCategory;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import ui.config.xml.app.AppFileManager;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//
@Slf4j
public class DeviceItemDisplayConfigManager {

    @Getter
    private static final DeviceItemDisplayConfigManager deviceItemDisplayManager = new DeviceItemDisplayConfigManager(); // 单例

    private static final String CONFIG_FILE_NAME = "devicesItemDisplayConfig.json";
    @Setter
    private String projectName;
    @Setter
    private List<DeviceCategory> allCategories;


    /**
     * 内存中的配置对象
     */
    private DeviceItemDisplayConfig deviceItemDisplayConfig;

    private DeviceItemDisplayConfigManager() {

    }

    /**
     * 保存当前deviceItemDisplayConfig到json文件中
     */
    public void save() {
        log.info("保存设备显示配置");
        if (deviceItemDisplayConfig == null) {
            deviceItemDisplayConfig = new DeviceItemDisplayConfig();
        }
        String config = JSON.toJSONString(deviceItemDisplayConfig);
        try {
            File configFile = AppFileManager.getInstance().getConfigFolder().createFile(CONFIG_FILE_NAME);
            try (OutputStreamWriter writer = new OutputStreamWriter(Files.newOutputStream(configFile.toPath()), StandardCharsets.UTF_8)) {
                writer.write(config);
            }
        } catch (IOException e) {
            log.warn("保存设备显示配置时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 从 JSON 文件中读取配置到内存
     */
    public DeviceItemDisplayConfig readDeviceConfig() {
        File configFile = null;
        try {
            configFile = AppFileManager.getInstance().getConfigFolder().createFile(CONFIG_FILE_NAME);
            if (configFile.exists() && configFile.length() > 0) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(Files.newInputStream(configFile.toPath()), StandardCharsets.UTF_8))) {
                    StringBuilder sb = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        sb.append(line);
                    }
                    String content = sb.toString().trim();
                    if (!content.isEmpty()) {
                        deviceItemDisplayConfig = JSON.parseObject(content, DeviceItemDisplayConfig.class);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("读取设备显示配置时出错: {}", e.getMessage(), e);
        }
        if (deviceItemDisplayConfig == null || deviceItemDisplayConfig.getDeviceCategories(projectName).isEmpty()) {
            Map<String, List<DeviceCategory>> devices = new HashMap<>();
            devices.put(projectName, allCategories);
            deviceItemDisplayConfig = new DeviceItemDisplayConfig(devices);
            save();
        }
        return deviceItemDisplayConfig;
    }

    /**
     * 获取当前内存中的配置对象
     */
    public DeviceItemDisplayConfig getDeviceItemDisplayConfig() {
        if (deviceItemDisplayConfig == null) {
            return readDeviceConfig();
        }
        return deviceItemDisplayConfig;
    }

    /**
     * 根据项目名称获取其设备分类列表
     */
    public List<DeviceCategory> getVisibleDeviceCategories(String projectName) {
        return getDeviceItemDisplayConfig().getDeviceCategories(projectName);
    }

    /**
     * 设置某项目的显示设备分类列表并保存
     */
    public void setVisibleDeviceCategories(String projectName, List<DeviceCategory> categories) {
        getDeviceItemDisplayConfig().setDeviceCategories(projectName, categories);
        save();
    }

}
