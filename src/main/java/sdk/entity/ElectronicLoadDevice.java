package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import common.constant.AppConstants;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.domain.Device;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ElectronicLoadDevice extends Device {

    private static final Map<String, ElectronicLoadDevice> deviceMap = new HashMap<>();

    public static ElectronicLoadDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            ElectronicLoadDevice device = new ElectronicLoadDevice(deviceModel);
            deviceMap.put(deviceModel, device);
        }
        return deviceMap.get(deviceModel);
    }

    public ElectronicLoadDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.ELECTRONIC_LOAD.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ELECTRONIC_LOAD;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<ElectronicLoadDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<ElectronicLoadDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        String commProtocol = getCommProtocol();
        String url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllSerialPorts(getDeviceModel());
        if (commProtocol != null) {
            //存在通讯协议
            if (commProtocol.equals(AppConstants.usbProtocol)) {
                url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllVisaPorts(getDeviceModel());
            }
        }
        JsonResponse<List<ElectronicLoadDevice>> resp = defaultGetJsonResponse(url,
                new TypeReference<JsonResponse<List<ElectronicLoadDevice>>>() {
                });
        return JsonResponse.retrieve(resp);
    }
}
