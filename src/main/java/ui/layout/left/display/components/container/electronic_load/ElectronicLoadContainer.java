package ui.layout.left.display.components.container.electronic_load;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;

public class ElectronicLoadContainer extends DeviceContainer {
    public ElectronicLoadContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        createView();
    }
}
