package ui.layout.right.components.operate.operateview.wrapper;

import common.constant.DeviceCategoryConstants;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.RemoteResistanceOperatePanel;
import ui.layout.right.components.operate.operateview.panel.ResistanceOperatePanel;
import ui.layout.right.components.operate.operateview.panel.ResistanceQROperatePanel;
import ui.model.MainModel;

public class ResistanceOperateTabWrapper extends DeviceOperateTabWrapper {

    public ResistanceOperateTabWrapper(MainModel mainModel) {
        super(mainModel);

    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_RESISTANCE;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel(DeviceCategoryConstants.RESISTANCE_DEVICE.getDeviceOfficialName(), new ResistanceOperatePanel(mainModel));
        addOperatePanel("威汇程控电阻仪", new RemoteResistanceOperatePanel(mainModel));
        addOperatePanel("QR10X", new ResistanceQROperatePanel(mainModel));
    }
}
