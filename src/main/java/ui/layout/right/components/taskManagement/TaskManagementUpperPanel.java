package ui.layout.right.components.taskManagement;

import excelcase.config.json.TaskConfig;
import excelcase.config.json.TaskConfigJson;
import ui.base.BaseView;
import ui.entry.ClientView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTabPaneView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.ExcelCaseTable;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class TaskManagementUpperPanel extends JPanel implements BaseView {
    private final JButton newTaskButton;
    private final JButton deleteTaskButton;
    private final MainModel mainModel;
    private final ClientView clientView;
    private final TaskManagementTable taskManagementTable;
    private final ExcelCaseTabPaneView excelCaseTabPaneView;
    private final List<TaskFile> taskFileList = TaskConfig.getInstance().getTaskFileList() == null ? new ArrayList<>() : TaskConfig.getInstance().getTaskFileList();

    public TaskManagementUpperPanel(TaskManagementTable taskManagementTable, ClientView clientView, MainModel mainModel) {
        this.taskManagementTable = taskManagementTable;
        this.mainModel = mainModel;
        this.clientView = clientView;
        excelCaseTabPaneView = clientView.getLeftPanelController().getLeftPanelView().getDisplayTabPane().getCaseMgmtTabPaneView().getExcelCaseTabPaneView();
        newTaskButton = new JButton("新建任务");
        deleteTaskButton = new JButton("删除任务");
        createView();
        createActions();
    }

    public void renderRowsByTaskConfig() {
        if (!taskFileList.isEmpty()) {
            taskManagementTable.setTaskEmpty(false);
            for (TaskFile taskFile : taskFileList) {
                addTask(taskFile);
            }
        }
        switchToTaskManagementTable();
    }

    @Override
    public void createView() {
        add(newTaskButton);
        add(deleteTaskButton);
    }

    @Override
    public void createActions() {
        newTaskButton.addActionListener(e -> newTask());
        deleteTaskButton.addActionListener(e -> deleteTask());
    }

    private void deleteTask() {
        int[] rows = taskManagementTable.getSelectedRows();
        rows = Arrays.stream(rows).boxed().sorted(((o1, o2) -> o2 - o1)).mapToInt(int1 -> int1).toArray();
        taskManagementTable.deleteRowsActivated(rows);
        for (int row : rows) {
            TaskFile taskFile = taskManagementTable.getRow(row);
            taskFileList.remove(taskFile);
        }
        TaskConfig.getInstance().setTaskFileList(taskFileList);
        TaskConfigJson.getInstance().save();
    }

    private void newTask() {
        String taskName = JOptionPane.showInputDialog(this, "请输入任务名称", "");
        if (taskName != null) {
            if (taskName.trim().isEmpty()) {
                SwingUtil.showWarningDialog(this, "任务名称不能为空");
                return;
            }
            TaskFile taskFile = new TaskFile();
            taskFile.setTaskName(taskName);
            ExcelCaseTable selectedExcelCaseTable = excelCaseTabPaneView.getExcelCaseRenderTabbedPane().getSelectedExcelCaseTable();
            taskFile.setExcelTableName(selectedExcelCaseTable.getSheetName());
            taskFile.setSelected(true);
            taskFile.setSelectedRows(selectedExcelCaseTable.getCheckedRows());
            try {
                taskFileList.add(taskFile);
                addTask(taskFile);
                TaskConfig.getInstance().setTaskFileList(taskFileList);
                TaskConfigJson.getInstance().save();
            } catch (Exception e) {
                SwingUtil.showWarningDialog(this, e.getMessage());
            }
            switchToTaskManagementTable();
        }
    }

    private void addTask(TaskFile taskFile) {
        taskManagementTable.addRowData(taskFile);
    }

    public void switchToTaskManagementTable() {
        Container parent = this.getParent();
        if (parent instanceof TaskManagementPaneView) {
            ((TaskManagementPaneView) parent).switchToTaskManagementTable();
        }
    }

}
