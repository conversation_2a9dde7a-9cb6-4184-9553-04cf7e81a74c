package ui.layout.left.display.components.treemenu.devicetree.impl;

import common.constant.DeviceCategory;
import lombok.Getter;
import lombok.Setter;
import sdk.base.operation.OperationTarget;
import sdk.domain.Device;
import ui.base.treelist.AbstractTree;
import ui.base.treelist.AbstractTreeNode;
import ui.config.json.tree.DeviceConfigManager;
import ui.model.MainModel;

import javax.swing.tree.TreeNode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备类别节点
 **/
public class DeviceNode extends AbstractTreeNode {

    @Getter
    @Setter
    private String deviceType;
    private final DeviceItemMenu deviceItemMenu;
    @Getter
    private final AbstractTree tree;

    @Getter
    private final List<Device> devices;

    public interface DeviceNodeListener {
        void onItemRecursiveArrangeCompleted(List<Device> devices);
    }

    @Setter
    @Getter
    private DeviceNodeListener deviceNodeListener;

    public DeviceNode(AbstractTree tree, String name, MainModel mainModel) {
        super(name);
        this.tree = tree;
        deviceItemMenu = new DeviceItemMenu(mainModel, this);
        devices = new ArrayList<>();
    }

    public List<Integer> getDeviceIndexes() {
        return getDevices().stream().mapToInt(OperationTarget::getDeviceIndex).boxed().sorted().collect(Collectors.toList());
    }

    /**
     * 创建设备树第一层类别节点
     *
     * @param tree           设备树
     * @param mainModel      主数据模型
     * @param deviceCategory 设备类别
     * @param parent         父类节点
     * @return DeviceNode
     */
    public static DeviceNode build(AbstractTree tree, MainModel mainModel, DeviceCategory deviceCategory, AbstractTreeNode parent) {
        DeviceNode node = new DeviceNode(tree, deviceCategory.getDeviceOfficialName() + deviceCategory.getRemark(), mainModel);
        node.setDeviceType(deviceCategory.getDeviceType());
        parent.addNode(node);
        return node;
    }

    /**
     * 获取设备item顺序（从1开始）
     *
     * @return 设备item顺序
     */
    public int getDeviceItemOrder() {
        return getChildCount();
    }

    /**
     * 添加设备节点
     *
     * @param device 设备实例
     */
    public void addDevice(Device device, boolean enableAutoRename) {
        int deviceIndex = device.getDeviceIndex();
        //设置设备索引
        device.setDeviceIndex(deviceIndex);
        //创建设备节点名
        DeviceItem deviceItem = DeviceItem.build(device, this, false, enableAutoRename);
        //设置菜单
        deviceItem.setMenu(deviceItemMenu);
        //更新树ui
        tree.updateTreeUI();
        tree.expandNode((AbstractTreeNode) deviceItem.getParent());
        //保存到json文件
        DeviceConfigManager.addDevice(device).save();
        devices.add(device);
//        if (enableAutoRename && isDeviceIndexRepeat()) {
//            //重排序号
//            DeviceItem.recursiveArrangeDeviceIndex(this);
//        }
    }


    public void removeDevice(Device device) {
        devices.remove(device);
    }

    public List<DeviceItem> getDeviceItems() {
        List<DeviceItem> deviceItems = new LinkedList<>();
        for (Enumeration<TreeNode> e = children(); e.hasMoreElements(); ) {
            DeviceItem item = (DeviceItem) e.nextElement();
            deviceItems.add(item);
        }
        return deviceItems;
    }

}
