package ui.layout.left.display.components.container.usbI2c;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ui.config.json.devices.serial.SerialConfig;

/**
 * @projectName: aitestxclient
 * @package: ui.layout.left.display.components.container.usbI2c
 * @className: IICConfig
 * @author: <PERSON><PERSON><PERSON>
 * @description: TODO
 * @date: 2024/4/11 10:17
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IICConfig extends SerialConfig {
    private String rate;//时钟频率
    private int clkSLevel;//时钟延展
    private int usbIndex;//usb索引
    private int ioNum;//io口选择
    private int ioDir;//io口方向
    private int ioBit;//io口电平
    private int preDivision;//预分频
    private int period;//占空比精度
    private int pulse;//占空比参数
    private int pulseNum;//脉冲数
    private byte adcNum;//ADC通道


}
