package ui.layout.right.components.operate.operateview.base;

import ui.layout.right.components.operate.header.AddressBaudRateHeaderHookComponent;
import ui.layout.right.components.operate.header.HeaderHookComponent;
import ui.model.MainModel;

public class AddressBaudRateDeviceOperatePanel extends BaudRateDeviceOperatePanel {

    public AddressBaudRateDeviceOperatePanel(MainModel mainModel) {
        super(mainModel);
        registerModelObservers();
    }

    @Override
    public HeaderHookComponent newHeaderHookComponent() {
        AddressBaudRateHeaderHookComponent hookComponent = new AddressBaudRateHeaderHookComponent();
        setHookComponent(hookComponent);
        return hookComponent;
    }

    public Integer getAddress() {
        return ((AddressBaudRateHeaderHookComponent) getHookComponent()).getAddress();
    }

    public void setAddress(Integer address) {
        ((AddressBaudRateHeader<PERSON>ookComponent) getHookComponent()).setAddress(address);
    }
}
