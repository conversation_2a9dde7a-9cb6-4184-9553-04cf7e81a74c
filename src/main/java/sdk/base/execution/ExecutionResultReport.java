package sdk.base.execution;

import lombok.Data;
import sdk.base.operation.OperationsReport;
import sdk.base.operation.TestResultReport;

import java.util.Map;

/**
 * 执行脚本测试结果
 */
@Data
public class ExecutionResultReport {

    //case整体测试结果
    private TestResultReport testResultReport;

    //case步骤测试结果
    private Map<Integer, OperationsReport> operationsReportMap;

    public boolean isEmpty() {
        return operationsReportMap == null || operationsReportMap.isEmpty();
    }

}