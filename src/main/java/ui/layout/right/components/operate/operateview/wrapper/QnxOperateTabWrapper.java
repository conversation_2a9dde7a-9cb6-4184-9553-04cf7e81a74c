package ui.layout.right.components.operate.operateview.wrapper;

import common.constant.DeviceCategoryConstants;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.QnxOperatePanel;
import ui.model.MainModel;

/**
 * QNX设备连接界面
 **/
public class QnxOperateTabWrapper extends DeviceOperateTabWrapper {
    public QnxOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel(DeviceCategoryConstants.QNX.getDeviceOfficialName(), new QnxOperatePanel(mainModel));
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_QNX;
    }

}
