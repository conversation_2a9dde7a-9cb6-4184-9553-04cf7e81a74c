package ui.layout.left.display.components.container.testbox.test;

import com.alibaba.fastjson2.JSONObject;
import sdk.base.operation.OperationResult;
import sdk.entity.TestBoxDevice;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/12 19:16
 * @description :
 * @modified By :
 * @since : 2023/6/12
 **/
public class ConfigBoardCardPane extends DeviceControlPanel {
    private final TestBoxDevice testBoxDevice;
    private final PanelGenerator testBoxPowerGenerator;
    private final PanelGenerator relayGenerator;

    private final PanelGenerator resistanceGenerator;
    private final PanelGenerator pwmOutputGenerator;
    private final PanelGenerator triStateOutputGenerator;
    private final TestBoxPowerPane testBoxPowerPane;
    private final ResistancePane resistancePane;
    private final PWMOutputPane pwmOutputPane;
    private final TriStateOutputPane triStateOutputPane;
    private final ClientView clientView;


    public ConfigBoardCardPane(ClientView clientView, MainModel mainModel, DeviceContainer deviceContainer) {
        super(deviceContainer, mainModel);
        this.clientView = clientView;
        testBoxDevice = (TestBoxDevice) getDeviceContainer().getDevice();
        testBoxPowerGenerator = new PanelGenerator();
        relayGenerator = new PanelGenerator();
        resistanceGenerator = new PanelGenerator();
        pwmOutputGenerator = new PanelGenerator();
        triStateOutputGenerator = new PanelGenerator();
        testBoxPowerPane = new TestBoxPowerPane(clientView, deviceContainer, testBoxPowerGenerator);
        resistancePane = new ResistancePane(this, resistanceGenerator);
        pwmOutputPane = new PWMOutputPane(this, pwmOutputGenerator);
        triStateOutputPane = new TriStateOutputPane(this, triStateOutputGenerator);
        createPane();
    }

    private void createPane() {
        setLayout(new BorderLayout());
        JPanel powerAndRelayPanel = new JPanel(new BorderLayout());
        JPanel testBoxPowerPanel = testBoxPowerPane.getTestBoxPowerPane();
        JPanel relayPanel = new RelayPane(this, relayGenerator).getRelayPanel();
        powerAndRelayPanel.add(testBoxPowerPanel, BorderLayout.NORTH);
        powerAndRelayPanel.add(relayPanel, BorderLayout.CENTER);

        JPanel resistancePanel = resistancePane.getResistancePanel();
        JPanel pwmOutputPanel = pwmOutputPane.getPWMOutputPanel();
        JPanel triStateOutputPanel = triStateOutputPane.getTriStateOutputPane();

        JPanel combinationPanel = new JPanel(new BorderLayout());
        combinationPanel.setPreferredSize(new Dimension(700, combinationPanel.getHeight()));
        combinationPanel.add(powerAndRelayPanel, BorderLayout.NORTH);
        combinationPanel.add(triStateOutputPanel, BorderLayout.CENTER);

        JPanel combinationLeftPanel = new JPanel(new BorderLayout());
        combinationLeftPanel.add(combinationPanel, BorderLayout.WEST);
        combinationLeftPanel.add(resistancePanel, BorderLayout.CENTER);

        add(combinationLeftPanel, BorderLayout.CENTER);
        add(pwmOutputPanel, BorderLayout.EAST);
    }


    private void loadBoardCardData(){
        loadRelayData();
        loadResistanceData();
        loadTriStateOutputData();
        loadPWMOutputData();
        loadPowerData();
    }

    public void loadConfigPaneData(boolean newThread) {
        if (newThread) {
            new SwingWorker<Void, Void>() {
                @Override
                protected Void doInBackground() {
                    loadBoardCardData();
                    return null;
                }
            }.execute();
        } else {
            loadBoardCardData();
        }

    }

    private void loadPowerData() {
        boolean output = testBoxPowerPane.fetchOutput();
        if (output) {
            testBoxPowerPane.getSwitchButton().setText("打开");
            testBoxPowerPane.getSwitchButton().setBackground(Color.GREEN);
        } else {
            testBoxPowerPane.getSwitchButton().setText("关闭");
            testBoxPowerPane.getSwitchButton().setBackground(new Color(192, 192, 193));
        }
        Double voltage = testBoxPowerPane.fetchVoltage();
        if (voltage != -1) {
            SwingUtilities.invokeLater(() -> {
                List<JPanel> controlList = testBoxPowerGenerator.getControlList();
                for (JPanel panel : controlList) {
                    Component[] components = panel.getComponents();
                    for (Component component : components) {
                        if (component instanceof SpinnerSlider) {
                            SpinnerSlider spinnerSlider = (SpinnerSlider) component;
                            spinnerSlider.getSpinner().setValue(voltage);
                            spinnerSlider.getSlider().setValue(Math.round(voltage.floatValue()));
                        }
                    }
                }
            });
        }
    }


    public synchronized void controlChangeListenerFlag(boolean flag) {
        List<SpinnerSliderListener> powerChangeListenerList = testBoxPowerPane.getChangeListenerList();
        for (SpinnerSliderListener spinnerSliderChangeListener : powerChangeListenerList) {
            spinnerSliderChangeListener.setFirstConnectDevice(flag);
        }

        List<SpinnerSliderListener> changeListenerList = resistancePane.getChangeListenerList();
        for (SpinnerSliderListener spinnerSliderChangeListener : changeListenerList) {
            spinnerSliderChangeListener.setFirstConnectDevice(flag);
        }

        List<CompositePanel> compositePanelList = pwmOutputPane.getCompositePanelList();
        for (CompositePanel compositePanel : compositePanelList) {
            for (SpinnerSliderListener changeListener : compositePanel.getChangeListenerList()) {
                changeListener.setFirstConnectDevice(flag);
            }
        }

        List<TriStateComboBox> triStateComboBoxList = triStateOutputPane.getTriStateComboBoxList();
        for (TriStateComboBox triStateComboBox : triStateComboBoxList) {
            triStateComboBox.setItemSelectedFlag(flag);
        }

    }


    private void loadResistanceData() {
        //读取电阻板卡所有通道的数据
        OperationResult res = testBoxDevice.fetchResistanceBoardCard();
        if (res.isOk()) {
            List<Integer> dataList = (List<Integer>) res.getData();
            SwingUtilities.invokeLater(() -> {
                List<JPanel> controlList = resistanceGenerator.getControlList();
                for (int i = 0; i < dataList.size(); i++) {
                    int value = dataList.get(i);
                    JPanel chanelPanel = controlList.get(i);
                    renderResistanceSpinner(chanelPanel, value);
                }
            });
        }
    }

    private static void renderResistanceSpinner(JPanel chanelPanel, int value) {
        Component[] components = chanelPanel.getComponents();
        for (Component component : components) {
            if (component instanceof SpinnerSlider) {
                SpinnerSlider spinnerSlider = (SpinnerSlider) component;
                spinnerSlider.getSpinner().setValue(value);
                spinnerSlider.getSlider().setValue(value);
            }
        }
    }

    private void loadPWMOutputData() {
        //读取PWM输出板卡所有通道的数据
        OperationResult res = testBoxDevice.fetchPWMOutputBoardCard();
        if (res.isOk()) {
            Map<String, JSONObject> dataMap = (Map<String, JSONObject>) res.getData();
            SwingUtilities.invokeLater(() -> {
                for (Map.Entry<String, JSONObject> entry : dataMap.entrySet()) {
                    int chanel = Integer.parseInt(entry.getKey());
                    PWMEntity pwmEntity = entry.getValue().to(PWMEntity.class);
                    renderPWMOutputSpinner(chanel, pwmEntity);
                }
            });
        }

    }


    private void renderPWMOutputSpinner(int chanel, PWMEntity pwmEntity) {
        float frequency = pwmEntity.getFrequency();
        float dutyCycle = pwmEntity.getDutyCycle();
        Component[] components = pwmOutputGenerator.getControlList().get(chanel - 1).getComponents();
        for (Component component : components) {
            if (component instanceof CompositePanel) {
                CompositePanel compositePanel = (CompositePanel) component;
                compositePanel.getFreqSpinner().setValue((double) frequency);
                compositePanel.getFreqSlider().setValue(Math.round(frequency));
                compositePanel.getDutySpinner().setValue((double) dutyCycle);
                compositePanel.getDutySlider().setValue(Math.round(dutyCycle));
            }
        }
    }


    private void loadRelayData() {
        //读取继电器板卡所有通道的数据
        OperationResult res = testBoxDevice.fetchRelayBoardCard();
        if (res.isOk()) {
            Map<String, Boolean> dataMap = (Map<String, Boolean>) res.getData();
            SwingUtilities.invokeLater(() -> {
                for (Map.Entry<String, Boolean> entry : dataMap.entrySet()) {
                    int channel = Integer.parseInt(entry.getKey());
                    Boolean isOpen = entry.getValue();
                    renderRelayButton(channel, isOpen);
                }
            });
        }
    }


    //TODO:CHANNEL
    private void renderRelayButton(int channel, boolean isOpen) {
        channel = (channel == 2) ? 3 : (channel == 3) ? 2 : channel;
        JPanel panel = relayGenerator.getControlList().get(channel - 1);
        Component[] components = panel.getComponents();
        for (Component component : components) {
            if (component instanceof JButton) {
                JButton button = (JButton) component;
                if (isOpen) {
                    button.setText("闭合");
                    button.setActionCommand("闭合");
                    button.setBackground(Color.GREEN);
                } else {
                    button.setText("断开");
                    button.setActionCommand("断开");
                    button.setBackground(Color.LIGHT_GRAY);
                }
            }
        }
    }


    private void loadTriStateOutputData() {
        //读取三态输出板卡所有通道的数据
        OperationResult res = testBoxDevice.fetchTriStateOutputBoardCard();
        if (res.isOk()) {
            List<Integer> dataList = (List<Integer>) res.getData();
            SwingUtilities.invokeLater(() -> {
                List<JPanel> controlList = triStateOutputGenerator.getControlList();
                for (int i = 0; i < dataList.size(); i++) {
                    int value = dataList.get(i);
                    JPanel chanelPanel = controlList.get(i);
                    renderTriStateOutputSpinner(chanelPanel, value);
                }
            });
        }
    }

    private void renderTriStateOutputSpinner(JPanel chanelPanel, int value) {
        Component[] components = chanelPanel.getComponents();
        for (Component component : components) {
            if (component instanceof TriStateComboBox) {
                TriStateComboBox comboBox = (TriStateComboBox) component;
                comboBox.setSelectedIndex(value);
                comboBox.setBackground(comboBox.getRenderer().getColor(value));
            }
        }
    }

    public void setRelay(int channel, int value) {
        //ACC和KL15在界面上通道号位置变化，底层通道号不变
        channel = (channel == 2) ? 3 : (channel == 3) ? 2 : channel;
        testBoxDevice.writeRelayBoardCard(channel, value);
    }

    public void setResistance(int chanel, int value) {
        testBoxDevice.writeResistanceBoardCard(chanel, value);
    }

    public OperationResult setResistanceInitData(int value) {
        return testBoxDevice.writeResistanceInitData(value);
    }

    public void setPWMOutput(int chanel, PWMEntity pwmEntity) {
        testBoxDevice.writePWMOutputBoardCard(chanel, pwmEntity);
    }

    public void setTriStateOutput(int chanel, int value) {
        testBoxDevice.writeTriStateOutputBoardCard(chanel, value);
    }

    public void initResistanceValue() {
        List<JPanel> controlList = resistanceGenerator.getControlList();
        for (JPanel chanelPanel : controlList) {
            renderResistanceSpinner(chanelPanel, 1000000);
        }
    }
}
