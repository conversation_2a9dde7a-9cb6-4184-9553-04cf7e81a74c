package ui.base.component.TabbedPane;

import javax.swing.*;
import javax.swing.plaf.ComponentUI;
import javax.swing.plaf.basic.BasicTabbedPaneUI;
import java.awt.*;

/**
 * MultifunctionalTabbedPaneUI :重绘Tab边框、Tab边距
 */

public class MultifunctionalTabbedPaneUI extends BasicTabbedPaneUI {
    protected  Color selectedColor;

    @SuppressWarnings({"MethodOverridesStaticMethodOfSuperclass", "UnusedDeclaration"})
    public static ComponentUI createUI(JComponent c) {
        return new MultifunctionalTabbedPaneUI();
    }



    @Override
    protected void paintTabBackground(Graphics g, int tabPlacement, int tabIndex, int x, int y, int w, int h, boolean isSelected) {
        super.paintTabBackground(g, tabPlacement, tabIndex, x, y, w, h, isSelected);
        g.setColor(!isSelected || selectedColor == null ? tabPane.getBackgroundAt(tabIndex)
                : selectedColor);
        tabPane.setBackground(null);
    }

    @Override
    protected void paintTabBorder(Graphics g, int tabPlacement, int tabIndex, int x, int y, int w, int h, boolean isSelected) {
        super.paintTabBorder(g, tabPlacement, tabIndex, x, y, w, h, isSelected);
        g.setColor(new Color(122,138,153));
        g.drawLine(x+1, y+2, x+1, y+h+2);
        g.drawLine(x+1, y+2, x+w-2, y+2);
        if(!isSelected){
            g.drawLine(x+1, y+h-1, x+w-2, y+h-1);
        }
    }
    @Override
    protected void installDefaults() {
        super.installDefaults();
        selectedColor = UIManager.getColor("TabbedPane.selected");
        if (tabInsets != null) tabInsets = new Insets(2, 2, 0, 2);
    }

}