package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import sdk.base.JsonResponse;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.base.operation.OperationTarget;
import sdk.constants.UrlConstants;
import sdk.constants.methods.ImageMethods;
import sdk.domain.Device;
import sdk.domain.complex.TemplatePicture;
import sdk.domain.image.*;
import sdk.entity.interfaces.IVision;

public class DefaultVisionDevice extends Device implements IVision {

    public DefaultVisionDevice() {
    }

    public DefaultVisionDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public JsonResponse<Object> outputDynamicFrame(boolean isOutput) {
        return outputDynamicFrame(getDeviceUniqueCode(), isOutput);
    }

    @Override
    public JsonResponse<Object> outputDynamicFrame(String deviceUUID, boolean isOutput) {
        return defaultPostJsonResponse(UrlConstants.DeviceUrls.StreamUrls.getControlCommandUrl(isOutput ? "dynamic" : "still"),
                deviceUUID,
                new TypeReference<JsonResponse<Object>>() {
                });
    }

    @Override
    public OperationResult testSimilarity(TemplateImageConfig templateImageConfig) {
        return testSimilarity(OperationTarget.ofDevice(this), templateImageConfig);
    }

    public static OperationResult testSimilarity(OperationTarget operationTarget, TemplateImageConfig templateImageConfig) {
        Operation operation =  new Operation();
        operation.setOperationTarget(operationTarget);
        operation.setOperationMethod(ImageMethods.testSimilarity);
        operation.setOperationObject(templateImageConfig);
        return executeOperation(operation);
    }

    @Override
    public OperationResult saveImage(ImageSaveOptions imageSaveOptions) {
        Operation operation = Operation.buildOperation(this);
        operation.setOperationMethod(ImageMethods.saveImage);
        operation.setOperationObject(imageSaveOptions);
        return executeOperation(operation);
    }

    @Override
    public JsonResponse<TemplateImageBo> saveTemplatePicture(TemplateImageBo templatePicture) {
        return defaultPostJsonResponse(UrlConstants.ImageUrls.UPLOAD_TEMPLATE, templatePicture,
                new TypeReference<JsonResponse<TemplateImageBo>>() {
                });
    }
    @Override
    public JsonResponse<TemplateRoiInfo> saveTemplatePictureROI(TemplateRoiInfo templateRoiInfo) {
        return defaultPostJsonResponse(UrlConstants.ImageUrls.UPLOAD_TEMPLATE_ROI, templateRoiInfo,
                new TypeReference<JsonResponse<TemplateRoiInfo>>() {
                });
    }

    @Override
    public JsonResponse<TemplatePicture> queryTemplatePicture(TemplateImageQuery templateImageQuery) {
        return defaultPostJsonResponse(UrlConstants.ImageUrls.QUERY_TEMPLATE,
                templateImageQuery,
                new TypeReference<JsonResponse<TemplatePicture>>() {
                });
    }

}
