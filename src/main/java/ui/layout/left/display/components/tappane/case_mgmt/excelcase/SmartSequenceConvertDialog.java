package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import sdk.base.JsonResponse;
import sdk.entity.JsonConfigKit;
import ui.layout.aichat.UserPromptPanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ItemEvent;
import java.util.HashMap;
import java.util.Map;

@Getter
public class SmartSequenceConvertDialog extends JDialog {
    public final static Map<Integer, String> CONVERT_METHOD = new HashMap<>();
    private final MainModel mainModel;
    private final JTextField hudConfigTextField;
    private final String CONFIG_FILENAME = "hudConfig";
    private final String CONFIG_KEY = "configPath";
    private final static String DESAY_MODEL_NAME = "DesaySV自研大模型";


    static {
        CONVERT_METHOD.put(0, "python");
        CONVERT_METHOD.put(1, "llm");
    }

    private static SmartSequenceConvertDialog instance;
    private final JTabbedPane tabbedPane;
    private JComboBox<String> llmComboBox;
    private boolean confirmed;

    public String getHudConfigFile() {
        return hudConfigTextField.getText();
    }

    public static SmartSequenceConvertDialog getInstance(MainModel mainModel) {
        if (instance == null) {
            instance = new SmartSequenceConvertDialog(mainModel);
        }
        return instance;
    }

    private JPanel pythonConvertPanel() {
        return new JPanel();
    }

    private JPanel aiConvertPanel() {
        JPanel mainBox = new JPanel(new BorderLayout(5, 0));
        llmComboBox = new JComboBox<>();
        llmComboBox.addItem(DESAY_MODEL_NAME);
        llmComboBox.addItem("ICI2&南昌大学合作大模型");
        Box vertialBox = Box.createVerticalBox();
        Box box = Box.createHorizontalBox();
        box.add(new JLabel("生成模型:"));
        box.add(llmComboBox);
        JLabel waitLabel = new JLabel("由IC_ST部门开发，使用AI思考能力。单条案例生成耗时约30秒，请耐心等候。");
        waitLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        JPanel promptPanel = UserPromptPanel.getInstance(mainModel);
        llmComboBox.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                promptPanel.setVisible(e.getItem().equals(DESAY_MODEL_NAME));
                waitLabel.setVisible(e.getItem().equals(DESAY_MODEL_NAME));
            }
        });
        vertialBox.add(box);
        vertialBox.add(waitLabel);
        mainBox.add(vertialBox, BorderLayout.NORTH);
        JPanel dynamicPanel = dynamicPanel();
        Box dynamicBox = Box.createVerticalBox();
        if (dynamicPanel != null) {
            dynamicBox.add(dynamicPanel);
        }
        dynamicBox.add(promptPanel);
        mainBox.add(dynamicBox, BorderLayout.CENTER);
        return mainBox;
    }

    private JPanel dynamicPanel() {
        if (mainModel.getAppInfo().getBuName().toLowerCase().contains("hud")) {
            JPanel panel = new JPanel();
            Box box = Box.createHorizontalBox();
            box.add(new JLabel("HUD配置表:"));
            //增加配置表文件路径上传
            hudConfigTextField.setPreferredSize(new Dimension(550, 100)); //设置文本框大小
            JsonResponse<String> response = JsonConfigKit.getConfig(
                    mainModel.getAppInfo().getProject(), CONFIG_FILENAME);
            if (response.isOk() && response.getData() != null) {
                String data = response.getData();
                JSONObject config = JSONObject.parseObject(data);
                if (config.containsKey(CONFIG_KEY)) {
                    String configPath = (String) config.get(CONFIG_KEY);
                    if (configPath != null && !configPath.isEmpty()) {
                        hudConfigTextField.setText(configPath);
                    }
                }

            }
            box.add(hudConfigTextField);
            JButton button = new JButton("选择文件");
            box.add(button);
            button.addActionListener(e -> {
                JFileChooser fileChooser = new JFileChooser();
                fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
                int returnValue = fileChooser.showOpenDialog(null);
                if (returnValue == JFileChooser.APPROVE_OPTION) {
                    String path = fileChooser.getSelectedFile().getAbsolutePath();
                    Map<String, Object> config = new HashMap<>();
                    config.put(CONFIG_KEY, path);
                    JsonResponse<String> res = JsonConfigKit.saveConfig(
                            mainModel.getAppInfo().getProject(), CONFIG_FILENAME, config);
                    if (res.isOk()) {
                        hudConfigTextField.setText(path);
                    }
                }
            });
            panel.add(box);
            return panel;
        }
        return null;
    }

    public int getLlmType() {
        return llmComboBox.getSelectedIndex();
    }

    public boolean isSelfHostModel() {
        return getLlmType() == 0;
    }

    private SmartSequenceConvertDialog(MainModel mainModel) {
        this.mainModel = mainModel;
        hudConfigTextField = new JTextField();
        setLayout(new BorderLayout());
        setTitle("自动生成动作序列");
        setSize(550, 350);
        confirmed = false;
        setLocationRelativeTo(null);
        tabbedPane = new JTabbedPane();
        tabbedPane.addTab("规则生成", pythonConvertPanel());
        tabbedPane.addTab("AI生成", aiConvertPanel());

        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
        JPanel bottomBox = new JPanel(new GridLayout(2, 1));
        Box horizontalBox = Box.createHorizontalBox();
        JButton convertButton = new JButton("生成动作序列");
        bottomBox.add(horizontalBox);
        bottomBox.add(convertButton);
        tabbedPane.addChangeListener(e -> horizontalBox.setVisible(tabbedPane.getSelectedIndex() == 0));

        convertButton.addActionListener(e -> {
            confirmed = true;
            setVisible(false);
        });
        add(tabbedPane, BorderLayout.CENTER);
        add(bottomBox, BorderLayout.SOUTH);
        tabbedPane.setSelectedIndex(1);
        setModal(true);
    }

    @Override
    public void dispose() {
        confirmed = false;
        super.dispose();
    }

    public SmartSequenceConvertDialog showDialog() {
        setVisible(true);
        return this;
    }

    public String getConvertMethod() {
        return CONVERT_METHOD.get(tabbedPane.getSelectedIndex());
    }
}
