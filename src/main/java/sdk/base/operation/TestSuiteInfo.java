package sdk.base.operation;

import lombok.Builder;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.List;

@Data
@Builder
public class TestSuiteInfo {
    @NotNull
    private String hardwareVersion;
    @NotNull
    private String pcName;
    private int projectId;
    @NotNull
    private String softwareVersion;
    @NotNull
    private String startTime;
    @NotNull
    private List<TestCaseInfo> testCases;
    private int testType;
    @NotNull
    private String testboardVersion;
    @NotNull
    private String testsuiteName;
    private int toolId;
    @NotNull
    private String userId;
    private int checkTotal; //点检总数
    private int testCaseTotalAll; //全功能总数
    private int testCaseTotalReview; //自动化总数
}
