package ui.base;

import common.exceptions.JsonAssembleException;
import sdk.base.operation.Operation;

import java.awt.*;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

public interface OperationAssembler<T> {
    default Class<?> getInterface() {
        Type[] types = getClass().getGenericInterfaces();
        ParameterizedType parameterizedType = (ParameterizedType) types[0];
        Type type = parameterizedType.getActualTypeArguments()[0];
        return checkType(type);
    }

    default Class<?> checkType(Type type) {
        if (type instanceof Class<?>) {
            return (Class<?>) type;
        } else if (type instanceof ParameterizedType) {
            ParameterizedType pt = (ParameterizedType) type;
            Type t = pt.getActualTypeArguments()[0];
            return checkType(t);
        } else {
            throw new IllegalArgumentException(String.format("Expected class %s", type.getClass().getName()));
        }
    }

    void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException;

    Operation assembleOperation(Operation injectedOperation);

    default T assembleOperationObject() {
        return null;
    }

    /**
     * 检查输入参数
     */
    boolean checkUserInput();

    /**
     * 获取弹出面板
     *
     * @return 弹出面板
     */
    default Component getAssembleContainer() {
        return (Component) this;
    }

    default Dimension getPerfectDimension() {
        return null;
    }

}
