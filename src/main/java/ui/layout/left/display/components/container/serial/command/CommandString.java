package ui.layout.left.display.components.container.serial.command;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 命令字符串对象，用于存储每行命令字符串的属性，其中包括顺序、命令释义、延时、命令类型、命令字符串
 * 其中有一个有参构造器和无参构造器，有参构造器用于读取存储SSCOM配置文件时传入的命令字符串属性；
 * 无参构造器用于新建命令行文件，其中设置了顺序与延时的默认值
 */
@Data
public class CommandString {
    private Map<String, String> sequence;//顺序
    private String commandInterpretation;//命令释义
    private String delayTime;//延时
    private Map<String, String> commandType;//命令类型
    private String command;//命令字符串
    private static final int ONE = 1;//用于左闭右开取值索引填充

    public CommandString(String s) {
        int semicolon = s.indexOf(";");
        int equalMarkLeft = s.indexOf("=");
        int equalMarkRight = s.indexOf("=", equalMarkLeft + ONE);
        int commaLeft = s.indexOf(",");
        int commaMiddle = s.indexOf(",", commaLeft + ONE);
        int commaRight = s.indexOf(",", commaMiddle + ONE);
        sequence = new HashMap<>();
        commandType = new HashMap<>();
        sequence.put(s.substring(0, equalMarkLeft), s.substring(equalMarkLeft + ONE, commaLeft));
        commandInterpretation = s.substring(commaLeft + ONE, commaMiddle);
        delayTime = s.substring(commaMiddle + ONE, semicolon);
        commandType.put(s.substring(semicolon + ONE, equalMarkRight), s.substring(equalMarkRight + ONE, commaRight));
        command = s.substring(commaRight + ONE);
    }

    public CommandString() {
        sequence = new HashMap<>();
        sequence.put("", "0");//默认顺序为“0”
        delayTime = "1000";//默认延迟为“1000”
    }
}

