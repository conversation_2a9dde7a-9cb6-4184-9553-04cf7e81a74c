package ui.layout.left.display.components.container.screen.touchPoint;

import cn.hutool.core.util.NumberUtil;
import com.jgoodies.forms.factories.CC;
import com.jgoodies.forms.layout.FormLayout;
import common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Setter;
import sdk.domain.screen.GestureCode;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import sdk.entity.ScreenKit;
import ui.base.BaseView;
import ui.layout.left.display.dialogs.ConfirmDialog;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.*;

/**
 * 触摸报点矩阵表格
 */
public class TouchMatrixTable extends JTable implements BaseView {
    public static final String X_POS = "X";
    public static final String Y_POS = "Y";
    public static final String GESTURE_CODE = "GestureCode";
    private static final CellSelectionSet cellSelectionSet = new CellSelectionSet();
    private final ScreenKit screenKitManager = OperationTargetHolder.getScreenKit();
    private final ScreenConfig screenConfig;
    private final JPopupMenu popupMenu;
    private final Cell selectCell;

    private int dataLength;

    @Setter
    private DataChangeListener dataChangeListener;

    public TouchMatrixTable(ScreenConfig screenConfig) {
        this.screenConfig = screenConfig;
        popupMenu = new JPopupMenu();
        selectCell = new Cell(0, 0);
        createView();
        createActions();
    }

    public interface DataChangeListener {

        void dataChanged(String hexString);

    }

    @Data
    private static class CellSelectionSet {
        public static final int BIT_SIZE = 8;

        private final Set<Integer> colorSet = new HashSet<>();

        private final List<Cell> cells = new ArrayList<>();

        private Map<String, ColorCellCollection> continuesCellsMap = new HashMap<>();

        public void setContinuesCellsMapFromConfig(Map<String, ColorCellCollection> map) {
            if (map != null && !map.isEmpty()) {
                continuesCellsMap.clear();
                continuesCellsMap.putAll(map);
                for (ColorCellCollection collection : map.values()) {
                    colorSet.add(collection.getColor().getRGB());
                }
            }
        }

        public ColorCellCollection findColorCellCollectionByIndex(int row) {
            for (Map.Entry<String, ColorCellCollection> entry : new HashSet<>(continuesCellsMap.entrySet())) {
                ColorCellCollection colorCellCollection = entry.getValue();
                for (Cell cell : colorCellCollection.getCells()) {
                    if (cell.getRow() == row) {
                        return colorCellCollection;
                    }
                }
            }
            return null;
        }

        public ColorCellCollection findColorCellCollectionByIndex(int row, int column, boolean remove) {
            for (Map.Entry<String, ColorCellCollection> entry : new HashSet<>(continuesCellsMap.entrySet())) {
                ColorCellCollection colorCellCollection = entry.getValue();
                for (Cell cell : colorCellCollection.getCells()) {
                    if (cell.getRow() == row && cell.getColumn() == column) {
                        if (remove) {
                            continuesCellsMap.remove(entry.getKey());
                        }
                        return colorCellCollection;
                    }
                }
            }
            return null;
        }

        public ColorCellCollection getColorCellCollection(int row, int column) {
            for (ColorCellCollection colorCellCollection : continuesCellsMap.values()) {
                for (Cell cell : colorCellCollection.getCells()) {
                    if (cell.getRow() == row && cell.getColumn() == column) {
                        return colorCellCollection;
                    }
                }
            }
            return null;
        }

        private Color randomColor() {
            while (true) {
                float hue = (float) Math.random();
                int rgb = Color.HSBtoRGB(hue, 0.5f, 0.5f);
                Color color = new Color(rgb);
                if (colorSet.contains(rgb)) {
                    continue;
                }
                colorSet.add(rgb);
                return color;
            }
        }

        public void remove(String cellsLabel) {
            continuesCellsMap.remove(cellsLabel);
        }

        public boolean isInterlock() {
            for (ColorCellCollection collection : continuesCellsMap.values()) {
                List<Cell> cellList = collection.getCells();
                for (Cell cell : cells) {
                    if (cellList.contains(cell)) {
                        return true;
                    }
                }
            }
            return false;
        }

        public void assign(String cellsLabel, int row, int columnCount) {
            ColorCellCollection colorCellCollection = cellSelectionSet.continuesCellsMap.get(String.valueOf(row));
            if (colorCellCollection == null) {
                colorCellCollection = new ColorCellCollection();
                colorCellCollection.setColor(randomColor());
                List<Cell> cellList = new ArrayList<>();
                for (int col = 0; col < columnCount; col++) {
                    cellList.add(new Cell(row, col));
                }
                colorCellCollection.setCells(cellList);
                continuesCellsMap.put(String.valueOf(row), colorCellCollection);
            }
            colorCellCollection.setText(cellsLabel);
        }

        public boolean assign(String cellsLabel) {
            if (continuesCellsMap.containsKey(cellsLabel)) {
                return false;
            }
            ColorCellCollection colorCellCollection = new ColorCellCollection();
            colorCellCollection.setText(cellsLabel);
            colorCellCollection.setColor(randomColor());
            colorCellCollection.setCells(new ArrayList<>(cells));
            continuesCellsMap.putIfAbsent(cellsLabel, colorCellCollection);
            return true;
        }

        public boolean isValid() {
            return !cells.isEmpty();
        }

        public boolean isContinuousCells() {
            cells.sort((o1, o2) -> {
                int result = Integer.compare(o1.getRow(), o2.getRow());
                if (result == 0) {
                    return Integer.compare(o1.getColumn(), o2.getColumn());
                }
                return result;
            });
            Cell prevCell = cells.get(0);
            for (int i = 1; i < cells.size(); i++) {
                Cell nextCell = cells.get(i);
                int preRow = prevCell.getRow();
                int nextRow = nextCell.getRow();
                int prevColumn = prevCell.getColumn();
                int nextColumn = nextCell.getColumn();
                if (nextColumn == 0) {
                    //第一列，判断前一个单元格是不是最后一个&上一行
                    if (prevColumn != BIT_SIZE - 1 || nextRow - preRow != 1) {
                        //不是连续区域
                        return false;
                    }
                } else {
                    //非第一列，判断和前一个单元格是不是相邻
                    if (nextRow != preRow || nextColumn - prevColumn != 1) {
                        //不是连续区域
                        return false;
                    }
                }
                prevCell = nextCell;
            }
            return true;
        }

        public void add(int r, int c) {
            if (!contains(r, c)) {
                cells.add(new Cell(r, c));
            }
        }

        public boolean containsOneOrLess() {
            return cells.size() <= 1;
        }

        public boolean contains(int r, int c) {
            for (Cell cell : cells) {
                if (cell.is(r, c)) {
                    return true;
                }
            }
            return false;
        }

        public void clear() {
            cells.clear();
        }

        public void clearAll() {
            cells.clear();
            colorSet.clear();
            continuesCellsMap.clear();
        }

    }

    @Override
    public void createView() {
        setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component component = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
                ColorCellCollection colorCellCollection = cellSelectionSet.getColorCellCollection(row, column);
                if (colorCellCollection != null) {
                    component.setBackground(colorCellCollection.getColor());
                    component.setForeground(Color.WHITE);
                } else {
                    if (isSelected) {
                        component.setBackground(table.getSelectionBackground());
                    } else {
                        component.setBackground(table.getBackground());
                    }
                }
                return component;
            }
        });
        setShowGrid(true);
        setModel(new DefaultTableModel(new Integer[]{7, 6, 5, 4, 3, 2, 1, 0}, 0));
        setCellSelectionEnabled(true);
        setRowHeight(30);
        makePopupMenu("定义该行(字节)为十六进制常量", new AddConstantActionListener(this, selectCell));
        makePopupMenu("定义为X坐标", new AddLabelActionListener(this, X_POS));
        makePopupMenu("定义为Y坐标", new AddLabelActionListener(this, Y_POS));
        makePopupMenu("定义触摸事件", new AddGestureCodeLabelActionListener(this, GESTURE_CODE));
//        makePopupMenu("定义为指定参数", new AddCustomizeParamLabelActionListener());
        makePopupMenu("取消当前定义", new CancelLabelActionListener(this, selectCell));
    }

    @Override
    public void createActions() {
        addMouseListener(new MyMouseAdapter(popupMenu, selectCell));
    }

    @Override
    public void restoreView() {
        if (screenConfig.getTouchPointMatrix().maxRow() > screenConfig.getTouchPointMatrix().getDataLength()) {
            screenConfig.getTouchPointMatrix().getUserTouchDataDefinition().clear();
            screenKitManager.updateConfig(screenConfig);
            return;
        }
        Map<String, ColorCellCollection> cellDefinitions = screenConfig.getTouchPointMatrix().getUserTouchDataDefinition();
        if (cellDefinitions != null && !cellDefinitions.isEmpty()) {
            cellSelectionSet.setContinuesCellsMapFromConfig(cellDefinitions);
            for (ColorCellCollection colorCellCollection : cellSelectionSet.continuesCellsMap.values()) {
                setLabelText(colorCellCollection, colorCellCollection.getText());
            }
            fireDataChange();
        }
    }

    private void setLabelText(ColorCellCollection collection, String label) {
        Cell cell = collection.firstCell();
        if (cell != null) {
            setValueAt(label, cell.getRow(), cell.getColumn());
            clearSelection();
            repaint();
        }
    }

    private static class MyMouseAdapter extends MouseAdapter {
        private final JPopupMenu popupMenu;
        private final Cell selectCell;

        public MyMouseAdapter(JPopupMenu popupMenu, Cell selectCell) {
            this.popupMenu = popupMenu;
            this.selectCell = selectCell;
        }

        @Override
        public void mouseReleased(MouseEvent e) {
            if (e.isPopupTrigger()) { // 检查是否是弹出触发
                if (SwingUtilities.isRightMouseButton(e)) {
                    JTable table = (JTable) e.getSource();
                    final int row = table.rowAtPoint(e.getPoint());    //获取行
                    final int column = table.columnAtPoint(e.getPoint());  //获取列
                    if (row >= 0 && row < table.getRowCount() && column >= 0) {
                        selectCell.setRow(row);
                        selectCell.setColumn(column);
                        popupMenu.show(e.getComponent(), e.getX(), e.getY());
                    }
                }
            }
        }
    }


    private abstract class LabelManagerActionListener implements ActionListener {
        private final JTable table;

        public LabelManagerActionListener(JTable table) {
            this.table = table;
        }

        private void saveConfig() {
            screenConfig.getTouchPointMatrix().setUserTouchDataDefinition(cellSelectionSet.continuesCellsMap);
            screenKitManager.updateConfig(screenConfig);
        }

        protected void removeLabel(Cell selectCell) {
            int row = selectCell.getRow();
            int column = selectCell.getColumn();
//            System.out.printf("select:(%d,%d)%n", row, column);
            ColorCellCollection collection = cellSelectionSet.findColorCellCollectionByIndex(row, column, true);
            if (collection != null) {
                if (collection.getText().equals(GESTURE_CODE)) {
                    screenConfig.getTouchPointMatrix().setGestureCode(null);
                }
                fireDataChange();
                setLabelText(collection, "");
                saveConfig();
            }
        }

        protected void addHexConstant(String hexConstant, int byteOrder) {
            addLabel(String.format("0x%s", hexConstant), byteOrder);
        }

        protected void addLabel(String bitsIdentifierKey) {
            addLabel(bitsIdentifierKey, null);
        }

        public boolean checkValid() {
            if (!cellSelectionSet.isValid()) {
                SwingUtil.showWarningDialog(TouchMatrixTable.this, "请选择区域");
                return false;
            }
            if (!cellSelectionSet.isContinuousCells()) {
                SwingUtil.showWarningDialog(TouchMatrixTable.this, "非连续区域");
                return false;
            }
            if (cellSelectionSet.isInterlock()) {
                SwingUtil.showWarningDialog(TouchMatrixTable.this, "存在已定义区域");
                return false;
            }
            return true;
        }

        protected void addLabel(String bitsIdentifierKey, Integer row) {
            ColorCellCollection colorCellCollection;
            if (row == null) {
                if (!checkValid()) {
                    return;
                }
                if (!cellSelectionSet.assign(bitsIdentifierKey)) {
                    SwingUtil.showWarningDialog(TouchMatrixTable.this, String.format("%s标签已存在", bitsIdentifierKey));
                    return;
                }
                colorCellCollection = cellSelectionSet.continuesCellsMap.get(bitsIdentifierKey);
            } else {
                //hex constant
                cellSelectionSet.assign(bitsIdentifierKey, row, table.getColumnCount());
                colorCellCollection = cellSelectionSet.continuesCellsMap.get(String.valueOf(row));
            }
            fireDataChange();
            setLabelText(colorCellCollection, bitsIdentifierKey);
            saveConfig();
        }
    }

    private void fireDataChange() {
        if (dataChangeListener != null) {
            dataChangeListener.dataChanged(getFriendlyHexString());
        }
    }

    private class CancelLabelActionListener extends LabelManagerActionListener implements ActionListener {
        private final Cell selectCell;

        public CancelLabelActionListener(JTable table, Cell selectCell) {
            super(table);
            this.selectCell = selectCell;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            removeLabel(selectCell);
        }

    }

    private class AddCustomizeParamLabelActionListener extends LabelManagerActionListener implements ActionListener {

        public AddCustomizeParamLabelActionListener(JTable table) {
            super(table);
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            String params = JOptionPane.showInputDialog("定义为自定义参数:");
            if (params != null) {
                addLabel(params);
            }
        }

    }

    private static class GestureCodeConfigDialog extends ConfirmDialog {
        private final JTextField pressTextField;
        private final JTextField dragTextField;
        private final JTextField releaseTextField;
        private final ScreenConfig screenConfig;

        public GestureCodeConfigDialog(ScreenConfig screenConfig) {
            this.screenConfig = screenConfig;
            setModal(true);
            setTitle("触摸事件设置");
            setLayout(new BorderLayout());
            setSize(450, 350);
            setLocationRelativeTo(null);
            pressTextField = new JTextField(8);
            dragTextField = new JTextField(8);
            releaseTextField = new JTextField(8);
            createView();
            createActions();
            restoreView();
        }

        @Override
        public void restoreView() {
            GestureCode gestureCode = screenConfig.getTouchPointMatrix().getGestureCode();
            if (gestureCode != null) {
                pressTextField.setText(String.format("%02X", gestureCode.getPressHexCode()));
                dragTextField.setText(String.format("%02X", gestureCode.getDragHexCode()));
                releaseTextField.setText(String.format("%02X", gestureCode.getReleaseHexCode()));
            }
        }

        public GestureCode getGestureCode() {
            GestureCode gestureCode = new GestureCode();
            gestureCode.setPressHexCode(Integer.parseInt(pressTextField.getText(), 16));
            gestureCode.setDragHexCode(Integer.parseInt(dragTextField.getText(), 16));
            gestureCode.setReleaseHexCode(Integer.parseInt(releaseTextField.getText(), 16));
            return gestureCode;
        }

        @Override
        protected boolean performConfirm() {
            if (pressTextField.getText().trim().isEmpty() || dragTextField.getText().trim().isEmpty() || releaseTextField.getText().trim().isEmpty()) {
                return false;
            }
            return super.performConfirm();
        }

        @Override
        public Component makeCenterPanel() {
            FormLayout formLayout = new FormLayout("pref, 10px, 15px, pref", "30px, 40px, 40px, 40px, 60px");
            JPanel panel = new JPanel(formLayout);
            panel.add(new JLabel("触摸事件名"), CC.xy(1, 1));
            panel.add(new JLabel("触摸代码"), CC.xy(4, 1));

            panel.add(new JLabel("按下(PRESS):"), CC.xy(1, 2));
            panel.add(new JLabel("0x"), CC.xy(3, 2));
            panel.add(pressTextField, CC.xy(4, 2));

            panel.add(new JLabel("拖动(DRAG):"), CC.xy(1, 3));
            panel.add(new JLabel("0x"), CC.xy(3, 3));
            panel.add(dragTextField, CC.xy(4, 3));

            panel.add(new JLabel("释放(RELEASE):"), CC.xy(1, 4));
            panel.add(new JLabel("0x"), CC.xy(3, 4));
            panel.add(releaseTextField, CC.xy(4, 4));

            JPanel centerPanel = new JPanel(new GridBagLayout());
            centerPanel.add(panel, new GridBagConstraints());
            return centerPanel;
        }

    }

    private class AddGestureCodeLabelActionListener extends AddLabelActionListener {

        public AddGestureCodeLabelActionListener(JTable table, String bitsIdentifierKey) {
            super(table, bitsIdentifierKey);
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            if (checkValid()) {
                GestureCodeConfigDialog configDialog = new GestureCodeConfigDialog(screenConfig);
                configDialog.setVisible(true);
                if (configDialog.isConfirmed()) {
                    GestureCode gestureCode = configDialog.getGestureCode();
                    screenConfig.getTouchPointMatrix().setGestureCode(gestureCode);
                    screenKitManager.updateConfig(screenConfig);
                    super.actionPerformed(e);
                }
            }
        }

    }

    private class AddConstantActionListener extends LabelManagerActionListener {
        private final Cell selectCell;

        public AddConstantActionListener(JTable table, Cell selectCell) {
            super(table);
            this.selectCell = selectCell;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            ColorCellCollection collection = cellSelectionSet.findColorCellCollectionByIndex(selectCell.getRow());
            if (collection != null && !NumberUtil.isNumber(collection.getText())) {
                SwingUtil.showWarningDialog(TouchMatrixTable.this, "存在已定义特殊含义区域,请先取消定义");
            } else {
                String hexConstantString = SwingUtil.showHexInputDialog((Component) e.getSource());
                if (!StringUtils.isBlank(hexConstantString)) {
                    addHexConstant(hexConstantString, selectCell.getRow());
                }
            }

        }
    }


    private class AddLabelActionListener extends LabelManagerActionListener {

        private final String bitsIdentifierKey;

        public AddLabelActionListener(JTable table, String bitsIdentifierKey) {
            super(table);
            this.bitsIdentifierKey = bitsIdentifierKey;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            addLabel(bitsIdentifierKey);
        }
    }

    public void changeDataLength(int dataLength) {
        this.dataLength = dataLength;
        cellSelectionSet.clearAll();
        DefaultTableModel model = ((DefaultTableModel) getModel());
        model.getDataVector().clear();
        model.setRowCount(dataLength);
        clearSelection();
        repaint();
        fireDataChange();
    }

    @Override
    public boolean isCellEditable(int row, int column) {
        return false;
    }

    @Override
    public void changeSelection(int rowIndex, int columnIndex, boolean toggle, boolean extend) {
        super.changeSelection(rowIndex, columnIndex, toggle, extend);
        if (toggle) {
            cellSelectionSet.add(rowIndex, columnIndex);
        } else {
            if (extend) {
                cellSelectionSet.add(rowIndex, columnIndex);
            } else {
                // reset
                clearSelection();
                repaint();
                cellSelectionSet.clear();
                cellSelectionSet.add(rowIndex, columnIndex);
            }
        }
    }

    @Override
    public boolean isCellSelected(int row, int column) {
//        if (cellSelectionSet.containsOneOrLess()) {
//            // show the default
//            return super.isCellSelected(row, column);
//        }
        return cellSelectionSet.contains(row, column);
    }

    private void makePopupMenu(String menuItemName, ActionListener actionListener) {
        JMenuItem item = new JMenuItem(menuItemName);
        item.addActionListener(actionListener);
        popupMenu.add(item);
    }

    @Data
    @AllArgsConstructor
    private static class TextCell {
        private Cell cell;
        private String text;
    }

    private void addToCellList(List<TextCell> cellList, String label) {
        addToCellList(cellList, label, label);
    }

    private void addToCellList(List<TextCell> cellList, String label, String hexInnerCode) {
        ColorCellCollection xColorCellCollection = cellSelectionSet.continuesCellsMap.get(label);
        if (xColorCellCollection != null) {
            for (Cell cell : xColorCellCollection.getCells()) {
                cellList.add(new TextCell(cell, hexInnerCode));
            }
        }
    }

    private Map<Integer, String> getCustomizeLabelMap() {
        List<TextCell> cellList = new ArrayList<>();
        addToCellList(cellList, X_POS);
        addToCellList(cellList, Y_POS);
        addToCellList(cellList, GESTURE_CODE, "G");
        cellList.sort(Comparator.comparingInt(o -> o.cell.getRow() + o.cell.getColumn()));
        Map<Integer, String> map = new HashMap<>();
        for (TextCell textCell : cellList) {
            int row = textCell.getCell().getRow();
            map.merge(row, textCell.getText(), (a, b) -> {
                if (a.contains(b)) {
                    return a;
                } else {
                    return a + b;
                }
            });
        }
        return map;
    }

    public String getFriendlyHexString() {
        StringBuilder sb = new StringBuilder();
        Map<Integer, String> labelMap = getCustomizeLabelMap();
        for (int row = 0; row < dataLength; row++) {
            ColorCellCollection colorCellCollection = cellSelectionSet.continuesCellsMap.get(String.valueOf(row));
            String label = labelMap.get(row);
            if (label != null) {
                if (label.length() == 1) {
                    sb.append(label).append("#").append(" ");
                } else {
                    sb.append(label).append(" ");
                }
            } else {
                if (colorCellCollection != null) {
                    sb.append(colorCellCollection.getText().replace("0x", "")).append(" ");
                } else {
                    sb.append("##").append(" ");
                }
            }
        }
        return sb.toString().trim();
    }

}
