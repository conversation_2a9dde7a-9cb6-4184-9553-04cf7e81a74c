package ui.layout.left.display.components.container.can;

import ui.base.BaseView;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.Timer;
import java.util.TimerTask;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CAN报文数据编辑对话框
 */
public class CanMessageDataEditDialog extends JDialog implements BaseView {
    private final int dataLength;
    private final String[] data;
    private StringBuilder dataShowBuilder;
    private final JTextField dataTextField;
    private boolean isPressCtrl = false;

    public CanMessageDataEditDialog(JComponent owner, Integer dataLength, JTextField dataTextField) {
        dataShowBuilder = new StringBuilder();
        this.dataLength = dataLength;
        this.dataTextField = dataTextField;
        data = new String[dataLength];
        for (int i = 0; i < dataLength; i++) {
            data[i] = "00";
        }
        createView();
    }

    @Override
    public void createView() {
        setTitle("数据编辑");
        setSize(new Dimension(530, 600));
        setResizable(false);
        setModal(true);
        JPanel containerPanel = new JPanel();
        containerPanel.setLayout(null);
        containerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JPanel panelInMatrixScrollPane = new JPanel();
        panelInMatrixScrollPane.setLayout(new BorderLayout(0, 0));
        JScrollPane matrixScrollPane = new JScrollPane(panelInMatrixScrollPane);
        matrixScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        matrixScrollPane.setBounds(15, 15, 480, 485);

        JLabel dataLengthLabel = new JLabel("共有 " + dataLength + " 个字节");
        dataLengthLabel.setFont(new Font(null, Font.PLAIN, 14));
        dataLengthLabel.setBounds(15, 510, 100, 20);

        JButton confirmBtnToCloseThisDialog = new JButton("确定");
        confirmBtnToCloseThisDialog.setBounds(200, 508, 120, 30);
        confirmBtnToCloseThisDialog.setBackground(new Color(176, 213, 247));

        containerPanel.add(matrixScrollPane);
        containerPanel.add(dataLengthLabel);
        containerPanel.add(confirmBtnToCloseThisDialog);
        //列标题panel
        JPanel columnTitlePanel = new JPanel();
        columnTitlePanel.setPreferredSize(new Dimension(440, 60));
        //数据编辑列标题内容
        columnTitlePanel.setLayout(new GridLayout(1, 11));
        columnTitlePanel.setBackground(new Color(208, 224, 227));

        panelInMatrixScrollPane.add(columnTitlePanel, BorderLayout.NORTH);
        panelInMatrixScrollPane.add(columnTitlePanel, BorderLayout.NORTH);

        int columnLength = 11;
        JLabel[] columnLabels = new JLabel[columnLength];
        columnLabels[0] = new JLabel("");
        columnLabels[columnLength - 1] = new JLabel("<html><body>&nbsp&nbsp十<br>进制</body></html>");
        columnLabels[columnLength - 2] = new JLabel("<html><body>十六<br>进制</body></html>");
        for (int i = 1; i < columnLabels.length - 2; i++) {
            columnLabels[i] = new JLabel(Integer.toString(8 - i));
        }

        for (JLabel colLabel : columnLabels) {
            colLabel.setHorizontalAlignment(SwingConstants.CENTER);
            colLabel.setFont(new Font(null, Font.PLAIN, 14));
            colLabel.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
                    super.mouseClicked(e);
                    if (e.getButton() == MouseEvent.BUTTON1) {
                        for (JLabel jLabel : columnLabels) {
                            jLabel.setFont(new Font(null, Font.PLAIN, 14));
                        }
                        colLabel.setFont(new Font(null, Font.BOLD, 14));
                        // System.out.println(thisLabels.getText());
                    }
                }
            });
            columnTitlePanel.add(colLabel);
            // 数据编辑主要内容区域
            int boxHeight = 53;
            JPanel dataControlsPanel = new JPanel();
            dataControlsPanel.setMinimumSize(new Dimension(440, boxHeight * dataLength));
            dataControlsPanel.setLayout(new BorderLayout());

            JPanel rowsTitlePanel = new JPanel();
            rowsTitlePanel.setPreferredSize(new Dimension(40, boxHeight * dataLength));
            rowsTitlePanel.setLayout(null);

            JPanel buttonsPanel = new JPanel();
            JPanel textFieldsPanel = new JPanel();
            textFieldsPanel.setPreferredSize(new Dimension(90, 420));

            dataControlsPanel.add(rowsTitlePanel, BorderLayout.WEST);
            panelInMatrixScrollPane.add(dataControlsPanel, BorderLayout.CENTER);

            String[] dataForTextFields = dataTextField.getText().split(" ");
            // 数据编辑面板左侧
            JLabel[] rowLabels = new JLabel[dataLength];
            for (int k = 0; k < rowLabels.length; k++) {
                rowLabels[k] = new JLabel();
                rowLabels[k].setFont(new Font(null, Font.PLAIN, 14));
                rowLabels[k].setText(Integer.toString(k + 1));
                rowLabels[k].setHorizontalAlignment(SwingConstants.CENTER);
                rowLabels[k].setBorder(BorderFactory.createLineBorder(new Color(217, 217, 217)));
                JLabel thisLabels = rowLabels[k];
                rowLabels[k].addMouseListener(new MouseAdapter() {
                    @Override
                    public void mouseClicked(MouseEvent e) {
                        super.mouseClicked(e);
                        for (JLabel rlabel : rowLabels) {
                            rlabel.setOpaque(false);
                            rlabel.repaint();
                        }
                        thisLabels.setOpaque(true);
                        thisLabels.setBackground(new Color(176, 213, 247));
                    }
                });
                rowsTitlePanel.add(rowLabels[k]);
                rowLabels[k].setBounds(0, boxHeight * k, 40, boxHeight);
            }
            dataControlsPanel.add(buttonsPanel, BorderLayout.CENTER);
            dataControlsPanel.add(textFieldsPanel, BorderLayout.EAST);
            // 按钮面板
            buttonsPanel.setLayout(null);
            JPanel[] panelToIncludeButtonsPanel = new JPanel[dataLength];
            // 数据编辑 十六进制 十进制
            TextFieldWithIndex[][] textFields = new TextFieldWithIndex[dataLength][2];
            JPanel[] panelToIncludeTextFieldPanel = new JPanel[dataLength];
            textFieldsPanel.setLayout(null);
//            System.out.println("begin loop=" + index++);
            if (dataForTextFields.length < dataLength) {
                continue;
            }
            for (int k = 0; k < dataLength; k++) {
                int fieldIndex = k;
                panelToIncludeTextFieldPanel[k] = new JPanel();
                panelToIncludeTextFieldPanel[k].setLayout(new GridLayout(1, 2));
                for (int j = 0; j < textFields[k].length; j++) {
                    textFields[k][j] = new TextFieldWithIndex(fieldIndex, j);
                    if (dataTextField.getText().isEmpty()) {
                        textFields[k][j].setText("00");
                    } else {
                        if (j == 0) {
//                            System.out.println("dataForTextFields:" + Arrays.toString(dataForTextFields) + ",k=" + k);
                            textFields[k][j].setText(dataForTextFields[k]);
                        }
                        if (j == 1) {
                            // 十六进制转10进制
                            int decimalNum = Integer.valueOf((dataForTextFields[k].isEmpty()) ? "0" : dataForTextFields[k], 16);
                            textFields[k][j].setText(String.valueOf(decimalNum).length() == 1 ? "0" + decimalNum : String.valueOf(decimalNum));
                        }
                    }

                    textFields[k][j].setFont(new Font(null, Font.PLAIN, 14));
                    textFields[k][j].setHorizontalAlignment(JTextField.CENTER);
                    panelToIncludeTextFieldPanel[k].add(textFields[k][j]);
                    TextFieldWithIndex thisTextField = textFields[k][j];
                    textFields[k][j].addKeyListener(new KeyAdapter() {
                        int count;

                        @Override
                        public void keyTyped(KeyEvent e) {
                            super.keyTyped(e);
                            boolean limitNum = (e.getKeyChar() >= KeyEvent.VK_0 && e.getKeyChar() <= KeyEvent.VK_9);
                            boolean limitLetter = ((int) e.getKeyChar() >= 65 && (int) e.getKeyChar() <= 70) || ((int) e.getKeyChar() >= 97 && (int) e.getKeyChar() <= 102);
                            if (thisTextField.getCol() == 0) {
                                if (!(limitNum || (e.getKeyChar() == KeyEvent.VK_BACK_SPACE) || limitLetter)) {
                                    e.consume();
                                }
                                java.util.Timer timer = new java.util.Timer();
                                timer.schedule(new TimerTask() {
                                    @Override
                                    public void run() {
                                        if (thisTextField.getText().length() > 2) {
                                            thisTextField.setText(thisTextField.getText().substring(0, 2));
                                        }
                                    }
                                }, 5);
                                timer.schedule(new TimerTask() {
                                    @Override
                                    public void run() {
                                        String reg = "[^\\dA-Fa-f]";
                                        Matcher matcher = Pattern.compile(reg).matcher(thisTextField.getText());
                                        isPressCtrl = false;
                                        while (matcher.find()) {
                                            isPressCtrl = true;
                                        }
                                        if (!isPressCtrl) {
                                            int decimalNum = Integer.valueOf((thisTextField.getText().isEmpty()) ? "0" : thisTextField.getText(), 16);//16����ת��Ϊ10����
                                            String binaryNum = Integer.toBinaryString(decimalNum);
                                            binaryNum = String.format("%8s", binaryNum).replace(" ", "0");
                                            if (decimalNum < 10) {
                                                textFields[fieldIndex][1].setText("0" + decimalNum);
                                            } else {
                                                textFields[fieldIndex][1].setText(String.valueOf(decimalNum));
                                                for (int i = 0; i < count + 1; i++) {
                                                    textFields[fieldIndex][1].setText(String.valueOf(decimalNum));
                                                }
                                            }
                                            thisTextField.setBinaryToBtns(binaryNum);

                                        }
                                    }
                                }, 5);
                            }
                            if (thisTextField.getCol() == 1) {
                                if (!(limitNum || (e.getKeyChar() == KeyEvent.VK_BACK_SPACE))) {
                                    e.consume();
                                }
                                java.util.Timer timer = new Timer();
                                timer.schedule(new TimerTask() {
                                    @Override
                                    public void run() {
                                        String reg = "\\D";
                                        Matcher matcher = Pattern.compile(reg).matcher(thisTextField.getText());
                                        isPressCtrl = false;
                                        while (matcher.find()) {
                                            isPressCtrl = true;
                                        }
                                        if (!isPressCtrl) {
                                            String binaryNum = Integer.toBinaryString(Integer.parseInt((thisTextField.getText().isEmpty()) ? "00" : thisTextField.getText()));
                                            binaryNum = String.format("%8s", binaryNum).replace(" ", "0");
                                            String hexadecimalNum = Long.toHexString(Long.parseLong((thisTextField.getText().isEmpty()) ? "00" : thisTextField.getText(), 10));
                                            textFields[fieldIndex][0].setText(hexadecimalNum.length() == 1 ? "0" + hexadecimalNum.toUpperCase() : hexadecimalNum.toUpperCase());
                                            if (hexadecimalNum.length() == 1) {
                                                textFields[fieldIndex][0].setText("0" + hexadecimalNum.toUpperCase());
                                            } else {
                                                textFields[fieldIndex][0].setText(hexadecimalNum.toUpperCase());
                                                for (int i = 0; i < count + 1; i++) {
                                                    textFields[fieldIndex][0].setText(hexadecimalNum.toUpperCase());
                                                }
                                            }
                                            thisTextField.setBinaryToBtns(binaryNum);
                                        }
                                        if (thisTextField.getText().length() >= 3) {
                                            thisTextField.setText(thisTextField.getText().substring(0, 3).trim().isEmpty() ? "0" : thisTextField.getText().substring(0, 3).trim());
                                            if (thisTextField.getText().length() == 3) {
                                                if (thisTextField.getText().charAt(0) == '0') {
                                                    thisTextField.setText(thisTextField.getText().substring(1, 3));
                                                }
                                            }
                                            if (!isPressCtrl) {
                                                thisTextField.setText(Integer.parseInt(thisTextField.getText()) > 255 ? "255" : thisTextField.getText());
                                                String binaryNum = Integer.toBinaryString(Integer.parseInt((thisTextField.getText().isEmpty()) ? "00" : thisTextField.getText()));//��xת��Ϊ������
                                                binaryNum = String.format("%8s", binaryNum).replace(" ", "0");
                                                String hexadecimalNum = Long.toHexString(Long.parseLong((thisTextField.getText().isEmpty()) ? "00" : thisTextField.getText(), 10));
                                                // System.out.println("hexadecimalNum:"+hexadecimalNum);
                                                textFields[fieldIndex][0].setText(hexadecimalNum.length() == 1 ? "0" + hexadecimalNum.toUpperCase() : hexadecimalNum.toUpperCase());
                                                thisTextField.setBinaryToBtns(binaryNum);

                                            }
                                        }
                                    }
                                }, 5);
                            }
                        }

                        @Override
                        public void keyReleased(KeyEvent e) {
                            super.keyReleased(e);
                            // 按下ctrl
                            if (e.getKeyCode() == 17) {
                                if (thisTextField.getCol() == 0) {
                                    String regEx = "[^\\dA-Fa-f]"; //获取除字母数字以外的其他字符
                                    Pattern pattern = Pattern.compile(regEx);
                                    Matcher matcher = pattern.matcher(thisTextField.getText());
                                    thisTextField.setText(matcher.replaceAll("").trim());
                                    int decimalNum = Integer.valueOf((thisTextField.getText().isEmpty()) ? "0" : thisTextField.getText(), 16);//16����ת��Ϊ10����
                                    String binaryNum = Integer.toBinaryString(decimalNum);
                                    binaryNum = String.format("%8s", binaryNum).replace(" ", "0");
                                    if (decimalNum < 10) {
                                        textFields[fieldIndex][1].setText("0" + decimalNum);
                                    } else {
                                        textFields[fieldIndex][1].setText("");
                                        textFields[fieldIndex][1].setText(decimalNum + "");
                                    }
                                    thisTextField.setBinaryToBtns(binaryNum);
                                }
                                if (thisTextField.getCol() == 1) {
                                    String regEx = "\\D"; //获取除字母数字以外的其他字符
                                    Pattern pattern = Pattern.compile(regEx);
                                    Matcher matcher = pattern.matcher(thisTextField.getText());
                                    thisTextField.setText(matcher.replaceAll("").trim());
                                    String binaryNum = Integer.toBinaryString(Integer.parseInt((thisTextField.getText().isEmpty()) ? "00" : thisTextField.getText()));//��xת��Ϊ������
                                    binaryNum = String.format("%8s", binaryNum).replace(" ", "0");
                                    String hexadecimalNum = Long.toHexString(Long.parseLong((thisTextField.getText().isEmpty()) ? "00" : thisTextField.getText(), 10));
                                    textFields[fieldIndex][0].setText(hexadecimalNum.length() == 1 ? "0" + hexadecimalNum.toUpperCase() : hexadecimalNum.toUpperCase());
                                    thisTextField.setBinaryToBtns(binaryNum);
                                }
                            }

                        }
                    });
                    textFields[k][j].addFocusListener(new FocusAdapter() {

                        @Override
                        public void focusLost(FocusEvent e) {
                            super.focusLost(e);
                            if (thisTextField.getText().isEmpty()) {
                                thisTextField.setText("00");
                            }
                            if (thisTextField.getText().length() == 1) {
                                thisTextField.setText("0" + (thisTextField.getText()).toUpperCase());
                            }
                            if (!thisTextField.getText().equals("00")) {
                                if (thisTextField.getText().length() == 2) {
                                    thisTextField.setText(thisTextField.getText().toUpperCase());
                                }
                            }
                            for (int f = 0; f < dataLength; f++) {
                                data[f] = (textFields[f][0].getText().isEmpty()) ? "00" : (textFields[f][0].getText().length() == 1 ? "0" + textFields[f][0].getText() : textFields[f][0].getText());
                            }
                        }

                    });
                }

                textFieldsPanel.add(panelToIncludeTextFieldPanel[k]);
                panelToIncludeTextFieldPanel[k].setBounds(0, boxHeight * k, 90, boxHeight);
            }

            // 数据编辑按钮
            BtnWithVal[][] btns = new BtnWithVal[dataLength][8];
            // 字节数据，按位置，记录每一行数据
            // 存储16进制、10进制
            String[] hexadecimalArr = new String[dataLength];
            String[] decimalArr = new String[dataLength];

            for (int k = 0; k < btns.length; k++) {
                int ByteIndex = k;
                panelToIncludeButtonsPanel[k] = new JPanel();
                panelToIncludeButtonsPanel[k].setLayout(new GridLayout(1, 8));
                for (int j = 0; j < btns[k].length; j++) {
                    btns[k][j] = new BtnWithVal(j, k);
                    if (dataTextField.getText().isEmpty()) {
                        btns[k][j].setText("0");
                    } else {
                        // 16进制转2进制
                        int decimalNum = Integer.valueOf((dataForTextFields[k].isEmpty()) ? "0" : dataForTextFields[k], 16);
                        String binaryNum = Integer.toBinaryString(decimalNum);
                        binaryNum = String.format("%8s", binaryNum).replace(" ", "0");
                        String[] dataForBtns = binaryNum.split("");
                        btns[k][j].setText(dataForBtns[j]);
                    }

                    btns[k][j].setBackground(new Color(176, 213, 247));
                    BtnWithVal thisBtn = btns[k][j];
                    btns[k][j].addMouseListener(new MouseAdapter() {
                        @Override
                        public void mouseClicked(MouseEvent e) {
                            super.mouseClicked(e);
                            if (e.getButton() == MouseEvent.BUTTON1) {
                                thisBtn.setText((thisBtn.getText().equals("0")) ? "1" : "0");
                                StringBuilder byteString = new StringBuilder();
                                String hexadecimal;
                                String decimal;
                                for (int j = 0; j < 8; j++) {
                                    byteString.append(btns[ByteIndex][j].getText());
                                    if (j == 7) {
                                        hexadecimal = Long.toHexString(Long.parseLong(byteString.toString(), 2));
                                        hexadecimalArr[ByteIndex] = hexadecimal;
                                        decimal = Integer.parseInt(byteString.toString(), 2) + "";
                                        decimalArr[ByteIndex] = decimal;
                                    }
                                }

                                textFields[ByteIndex][0].setText(hexadecimalArr[ByteIndex].length() == 1 ? "0" + hexadecimalArr[ByteIndex].toUpperCase() : hexadecimalArr[ByteIndex].toUpperCase());
                                textFields[ByteIndex][1].setText(decimalArr[ByteIndex].length() == 1 ? "0" + decimalArr[ByteIndex] : decimalArr[ByteIndex]);
                                for (int f = 0; f < dataLength; f++) {
                                    data[f] = (textFields[f][0].getText().isEmpty()) ? "00" : (textFields[f][0].getText().length() == 1 ? "0" + textFields[f][0].getText() : textFields[f][0].getText());
                                }
                            }
                        }

                        @Override
                        public void mouseEntered(MouseEvent e) {
                            // TODO Auto-generated method stub
                            super.mouseEntered(e);
                            if (e.getSource() == thisBtn) {
                                for (BtnWithVal[] btn : btns) {
                                    for (BtnWithVal btnWithVal : btn) {
                                        btnWithVal.setBackground(new Color(176, 213, 247));
                                    }
                                }
                                thisBtn.setBackground(new Color(111, 168, 220));
                            }
                        }

                    });
                    panelToIncludeButtonsPanel[k].add(btns[k][j]);

                }
                buttonsPanel.add(panelToIncludeButtonsPanel[k]);
                if (dataLength < 8) {
                    panelToIncludeButtonsPanel[k].setBounds(0, boxHeight * k, 348, boxHeight);
                } else {
                    panelToIncludeButtonsPanel[k].setBounds(0, boxHeight * k, 335, boxHeight);
                }
                panelToIncludeButtonsPanel[k].setBorder(null);

            }
            for (int k = 0; k < dataLength; k++) {
                for (int j = 0; j < 2; j++) {
                    TextFieldWithIndex thisTextField = textFields[k][j];
                    textFields[k][j].addKeyListener(new KeyAdapter() {
                        @Override
                        public void keyReleased(KeyEvent e) {
                            super.keyReleased(e);
                            if (e.getKeyCode() == 17) {
                                new Timer().schedule(new TimerTask() {
                                    @Override
                                    public void run() {
                                        int row = thisTextField.getRow();
                                        for (int a = 0; a < thisTextField.getBinaryToBtns().length(); a++) {
                                            btns[row][a].setText(thisTextField.getBinaryToBtns().substring(a, a + 1));
                                        }
                                    }
                                }, 5);
                            }
                        }

                        @Override
                        public void keyTyped(KeyEvent e) {
                            super.keyTyped(e);
                            new Timer().schedule(new TimerTask() {
                                @Override
                                public void run() {
                                    if (!isPressCtrl) {
                                        int row = thisTextField.getRow();
                                        for (int a = 0; a < thisTextField.getBinaryToBtns().length(); a++) {
                                            btns[row][a].setText(thisTextField.getBinaryToBtns().substring(a, a + 1));
                                        }
                                    }
                                }
                            }, 15);
                        }
                    });
                }
            }
        }
        CanMessageDataEditDialog thisDialog = this;
        confirmBtnToCloseThisDialog.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                // TODO Auto-generated method stub
                super.mouseClicked(e);
                if (e.getButton() == MouseEvent.BUTTON1) {
                    for (int k = 0; k < dataLength; k++) {
//                        System.out.print(data[k] + "  ");
                        dataShowBuilder.append(data[k]).append(" ");
                    }
//						 System.out.println(dataShow);
                    dataTextField.setText(dataShowBuilder.toString());
                    thisDialog.dispose();
                }
            }

        });
        add(containerPanel);
    }
}
