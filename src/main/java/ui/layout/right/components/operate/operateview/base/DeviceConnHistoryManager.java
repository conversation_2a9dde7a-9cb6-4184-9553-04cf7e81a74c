package ui.layout.right.components.operate.operateview.base;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.domain.Device;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DeviceConnHistoryManager {

    @Getter
    private Map<String, Map<String, Device>> connectHistoryMap; // deviceModelType -> index -> device
    private String filePath;

    public void init() {
        // 初始化一次即可
        if (filePath != null) {
            return;
        }
        filePath = "D:\\FlyTest\\data\\client\\app\\config\\connectedDevices.json";
        readConnectHistoryFromFile();
    }

    // TODO 遍历 headers  ，再获取panel，从中选择device是connect == true的， 记录索引 + device
    public void addDeviceConnectedHistory(Device device) {
        if (device == null) {
            return;
        }
        String deviceModel = device.getDeviceModel();
//        if (deviceModel.equals(DeviceModel.Camera.BASLER_CAMERA)) {
//            deviceModel = DeviceModel.Camera.USB_CAMERA;
//        }
        String currentIndex = "" + device.getDeviceIndex();
        Map<String, Device> portDeviceMap; // index -> device
        if (!connectHistoryMap.containsKey(deviceModel)) {
            portDeviceMap = new HashMap<>();
            connectHistoryMap.put(deviceModel, portDeviceMap);
        } else {
            portDeviceMap = connectHistoryMap.get(deviceModel);
        }
        portDeviceMap.put(currentIndex, device);

        // 持久化
        writeConnectHistoryToFile();
    }

    public void readConnectHistoryFromFile() {
        File connectHistoryFile = null;
        if (Files.exists(Paths.get(filePath))) {
            connectHistoryFile = new File(filePath);
        } else {
            try {
                connectHistoryFile = Files.createFile(Paths.get(filePath)).toFile();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (connectHistoryFile != null) {
            try {
                String s = new String(Files.readAllBytes(Paths.get(connectHistoryFile.getAbsolutePath())), StandardCharsets.UTF_8);
                connectHistoryMap = JSON.parseObject(s, new TypeReference<Map<String, Map<String, Device>>>() {
                });
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (connectHistoryMap == null) {
            connectHistoryMap = new HashMap<>();
        }
    }

    public void writeConnectHistoryToFile() {
        String s = JSON.toJSONString(connectHistoryMap);
        try {
            Files.write(Paths.get(filePath), s.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}
