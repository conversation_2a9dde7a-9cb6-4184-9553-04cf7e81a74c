package ui.layout.left.display.components.toolkit.monitor;

import com.alibaba.fastjson2.JSON;
import common.exceptions.JsonAssembleException;
import lombok.Getter;
import lombok.Setter;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.domain.BooleanComparator;
import sdk.domain.monitor.MonitorDataPackage;
import ui.base.BaseView;
import ui.base.OperationAssembler;
import ui.base.component.TimeSelector;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ItemEvent;
import java.util.List;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static sdk.domain.monitor.MonitorDataPackage.*;

/**
 * 设备数据监控面板
 */
public class DeviceDataMonitorPanel extends JPanel implements BaseView, OperationAssembler<MonitorDataPackage> {
    public final static int defaultMaxValue = 60;

    private final static Map<String, String> compareNotationMap = new HashMap<String, String>() {
        {
            put("大于", ">");
            put("小于", "<");
            put("大于等于", ">=");
            put("小于等于", "<=");
            put("等于", "==");
            put("不等于", "!=");
        }
    };

    private static final List<String> COMPARE_NOTATION_MORE = new ArrayList<>(Arrays.asList("不判断", "大于", "等于", "大于等于"));
    private static final List<String> COMPARE_NOTATION_LESS = new ArrayList<>(Arrays.asList("不判断", "小于", "等于", "小于等于"));

    @Getter
    private final JComboBox<String> moreOrEqualComboBox;
    @Getter
    private final JComboBox<String> lessOrEqualComboBox;
    @Getter
    private final JSpinner moreOrEqualSpinner;
    @Getter
    private final JSpinner lessOrEqualSpinner;
    private Box monitorOptionConfigBox;
    @Setter
    private DataMonitorHandler dataMonitorHandler;
    @Getter
    private final DeviceMonitorConstant deviceMonitor;
    private final TimeSelector timeSelector;
    private final JComboBox<String> monitorTypeComboBox;
    private final JButton debugButton;
    private final JButton addToScriptButton;
    private final JComboBox<String> currentDirectionComboBox;
    private final Box monitorBox;
    private final MainModel mainModel;
    private final int maxValue;

    private boolean onlyDC;

    public DeviceDataMonitorPanel(MainModel mainModel, DeviceMonitorConstant deviceMonitor, MonitorParameterConfig monitorParameterConfig) {
        this.mainModel = mainModel;
        this.maxValue = monitorParameterConfig == null || monitorParameterConfig.getMaxValue() == null ?
                defaultMaxValue : monitorParameterConfig.getMaxValue();
        this.deviceMonitor = deviceMonitor;
        currentDirectionComboBox = new JComboBox<>(new String[]{AC, DC, VOL});
        moreOrEqualComboBox = new JComboBox<>();
        lessOrEqualComboBox = new JComboBox<>();
        moreOrEqualSpinner = new JSpinner();
        lessOrEqualSpinner = new JSpinner();
        timeSelector = new TimeSelector();
        monitorTypeComboBox = new JComboBox<>(new String[]{STOP_WHEN_NOT_CONDITION, STOP_WHEN_CONDITION});

        debugButton = SwingUtil.getDebugButton();
        addToScriptButton = SwingUtil.getAddToScriptButton();
        monitorBox = createMonitorBox();
    }

    public CurrentDirection getCurrentDirection() {
        if (onlyDC) {
            return CurrentDirection.UNDEFINE;
        }
        if (Objects.equals(currentDirectionComboBox.getSelectedItem(), AC)) {
            return CurrentDirection.AC;
        } else if (Objects.equals(currentDirectionComboBox.getSelectedItem(), DC)) {
            return CurrentDirection.DC;
        } else if (Objects.equals(currentDirectionComboBox.getSelectedItem(), VOL)) {
            return CurrentDirection.VOL;
        }
        return CurrentDirection.UNDEFINE;
    }

    public void setOnlyDC(boolean onlyDC) {
        this.onlyDC = onlyDC;
        if (onlyDC) {
            currentDirectionComboBox.setSelectedItem(DC);
        }
        currentDirectionComboBox.setEnabled(!onlyDC);
    }

    public void setVol(boolean flag) {
        if (flag) {
            currentDirectionComboBox.setSelectedItem(VOL);
        }
        currentDirectionComboBox.setEnabled(false);
    }

    public Box createMonitorBox() {
        Box monitorBox = Box.createVerticalBox();
        monitorBox.setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), deviceMonitor.getMonitorTitle(), TitledBorder.LEADING, TitledBorder.TOP, null, null));
        Box moreOrLessBox = Box.createHorizontalBox();
        moreOrLessBox.add(currentDirectionComboBox);
        moreOrLessBox.add(moreOrEqualSpinner);
        moreOrLessBox.add(Box.createHorizontalStrut(2));
        moreOrLessBox.add(moreOrEqualComboBox);
        moreOrLessBox.add(Box.createHorizontalStrut(3));
        moreOrLessBox.add(new JLabel("(" + deviceMonitor.getMonitorLabel() + ")"));
        moreOrLessBox.add(Box.createHorizontalStrut(3));
        moreOrLessBox.add(lessOrEqualComboBox);
        moreOrLessBox.add(Box.createHorizontalStrut(2));
        moreOrLessBox.add(lessOrEqualSpinner);

        moreOrEqualSpinner.setEnabled(false);
        lessOrEqualSpinner.setEnabled(false);

        monitorOptionConfigBox = Box.createHorizontalBox();
        monitorOptionConfigBox.add(new JLabel("监控持续时间:"));
        monitorOptionConfigBox.add(timeSelector);
        monitorOptionConfigBox.add(new JLabel("监控选项:"));
        monitorOptionConfigBox.add(monitorTypeComboBox);


        moreOrEqualSpinner.setModel(new SpinnerNumberModel(0.0, 0.0, maxValue, 1));
        lessOrEqualSpinner.setModel(new SpinnerNumberModel(0.0, 0.0, maxValue, 1));
        for (String item : COMPARE_NOTATION_MORE) {
            moreOrEqualComboBox.addItem(item);
        }
        for (String item : COMPARE_NOTATION_LESS) {
            lessOrEqualComboBox.addItem(item);
        }

        monitorBox.add(moreOrLessBox);
        monitorBox.add(Box.createVerticalStrut(10));
        monitorBox.add(monitorOptionConfigBox);

        return monitorBox;
    }

    @Override
    public void createView() {
        setLayout(new GridLayout(1, 1));
        Box box = Box.createHorizontalBox();
        Box assistBox = Box.createVerticalBox();
        assistBox.add(debugButton);
        assistBox.add(addToScriptButton);
        box.add(monitorBox);
        box.add(assistBox);
        add(box);
    }

    private String getCompareNotation(String friendlyNotation) {
        return compareNotationMap.get(friendlyNotation);
    }

    private String getFriendlyNotation(String compareNotation) {
        String targetKey = null;
        for (Map.Entry<String, String> entry : compareNotationMap.entrySet()) {
            if (Objects.equals(entry.getValue(), compareNotation)) {
                targetKey = entry.getKey();
                break;
            }
        }
        return targetKey;
    }

    public String getMonitorCondition() {
        StringBuilder condition = new StringBuilder();
        if (moreOrEqualComboBox.getSelectedIndex() != 0 && !moreOrEqualSpinner.getValue().equals(Float.NEGATIVE_INFINITY)) {
            String notation = (String) moreOrEqualComboBox.getSelectedItem();
            assert notation != null;
            String notationExpr = getCompareNotation(notation);
            if (notationExpr != null) {
                condition.append(notationExpr).append(moreOrEqualSpinner.getValue());
            }
        }

        if (lessOrEqualComboBox.getSelectedIndex() != 0 && !lessOrEqualSpinner.getValue().equals(Float.POSITIVE_INFINITY)) {
            String notation = (String) lessOrEqualComboBox.getSelectedItem();
            assert notation != null;
            String notationExpr = getCompareNotation(notation);
            if (notationExpr != null) {
                if (!condition.toString().isEmpty()) {
                    condition.append("&");
                }
                condition.append(notationExpr).append(lessOrEqualSpinner.getValue());
            }
        }

        return condition.toString();
    }

    public void buildFromMonitorCondition(String monitorCondition) {
        String[] conditionArray = monitorCondition.split("&");
        for (int i = 0; i < conditionArray.length; i++) {
            Pattern pattern = Pattern.compile("([><=!]+)([0-9]*\\.?[0-9]+)");
            Matcher matcher = pattern.matcher(conditionArray[i]);
            if (matcher.find()) {
                String compareNotation = matcher.group(1);
                float value = Float.parseFloat(matcher.group(2));
                if (i == 0) {
                    moreOrEqualSpinner.setValue(value);
                    moreOrEqualComboBox.setSelectedItem(getFriendlyNotation(compareNotation));
                }
                if (i == 1) {
                    lessOrEqualSpinner.setValue(value);
                    lessOrEqualComboBox.setSelectedItem(getFriendlyNotation(compareNotation));
                }
            }
        }
    }

    public int getDuration() {
        return timeSelector.getSeconds();
    }

    public MonitorDataPackage getMonitorDataPackage() {
        return getMonitorDataPackage(true);
    }

    public MonitorDataPackage getMonitorDataPackage(boolean continuouslyMonitored) {
        String expr = getMonitorCondition();
        MonitorDataPackage monitorDataPackage = new MonitorDataPackage();
        monitorDataPackage.setCondition(expr);
        monitorDataPackage.setDuration(continuouslyMonitored ? getDuration() : 0);
        monitorDataPackage.setCurrentDirection(getCurrentDirection().name());
        monitorDataPackage.setStopOnCondition(Objects.equals(monitorTypeComboBox.getSelectedItem(), STOP_WHEN_CONDITION));
        return monitorDataPackage;
    }

    @Override
    public void createActions() {
        debugButton.addActionListener(e -> {
            String expr = getMonitorCondition();
            if (Objects.equals(expr, "")) {
                SwingUtil.showWarningDialog(this, "请设置判断表达式");
                return;
            }
            OperationResult operationResult = dataMonitorHandler.monitor(getMonitorDataPackage(false));
            if (operationResult.isOk()) {
                BooleanComparator booleanComparator = JSON.parseObject(String.valueOf(operationResult.getData()), BooleanComparator.class);
                if (booleanComparator.isPass()) {
                    SwingUtil.showInformationDialog(this, booleanComparator.getResult());
                } else {
                    SwingUtil.showWarningDialog(this, booleanComparator.getResult());
                }
            } else {
                SwingUtil.showWarningDialog(this, operationResult.getMessage());
            }
        });

        addToScriptButton.addActionListener(e -> {
            String expr = getMonitorCondition();
            if (Objects.equals(expr, "")) {
                SwingUtil.showWarningDialog(this, "请设置判断表达式");
                return;
            }
            Operation operation = dataMonitorHandler.addToScript(getMonitorDataPackage());
            mainModel.getOperationModel().updateOperation(operation);
        });

        moreOrEqualComboBox.addItemListener(e -> {
            int stateChange = e.getStateChange();// 获得事件类型
            if (stateChange == ItemEvent.SELECTED) {// 查看是否由选中选项触发
                int index = moreOrEqualComboBox.getSelectedIndex();
                moreOrEqualSpinner.setEnabled(index != 0);
            }
        });

        lessOrEqualComboBox.addItemListener(e -> {
            int stateChange = e.getStateChange();// 获得事件类型
            if (stateChange == ItemEvent.SELECTED) {// 查看是否由选中选项触发
                int index = lessOrEqualComboBox.getSelectedIndex();
                lessOrEqualSpinner.setEnabled(index != 0);
            }
        });
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        debugButton.setEnabled(isDeviceConnected);
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        MonitorDataPackage monitorDataPackage = JSON.to(MonitorDataPackage.class, injectedOperation.getOperationObject());
        if (monitorDataPackage.getCurrentDirection() != null) {
            if (monitorDataPackage.getCurrentDirection().equals(CurrentDirection.DC.name())) {
                currentDirectionComboBox.setEnabled(true);
                currentDirectionComboBox.setSelectedItem(DC);
            } else if (monitorDataPackage.getCurrentDirection().equals(CurrentDirection.AC.name())) {
                currentDirectionComboBox.setEnabled(true);
                currentDirectionComboBox.setSelectedItem(AC);
            } else if (monitorDataPackage.getCurrentDirection().equals(CurrentDirection.UNDEFINE.name())) {
                setOnlyDC(true);
            }
        } else {
            setOnlyDC(true);
        }
        monitorTypeComboBox.setSelectedItem(monitorDataPackage.isStopOnCondition() ? STOP_WHEN_CONDITION : STOP_WHEN_NOT_CONDITION);
        timeSelector.buildFromSeconds(monitorDataPackage.getDuration());
        buildFromMonitorCondition(monitorDataPackage.getCondition());
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        injectedOperation.setOperationObject(getMonitorDataPackage());
        return injectedOperation;
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

    @Override
    public JComponent getAssembleContainer() {
        return monitorBox;
    }


}
