package excelcase.config.json;

import com.alibaba.fastjson2.JSON;
import common.utils.FileUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

@Setter
@Slf4j
public class ExcelCaseTemplateConfig {
    private volatile static ExcelCaseTemplateConfig instance;
    private File templateConfigfile;

    public static ExcelCaseTemplateConfig getInstance() {
        if (instance == null) {
            synchronized (ExcelCaseTemplateConfig.class) {
                if (instance == null) {
                    instance = new ExcelCaseTemplateConfig();
                }
            }
        }
        return instance;
    }

    public ExcelCaseTemplate readJsonConfigFile() {
        try {
            log.info("读取配置文件:{}", templateConfigfile.getAbsolutePath());
            String readStringFromFile = FileUtils.readStringFromFile(templateConfigfile);
            return JSON.parseObject(readStringFromFile, ExcelCaseTemplate.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void writeJsonConfigFile(String configJsonString) {
        try {
            FileUtils.writeFileFromString(templateConfigfile, configJsonString, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    public void initExcelCaseTemplate() throws IOException {
        ExcelCaseTemplate excelCaseTemplate = readJsonConfigFile();
        ExcelCaseTemplate templateInstance = ExcelCaseTemplate.getInstance();
        templateInstance.initData();
        if (excelCaseTemplate == null) {
            log.info("excelCaseTemplate 为 null，初始化新实例并写入文件");
            writeJsonConfigFile(JSON.toJSONString(templateInstance));
        } else {
            log.info("excelCaseTemplate 不为 null，更新配置");
            HashMap<String, List<String>> configTemplateColumnMap = excelCaseTemplate.getTemplateColumnMap();
            HashMap<String, List<String>> templateColumnMap = ExcelCaseTemplate.getInstance().getTemplateColumnMap();
            if (!configTemplateColumnMap.equals(templateColumnMap)) {
                writeJsonConfigFile(JSON.toJSONString(templateInstance));
            } else {
                templateInstance.setTemplateColumnMap(excelCaseTemplate.getTemplateColumnMap());
            }
        }
    }

}
