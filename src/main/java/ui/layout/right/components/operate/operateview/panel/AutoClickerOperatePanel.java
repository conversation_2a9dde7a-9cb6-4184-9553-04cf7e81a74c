package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.AutoClickerDevice;
import sdk.entity.OperationTargetHolder;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

public class AutoClickerOperatePanel extends DeviceOperatePanel {

    public AutoClickerOperatePanel(MainModel mainModel) {
        super(mainModel);
        AutoClickerDevice httpClient = OperationTargetHolder.getAutoClickerDeviceManager();
        setDeviceClient(httpClient);
        createView();
        createActions();
    }
}
