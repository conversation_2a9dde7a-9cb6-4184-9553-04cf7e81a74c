package ui.layout.left.display.components.container.power.kikusui.pulseSequence;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import ui.base.BaseView;
import ui.config.base.Folder;
import ui.config.xml.device.KikusuiDeviceConfig;
import ui.config.xml.device.WaveConfig;
import ui.config.xml.device.WaveConstants;
import ui.config.xml.device.WavePulse;
import ui.layout.left.display.dialogs.ConfirmDialog;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 创建脉冲序列界面
 */
@Slf4j
public class PulseSequenceSetupDialog extends ConfirmDialog implements BaseView {
    private static final String delTestRequirementHint = "删除测试需求图";
    private static final String addTestRequirementHint = "添加测试需求图";

    private static final String delOscillogramHint = "删除波形图";
    private static final String addOscillogramHint = "添加波形图";
    private static final int memorySum = 16;

    private final Font font = new Font("宋体", Font.PLAIN, 13);

    @Setter
    private String sequenceName;

    private JTextField[] pulseNameTextField;

    private JButton[] addRequirementImgButton;

    private JButton[] addOscillogramImgButton;
    private JLabel[] requirementImageInfoLabel;
    private JLabel[] oscillogramImageInfoLabel;

    private TemporaryImage[] temporaryImageList;

    private KikusuiDeviceConfig kikusuiDeviceConfig;

    private TemporaryImage[] initImageList;
    private String[] initWaveNames;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class TemporaryImage {
        private File requirementImage;
        private File oscillogramImage;

        public TemporaryImage copy() {
            return new TemporaryImage(requirementImage, oscillogramImage);
        }
    }


    public PulseSequenceSetupDialog() {
        setPreferredSize(new Dimension(1000, 550));
    }

    public static PulseSequenceSetupDialog of(String sequenceName, KikusuiDeviceConfig kikusuiDeviceConfig) {
        PulseSequenceSetupDialog dialog = new PulseSequenceSetupDialog();
        dialog.kikusuiDeviceConfig = kikusuiDeviceConfig;
        dialog.setTitle(String.format("创建脉冲系列-%s", sequenceName));
        dialog.setSequenceName(sequenceName);
        dialog.createView();
        dialog.createActions();
        dialog.setDefaultCloseOperation(DO_NOTHING_ON_CLOSE);
        SwingUtil.centerInScreen(dialog);
        dialog.temporaryImageList = new TemporaryImage[memorySum];
        dialog.initImageList = new TemporaryImage[memorySum];
        dialog.initWaveNames = new String[memorySum];
        for (int i = 0; i < memorySum; i++) {
            dialog.temporaryImageList[i] = new TemporaryImage();
            dialog.temporaryImageList[i].setRequirementImage(new File(""));
            dialog.temporaryImageList[i].setOscillogramImage(new File(""));
            dialog.initImageList[i] = new TemporaryImage();
            dialog.initImageList[i].setRequirementImage(new File(""));
            dialog.initImageList[i].setOscillogramImage(new File(""));
            dialog.initWaveNames[i] = dialog.pulseNameTextField[i].getText().trim();
        }
        return dialog;
    }


    public PulseSequenceSetupDialog build() {
        setVisible(true);
        return this;
    }

    /**
     * 从配置文件导入波形配置
     */
    public PulseSequenceSetupDialog loadFromJsonConfig() {
        WaveConfig waveConfig = kikusuiDeviceConfig.getWavePulseSequenceOfUser(sequenceName);
        List<WavePulse> pulses = waveConfig.getPulses();
        for (WavePulse wavePulse : pulses) {
            int index = wavePulse.getMemory() - 1;
            pulseNameTextField[index].setText(wavePulse.getName());
            File file = kikusuiDeviceConfig.getSequenceFolder().getFolder(sequenceName).toFile();
            if (!wavePulse.getTestRequirement().trim().isEmpty()) {
                temporaryImageList[index].setRequirementImage(new File(file, wavePulse.getTestRequirement()));
            } else {
                temporaryImageList[index].setRequirementImage(new File(""));
            }
            if (!wavePulse.getOscillogram().trim().isEmpty()) {
                temporaryImageList[index].setOscillogramImage(new File(file, wavePulse.getOscillogram()));
            } else {
                temporaryImageList[index].setOscillogramImage(new File(""));
            }
        }
        for (int i = 0; i < temporaryImageList.length; i++) {
            File requirementImage = temporaryImageList[i].getRequirementImage();
            File oscillogramImage = temporaryImageList[i].getOscillogramImage();
            if (!requirementImage.getPath().trim().isEmpty()) {
                setReqImageTextDisplay(i, requirementImage);
            }
            if (!oscillogramImage.getPath().trim().isEmpty()) {
                setOscillogramImageTextDisplay(i, oscillogramImage);
            }
        }
        for (int i = 0; i < memorySum; i++) {
            initImageList[i] = temporaryImageList[i].copy();
            initWaveNames[i] = pulseNameTextField[i].getText().trim();
        }
        return this;
    }


    @Override
    public JPanel makeCenterPanel() {
        JPanel panel = new JPanel(new GridLayout(16, 1));

        // 内存位置Label、输入脉冲名称TextField、添加图片Button、图片名称Label
        JLabel[] memoryLabel = new JLabel[memorySum];
        pulseNameTextField = new JTextField[memorySum];
        addRequirementImgButton = new JButton[memorySum];
        addOscillogramImgButton = new JButton[memorySum];

        requirementImageInfoLabel = new JLabel[memorySum];
        oscillogramImageInfoLabel = new JLabel[memorySum];

        for (int i = 0; i < memorySum; i++) {
            int memory = i + 1;
            //创建一行布局：JLabel+JTextField+JButton+JButton
//            panelOfList[i] = new JPanel();
            JPanel rowPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
            //添加具体控件
            memoryLabel[i] = new JLabel(String.format("内存%s:", memory), SwingConstants.RIGHT);
            pulseNameTextField[i] = new JTextField();
            addRequirementImgButton[i] = new JButton(addTestRequirementHint);
            addOscillogramImgButton[i] = new JButton(addOscillogramHint);

            requirementImageInfoLabel[i] = new JLabel();
            oscillogramImageInfoLabel[i] = new JLabel();

            memoryLabel[i].setPreferredSize(new Dimension(50, 25));
            pulseNameTextField[i].setPreferredSize(new Dimension(200, 25));
            pulseNameTextField[i].setMargin(new Insets(0, 5, 0, 0));

            addRequirementImgButton[i].setPreferredSize(new Dimension(150, 25));
            addOscillogramImgButton[i].setPreferredSize(new Dimension(120, 25));

            requirementImageInfoLabel[i].setPreferredSize(new Dimension(200, 25));
            oscillogramImageInfoLabel[i].setPreferredSize(new Dimension(200, 25));

            addRequirementImgButton[i].setFont(font);
            addOscillogramImgButton[i].setFont(font);
            requirementImageInfoLabel[i].setFont(font);
            oscillogramImageInfoLabel[i].setFont(font);

            rowPanel.add(memoryLabel[i]);
            rowPanel.add(pulseNameTextField[i]);
            rowPanel.add(addRequirementImgButton[i]);
            rowPanel.add(addOscillogramImgButton[i]);
            rowPanel.add(requirementImageInfoLabel[i]);
            rowPanel.add(oscillogramImageInfoLabel[i]);
            panel.add(rowPanel);
            final int index = i;
            addRequirementImgButton[i].addActionListener(e -> {
                if (requirementImageInfoLabel[index].getText().trim().isEmpty()) {
                    File imageFile = getImageFile();
                    if (imageFile != null) {
                        temporaryImageList[index].setRequirementImage(imageFile);
                        setReqImageTextDisplay(index, imageFile);
                    }
                } else {
                    //TODO：删除sequence文件夹相同文件
                    temporaryImageList[index].setRequirementImage(new File(""));
                    requirementImageInfoLabel[index].setText("");
                    addRequirementImgButton[index].setText(addTestRequirementHint);
                }

            });

            addOscillogramImgButton[i].addActionListener(e -> {
                if (oscillogramImageInfoLabel[index].getText().trim().isEmpty()) {
                    File imageFile = getImageFile();
                    if (imageFile != null) {
                        temporaryImageList[index].setOscillogramImage(imageFile);
                        setOscillogramImageTextDisplay(index, imageFile);
                    }
                } else {
                    //TODO：删除sequence文件夹相同文件
                    temporaryImageList[index].setOscillogramImage(new File(""));
                    oscillogramImageInfoLabel[index].setText("");
                    addOscillogramImgButton[index].setText(addOscillogramHint);
                }

            });
        }
        return panel;
    }

    private void setReqImageTextDisplay(int index, File imageFile) {
        requirementImageInfoLabel[index].setText(String.format("需求图:%s", imageFile.getName()));
        addRequirementImgButton[index].setText(delTestRequirementHint);
    }

    private void setOscillogramImageTextDisplay(int index, File imageFile) {
        oscillogramImageInfoLabel[index].setText(String.format("波形图:%s", imageFile.getName()));
        addOscillogramImgButton[index].setText(delOscillogramHint);
    }

    /**
     * 获取用户选择的图片
     *
     * @return 图片文件
     */
    private File getImageFile() {
        return SwingUtil.getFileChooser(this, "选择图片",
                new FileNameExtensionFilter("Image Files(*.jpg,*.jpeg,*.png)", "jpg", "jpeg", "png", "JPG", "JPEG", "PNG"),
                false);
    }

    /**
     * 判断是否用户编辑过
     *
     * @return 是否用户编辑过
     */
    private boolean isUserEdit() {
        boolean isEdit = false;
        for (int i = 0; i < memorySum; i++) {
            if (!initWaveNames[i].equals(pulseNameTextField[i].getText().trim())) {
                isEdit = true;
                break;
            }
            if (!initImageList[i].getRequirementImage().getPath().trim().equals(temporaryImageList[i].getRequirementImage().getPath().trim())
                    || !initImageList[i].getOscillogramImage().getPath().trim().equals(temporaryImageList[i].getOscillogramImage().getPath().trim())) {
                isEdit = true;
                break;
            }
        }
        return isEdit;
    }

    private boolean askWhenClose() {
        if (isUserEdit()) {
            int answer = JOptionPane.showConfirmDialog(PulseSequenceSetupDialog.this, "当前有未保存的更改内容，确定丢弃并退出？", "关闭对话", JOptionPane.YES_NO_OPTION);
            return answer == JOptionPane.YES_OPTION;
        } else {
            return true;
        }
    }

    @Override
    public void createActions() {
        super.createActions();
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                if (askWhenClose()) {
                    dispose();
                }
            }
        });
    }

    @Override
    public void cancel() {
        if (askWhenClose()) {
            setConfirmed(false);
            dispose();
        }
    }

    /**
     * 保存配置
     */
    private void saveConfig() {
        String imageFolderPrefix = "image";
        String tempFolderPrefix = "temp";
        Folder dstFolder = kikusuiDeviceConfig.getSequenceFolder().createFolder(sequenceName);
        Folder tempFolder = dstFolder.createFolder(tempFolderPrefix);
        Folder requirementFolder = tempFolder.createFolder(WaveConstants.TEST_REQUIREMENT);
        Folder oscillogramFolder = tempFolder.createFolder(WaveConstants.OSCILLOGRAM);
        try {
            for (TemporaryImage image : temporaryImageList) {
                File requirementImage = image.getRequirementImage();
                File oscillogramImage = image.getOscillogramImage();
                if (!requirementImage.getPath().trim().isEmpty()) {
                    requirementFolder.copy(requirementImage);
                }
                if (!oscillogramImage.getPath().trim().isEmpty()) {
                    oscillogramFolder.copy(oscillogramImage);
                }
            }
            dstFolder.delete(imageFolderPrefix);
            tempFolder.rename(imageFolderPrefix);
            WaveConfig waveConfig = new WaveConfig();
            waveConfig.setBuiltin(false);
            waveConfig.setSequenceName(sequenceName);
            waveConfig.setFriendlyName(sequenceName);
            List<WavePulse> pulses = waveConfig.getPulses();
            for (int i = 0; i < temporaryImageList.length; i++) {
                WavePulse wavePulse = new WavePulse();
                wavePulse.setName(pulseNameTextField[i].getText());
                wavePulse.setMemory(i + 1);
                File requirementImage = temporaryImageList[i].getRequirementImage();
                if (!requirementImage.getPath().trim().isEmpty()) {
                    wavePulse.setTestRequirement(imageFolderPrefix + File.separator
                            + WaveConstants.TEST_REQUIREMENT + File.separator + requirementImage.getName());
                }
                File oscillogramImage = temporaryImageList[i].getOscillogramImage();
                if (!oscillogramImage.getPath().trim().isEmpty()) {
                    wavePulse.setOscillogram(imageFolderPrefix + File.separator
                            + WaveConstants.OSCILLOGRAM + File.separator + oscillogramImage.getName());
                }
                pulses.add(wavePulse);
            }
            kikusuiDeviceConfig.writeWavePulseSequenceOfUser(dstFolder, waveConfig);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public boolean performConfirm() {
        if (!isUserEdit()) {
            SwingUtil.showWarningDialog(this, "当前修改内容为空");
            return false;
        }
        saveConfig();
        return true;
    }
}
