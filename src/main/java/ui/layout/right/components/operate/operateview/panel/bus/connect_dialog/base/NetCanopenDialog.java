package ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.base;

import lombok.Getter;
import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.Objects;

import sdk.domain.bus.NetCanConfigParameter;


/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/5/12 18:48
 * @Version: 1.0
 * @Desc : 通道启动时net类属性设置面板
 */
public class NetCanopenDialog extends JDialog implements ActionListener {
    private static final String CONFIRM = "确认";
    private static final String CANCEL = "取消";
    private static final String LOCAL_PORT = "本地端口";
    private static final String WORK_MODE = "工作模式";
    private static final String CAN_FD_ACCELERATOR = "CANFD加速";
    private static final String CAN_PROTOCOL = "协议";
    private static final String IP = "ip地址";
    private static final String WORK_PORT = "工作端口";
    private static final String CAN = "CAN";
    private static final String CANFD = "CAN FD";
    private static final String TITLE = "启动";
    private final JLabel protocolLabel;
    private final JLabel canfdAccelerateLabel;
    private final JLabel workModeLabel;
    private final JLabel localPortLabel;
    private final JLabel ipLabel;
    private final JLabel workPortLabel;
    private final JTextField localPortTextField;
    private final JTextField ipTextField;
    private final JTextField workPortTextField;

    private final JComboBox<String> protocolComboBox;
    private final JComboBox<String> canFdAccelerateComboBox;
    private final JComboBox<String> workModeComboBox;

    private static final int WIDTH = 500;
    private static final int HEIGHT = 550;

    @Getter
    private boolean configConfirmed = true;//对话框“取消”标志
    @Getter
    private NetCanConfigParameter netCanConfigParameter;

    public NetCanopenDialog(int channel){
        netCanConfigParameter = new NetCanConfigParameter();
        netCanConfigParameter.setChannel(channel);
        //创建面板与滚动面板
        JPanel panel = new JPanel();
        //确认与取消按钮
        JButton confirmButton = new JButton(CONFIRM);
        JButton cancelButton = new JButton(CANCEL);
        confirmButton.setActionCommand(CONFIRM);
        confirmButton.addActionListener(this);
        cancelButton.setActionCommand(CANCEL);
        cancelButton.addActionListener(this);
        confirmButton.setFocusPainted(false);
        cancelButton.setFocusPainted(false);
        Box horizontalBox = Box.createHorizontalBox();
        horizontalBox.add(Box.createHorizontalGlue());
        horizontalBox.add(confirmButton);
        horizontalBox.add(Box.createHorizontalGlue());
        horizontalBox.add(cancelButton);
        horizontalBox.add(Box.createHorizontalGlue());

        //协议
        String[] protocol = {CAN, CANFD};
        protocolLabel = new JLabel("协议");
        protocolComboBox = new JComboBox<>(protocol);
        protocolComboBox.setActionCommand(CAN_PROTOCOL);
        protocolComboBox.addActionListener(this);
        //CANFD加速
        String[] canFdAccelerate = {"否", "是"};
        canfdAccelerateLabel = new JLabel(CAN_FD_ACCELERATOR);
        canFdAccelerateComboBox = new JComboBox<>(canFdAccelerate);
        canFdAccelerateComboBox.setActionCommand(CAN_FD_ACCELERATOR);
        canFdAccelerateComboBox.setSelectedIndex(0);
        canFdAccelerateComboBox.addActionListener(this);
        //工作模式
        String[] workMode = {"服务器", "客户端"};
        workModeLabel = new JLabel(WORK_MODE);
        workModeComboBox = new JComboBox<>(workMode);
        workModeComboBox.setActionCommand(WORK_MODE);
        workModeComboBox.setSelectedIndex(0);
        workModeComboBox.addActionListener(this);
        //本地端口
        localPortLabel = new JLabel(LOCAL_PORT);
        localPortTextField = new JTextField("4001");
        localPortTextField.setEnabled(true);
        //IP地址
        ipLabel = new JLabel(IP);
        ipTextField = new JTextField("*************");
        ipTextField.setEnabled(true);
        //工作端口
        workPortLabel = new JLabel(WORK_PORT);
        workPortTextField = new JTextField("8000");
        workPortTextField.setEnabled(true);
        //布局设置
        setLayout(panel);
        setTitle(TITLE);
        setLayout(new BorderLayout());
        setSize(new Dimension(WIDTH, HEIGHT));
        setResizable(true);
        setLocationRelativeTo(null);
        add(panel, BorderLayout.CENTER);
        add(horizontalBox, BorderLayout.SOUTH);
        setModal(true);
        protocolComboBox.setSelectedIndex(1);
        workModeComboBox.setSelectedIndex(1);
        deepHide(localPortTextField);
        workModeComboBox.addItemListener(e -> enableModedLayout(isModeService()));
        canFdAccelerateComboBox.setSelectedIndex(1);
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                // 在这里实现关闭事件的处理逻辑
                dialogClosed();
            }
        });

    }

    private void enableModedLayout(boolean isEnable) {
        if(isEnable){
            restoreComponent(localPortTextField,netCanConfigParameter.getLocalPort());
            deepHide(ipTextField);
            deepHide(workPortTextField);
        }else{
            deepHide(localPortTextField);
            restoreComponent(ipTextField,netCanConfigParameter.getIp());
            restoreComponent(workPortTextField,netCanConfigParameter.getWorkPort());
        }
    }

    /**
     * 初始化对话框的组件状态，根据传入的CanConfigParameter
     *
     *
     */
    public void initializeWithConfig(NetCanConfigParameter netCanConfigParameter) {
        if(netCanConfigParameter!=null){
            this.netCanConfigParameter = netCanConfigParameter;
            // 设置各个组件的值
            protocolComboBox.setSelectedItem(netCanConfigParameter.getProtocol());
            canFdAccelerateComboBox.setSelectedItem(netCanConfigParameter.isAccelerate() ? "是" : "否");
            workModeComboBox.setSelectedItem(netCanConfigParameter.getWorkMode());
            localPortTextField.setText(netCanConfigParameter.getLocalPort());
            ipTextField.setText(netCanConfigParameter.getIp());
            workPortTextField.setText(netCanConfigParameter.getWorkPort());
            enableModedLayout(isModeService(netCanConfigParameter.getWorkMode()));
        }
    }

    private void setLayout(JPanel panel) {
        //布局设置
        GroupLayout layout = new GroupLayout(panel); //分组布局的面板
        panel.setLayout(layout);
        layout.setAutoCreateGaps(true);//组件之间的间隙自动适应
        layout.setAutoCreateContainerGaps(true); //容器与触到容器边框的组件之间的间隙自动适应
        /*分组布局*/
        //水平并行: 垂直排列（上下排列）
        GroupLayout.ParallelGroup labelParallelGroup = layout.createParallelGroup().
                addComponent(protocolLabel).
                addComponent(canfdAccelerateLabel).
                addComponent(workModeLabel).
                addComponent(localPortLabel).
                addComponent(ipLabel).
                addComponent(workPortLabel);
        GroupLayout.ParallelGroup elementParallelGroup = layout.createParallelGroup().
                addComponent(protocolComboBox).
                addComponent(canFdAccelerateComboBox).
                addComponent(workModeComboBox).
                addComponent(localPortTextField).
                addComponent(ipTextField).
                addComponent(workPortTextField);
        //水平串行（左右）labelParallelGroup和elementParallelGroup
        GroupLayout.SequentialGroup sequentialGroup = layout.createSequentialGroup().
                addGroup(labelParallelGroup).
                addGroup(elementParallelGroup);
        layout.setHorizontalGroup(sequentialGroup);
        //垂直并行（左右）
        GroupLayout.ParallelGroup protocolGroup = layout.createParallelGroup().
                addComponent(protocolLabel).
                addComponent(protocolComboBox);
        GroupLayout.ParallelGroup CANFDAccelerateparallelGroup = layout.createParallelGroup().
                addComponent(canfdAccelerateLabel).
                addComponent(canFdAccelerateComboBox);
        GroupLayout.ParallelGroup workModeGroup = layout.createParallelGroup().
                addComponent(workModeLabel).
                addComponent(workModeComboBox);
        GroupLayout.ParallelGroup localPortGroup = layout.createParallelGroup().
                addComponent(localPortLabel).
                addComponent(localPortTextField);
        GroupLayout.ParallelGroup ipGroup = layout.createParallelGroup().
                addComponent(ipLabel).
                addComponent(ipTextField);
        GroupLayout.ParallelGroup workPortGroup = layout.createParallelGroup().
                addComponent(workPortLabel).
                addComponent(workPortTextField);
        //垂直串行（上下）
        GroupLayout.SequentialGroup layoutSequentialGroup = layout.createSequentialGroup().
                addGroup(protocolGroup).
                addGroup(CANFDAccelerateparallelGroup).
                addGroup(workModeGroup).
                addGroup(localPortGroup).
                addGroup(ipGroup).
                addGroup(workPortGroup);
        layout.setVerticalGroup(layoutSequentialGroup);
    }

    private static void deepHide(JComponent component) {
        // 1. 强制阻断所有绘制
        component.setOpaque(false);
//        component.setContentAreaFilled(false);
//        component.setFocusPainted(false);
        component.setBorder(BorderFactory.createEmptyBorder());

        // 2. 清除所有视觉属性
        component.setBackground(new Color(0,0,0,0));
        component.setForeground(new Color(0,0,0,0));
        if (component instanceof JTextField) {
            ((JTextField) component).setText("");
            ((JTextField) component).setCaretColor(new Color(0,0,0,0));
            ((JTextField) component).setSelectionColor(new Color(0,0,0,0));
        }

        // 3. 禁用交互
        component.setEnabled(false);
        component.setFocusable(false);

        // 4. 强制刷新界面
        component.getParent().revalidate();
        component.getParent().repaint();
    }

    // 恢复显示
    private static void restoreComponent(JComponent component,String text) {
        component.setOpaque(true);
        component.setBorder(UIManager.getBorder("TextField.border"));

        component.setBackground(UIManager.getColor("TextField.background"));
        component.setForeground(UIManager.getColor("TextField.foreground"));
        if (component instanceof JTextField) {
            ((JTextField) component).setText(text);
            ((JTextField) component).setCaretColor(UIManager.getColor("TextField.caretForeground"));
            ((JTextField) component).setSelectionColor(UIManager.getColor("TextField.selectionBackground"));
        }

        component.setEnabled(true);
        component.setFocusable(true);

        component.getParent().revalidate();
        component.getParent().repaint();
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        //封装设备管理属性
        if (e.getActionCommand().equals(CONFIRM)) {
                setNetCanConfigParameter();
                configConfirmed = true;
                setVisible(false);
        }else if(e.getActionCommand().equals(CANCEL)){
            dialogClosed();
        }
    }

    public void setNetCanConfigParameter() {
        netCanConfigParameter.setProtocol(String.valueOf(protocolComboBox.getSelectedItem()));
        netCanConfigParameter.setAccelerate("是".equals(canFdAccelerateComboBox.getSelectedItem()));
        netCanConfigParameter.setWorkMode(String.valueOf(workModeComboBox.getSelectedItem()));
        netCanConfigParameter.setLocalPort(localPortTextField.getText());
        netCanConfigParameter.setIp(String.valueOf(ipTextField.getText()));
        netCanConfigParameter.setWorkPort(workPortTextField.getText());
    }

    private boolean isModeService() {
        return Objects.requireNonNull(workModeComboBox.getSelectedItem()).equals("服务器");
    }

    private boolean isModeService(String mode) {
        return mode.equals("服务器");
    }

    protected void dialogClosed() {
        configConfirmed = false;
        setVisible(false);
    }

    public static void main(String[] args) {
        // 创建一个示例的 LIN 配置参数
        NetCanConfigParameter configParameter = new NetCanConfigParameter();

        // 在Swing线程中启动对话框
        SwingUtilities.invokeLater(() -> {
            // 创建并显示 LIN 配置对话框
            NetCanopenDialog linOpenDialog = new NetCanopenDialog(1);
//            NetCanopenDialog.initializeWithConfig(configParameter);  // 使用示例配置初始化对话框
            linOpenDialog.setVisible(true);  // 显示对话框
        });
    }
}
