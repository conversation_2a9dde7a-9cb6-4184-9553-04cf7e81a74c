package ui.layout.right.components.testscript.scriptview;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import common.exceptions.JsonAssembleException;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import sdk.base.operation.Operation;
import ui.base.OperationAssembler;

import javax.swing.*;
import java.awt.*;
import java.awt.event.*;
import java.util.ArrayList;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JsonObjectDisplayPanel extends JPanel implements OperationAssembler<Object> {

    private int depth = 0;
    private int index = 0;
    private final JPanel panelCenter;
    @Getter
    private final StringBuilder operationObjectStringBuilder = new StringBuilder();
    private final ArrayList<Object> dataTypesOfJSONArray = new ArrayList<>();
    private int count = 0;

    @Getter
    private final ArrayList<String> keyList = new ArrayList<>();
    @Getter
    private final ArrayList<String> typeList = new ArrayList<>();
    @Getter
    private final ArrayList<Integer> levelList = new ArrayList<>();
    @Getter
    private final ArrayList<Integer> widthList = new ArrayList<>();
    private final static String DATA_TYPE_STRING = "String";
    private final static String DATA_TYPE_INTEGER = "Integer";
    private final static String DATA_TYPE_DOUBLE = "Double";
    private final static String DATA_TYPE_BOOLEAN = "Boolean";

    private final JSONObject jsonObj;

    public JsonObjectDisplayPanel(JSONObject jsonObj) {
        this.jsonObj = jsonObj;
        setLayout(new BorderLayout());
        panelCenter = new JPanel(new GridLayout(jsonObj.size(), 1));
        operationObjectStringBuilder.delete(0, operationObjectStringBuilder.length());
        enumerateJsonObject(jsonObj);
        operationObjectStringBuilder.append(jsonObj);
        add(panelCenter, BorderLayout.CENTER);
    }

    public void enumerateJsonObject(@NotNull JSONObject jsonObj) {
        for (Map.Entry<String, Object> stringObjectEntry : jsonObj.entrySet()) {
            String key = stringObjectEntry.getKey();
            String dataTypeOfItemInJsonObj = stringObjectEntry.getValue().getClass().getSimpleName();
            keyList.add(key);
            typeList.add(dataTypeOfItemInJsonObj);
            levelList.add(depth);
            //System.out.println(entry.getKey().toString() + ":" + entry.getValue().getClass().getSimpleName());
            int xIncrease = depth * 30;
            int yIncrease = index * 30;
            if (depth == 0) {
                JLabel label = new JLabel(key + ":", SwingConstants.CENTER);
                panelCenter.add(label);
                label.setFont(new Font(null, Font.PLAIN, 12));
                // 记录深度为0,不是对象类型的数据的label的宽度
                int width = (int) label.getPreferredSize().getWidth();
                widthList.add(width);
                label.setBounds(10 + xIncrease, 20 + yIncrease, width, 25);
            } else {
                JLabel label = new JLabel(key + ":", SwingConstants.RIGHT);
                panelCenter.add(label);
                label.setFont(new Font(null, Font.PLAIN, 12));
                label.setBounds(xIncrease, 20 + yIncrease, 100, 25);
            }
            //System.out.println(typeList.size());
            int x = 100 + xIncrease;
            int y = 20 + yIncrease;
            //值为Double类型生成文本框
            if (dataTypeOfItemInJsonObj.equals(DATA_TYPE_DOUBLE)) {
                createTextField(stringObjectEntry, x, y, "textFieldForDouble");
            }
            //值为Integer类型生成文本框
            if (dataTypeOfItemInJsonObj.equals(DATA_TYPE_INTEGER)) {
                createTextField(stringObjectEntry, x, y, "textFieldForInteger");
            }
            //值为布尔类型生成复选框
            if (dataTypeOfItemInJsonObj.equals(DATA_TYPE_BOOLEAN)) {
                JCheckBox checkBox = new JCheckBox();
                checkBox.setSelected((Boolean) stringObjectEntry.getValue());
                panelCenter.add(checkBox);
                if (depth == 0) {
                    x = getWidthList().get(count) + 10;
                    count++;
                }
                checkBox.setBounds(x, y, 25, 25);
                checkBox.setName("checkBox");
                //复选框的选择监听事件
                int Depth = getLevelList().get(getIndex(checkBox.getBounds().y));
                checkBox.addItemListener(e -> {
                    String nextVal = String.valueOf(checkBox.isSelected());
                    if (Depth == 0) {
                        changeJson(key, nextVal);
                    } else {
                        changeData(key, checkBox, null, null);
                    }
                });
            }
            //值为String类型
            if (dataTypeOfItemInJsonObj.equals(DATA_TYPE_STRING)) {
                createTextField(stringObjectEntry, x, y, "textFieldForString");
////               JSON文件存放algorithm参数的处理
//                if (key.equals("algorithm")) {
//                    JSONArray jsonArray = readAlgorithm("algorithm.json");
//                    List<String> list = new ArrayList<>();
//                    for (Object o : jsonArray) {
//                        list.add((String) o);
//                    }
//                    JComboBox<Object> algorithmComboBox = new JComboBox<>(list.toArray());
//                    panelCenter.add(algorithmComboBox);
//                    if (depth == 0) {
//                        x = getWidthList().get(count) + 10;
//                        count++;
//                    }
//                    algorithmComboBox.setBounds(x, y, 200, 25);
//
//                    algorithmComboBox.setName("comboBox");
//                    algorithmComboBox.addItemListener(new ItemListener() {
//                        String preVal = "";
//                        String nextVal = "";
//
//                        @Override
//                        public void itemStateChanged(ItemEvent e) {
//                            if (e.getStateChange() == ItemEvent.DESELECTED) {
//                                preVal = (String) e.getItem();
//                            }
//                            if (e.getStateChange() == ItemEvent.SELECTED) {
//                                nextVal = (String) e.getItem();
//                                int Depth = getLevelList().get(getIndex(algorithmComboBox.getBounds().y));
//                                if (Depth == 0) {
//                                    changeJson(key, nextVal);
//                                } else {
//                                    changeData(key, algorithmComboBox, preVal, nextVal);
//                                }
//                            }
//                        }
//                    });
//                } else {
//                    createTextField(stringObjectEntry, x, y, "textFieldForString");
//                }
            }
            if (dataTypeOfItemInJsonObj.equals("JSONArray")) {
                JSONArray jsonArray = (JSONArray) stringObjectEntry.getValue();
                int[] xArr = new int[jsonArray.size() + 1];
                xArr[0] = x;
//                xArr最后一个元素存的是最后一个控件能完整显示的x坐标
                for (int i = 1; i < jsonArray.size() + 1; i++) {
                    if (jsonArray.get(i - 1).getClass().getSimpleName().equals(DATA_TYPE_BOOLEAN)) {
                        xArr[i] = xArr[i - 1] + 25 + 8;
                    } else {
                        xArr[i] = xArr[i - 1] + 150 + 8;
                    }
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    String dataTypeOfItemInJsonArr = jsonArray.get(i).getClass().getSimpleName();
                    if (dataTypeOfItemInJsonArr.equals(DATA_TYPE_INTEGER) || dataTypeOfItemInJsonArr.equals(DATA_TYPE_DOUBLE)) {
                        dataTypesOfJSONArray.add(i, dataTypeOfItemInJsonArr);
                        if (dataTypeOfItemInJsonArr.equals(DATA_TYPE_INTEGER)) {
                            createTextFieldByJsonArray(stringObjectEntry, jsonArray.get(i).toString(), xArr[i], y, "textFieldForInteger" + i);
                        } else {
                            createTextFieldByJsonArray(stringObjectEntry, jsonArray.get(i).toString(), xArr[i], y, "textFieldForDouble" + i);
                        }
                    }

                    if (dataTypeOfItemInJsonArr.equals(DATA_TYPE_STRING)) {
                        dataTypesOfJSONArray.add(i, DATA_TYPE_STRING);
                        createTextFieldByJsonArray(stringObjectEntry, jsonArray.get(i).toString(), xArr[i], y, "textFieldForString" + i);
                    }

                    if (dataTypeOfItemInJsonArr.equals(DATA_TYPE_BOOLEAN)) {
                        dataTypesOfJSONArray.add(i, DATA_TYPE_BOOLEAN);
                        createCheckBoxByJsonArray(stringObjectEntry, Boolean.valueOf(jsonArray.get(i).toString()), xArr[i], y, "checkBox" + i);
                    }
                }
            }
            index++;
            if (dataTypeOfItemInJsonObj.equals("JSONObject")) {
                if (depth == 0) {
                    count++;
                }
                depth++;
                enumerateJsonObject((JSONObject) stringObjectEntry.getValue());
                depth--;
            }
        }
    }

    public ArrayList<String> findJsonObj(int index) {
//        arr:装每一层的对象名
        ArrayList<String> arr = new ArrayList<>();
        for (int j = 0; j <= 10; j++) arr.add(null);
        for (int i = index; i >= 0; i--) {
            String key = getKeyList().get(i);
            String type = getTypeList().get(i);
            int depth = getLevelList().get(i);
            if (type.equals("JSONObject") || type.equals("JSONArray")) {
//                记录该控件所属的json对象(从后往前找,找到同样深度的第一个对象，装入arr,没找到之前arr为空)
                if (arr.get(depth) == (null)) arr.set(depth, key);
            }
        }
        return arr;
    }

    public int findPosition(@NotNull ArrayList<String> arr, String key) {
        int position = -1;
//        realLength:记录数组有值的长度
        int realLength = 0;
        for (Object o : arr) {
            if (o != null) realLength++;
        }
//        把选中的设置项也添加到数组中
        if (arr.get(realLength) == null) {
            arr.set(realLength, key);
            realLength++;
        }
//        System.out.println(arr);
        int fromIndex = 0;
//        在多层的对象中
        if (realLength > 0) {
            for (int i = 0; i < realLength; i++) {
                if (i != 0) {
//                 [roi,pointEnd,empty] pointEnd的位置查找，从stringBuilder中的roi之后开始，以此类推
                    fromIndex = operationObjectStringBuilder.indexOf(arr.get(i - 1), fromIndex);
                }
                if (i == realLength - 1) position = operationObjectStringBuilder.indexOf(arr.get(i), fromIndex);
            }
        }
        return position;
    }

    public void settingsCheckBox(String key, @NotNull JCheckBox checkBox, int position) {
//       按"empty": true的格式替换,而position是e的位置
        position = position - 1;
        String[] str = new String[2];
//      把stringBuilder中str[0]修改为str[1],checkBox.isSelected()是点击checkBox后的值
        str[0] = "\"" + key + "\":" + !checkBox.isSelected();
        str[1] = "\"" + key + "\":" + checkBox.isSelected();
        int endIndex = position + str[0].length();
        operationObjectStringBuilder.replace(position, endIndex, str[1]);
    }

    public void settingsTextField(String key, int position, String preVal, String nextVal, @NotNull String name) {
        position = position - 1;
        String[] str = new String[2];
        if (name.equals("textFieldForDouble")) {
            str[0] = "\"" + key + "\":" + preVal;
            str[1] = "\"" + key + "\":" + nextVal;
        } else if (name.equals("textFieldForString")) {
            str[0] = "\"" + key + "\":\"" + preVal + "\"";
            str[1] = "\"" + key + "\":\"" + nextVal + "\"";
        }
//      把stringBuilder中str[0]修改为str[1]
        int endIndex = position + str[0].length();
        operationObjectStringBuilder.replace(position, endIndex, str[1]);
    }

    public void settingsComboBox(String key, int position, String preVal, String nextVal) {
        position = position - 1;
        String[] str = new String[2];
//      把stringBuilder中str[0]修改为str[1]
        str[0] = "\"" + key + "\":\"" + preVal + "\"";
        str[1] = "\"" + key + "\":\"" + nextVal + "\"";
        int endIndex = position + str[0].length();
        operationObjectStringBuilder.replace(position, endIndex, str[1]);
    }

    public int getIndex(int Height) {
        return (Height - 20) / 30;
    }

    public void createTextField(Map.Entry<String, Object> entry, int x, int y, String name) {
        JTextField textField = new JTextField(entry.getValue().toString());
        panelCenter.add(textField);
        if (depth == 0) {
            x = getWidthList().get(count) + 10;
            count++;
            textField.setBounds(x, y, 200, 25);
        } else {
            textField.setBounds(x, y, 200, 25);
        }
        textField.setName(name);
        if (!name.contains(DATA_TYPE_STRING)) limitInput(textField, name);
        textField.addFocusListener(new FocusAdapter() {
            String preVal = "";
            String nextVal = "";

            @Override
            public void focusGained(FocusEvent e) {
                super.focusGained(e);
                preVal = textField.getText();
            }

            @Override
            public void focusLost(FocusEvent e) {
                super.focusLost(e);
                nextVal = textField.getText();
                nextVal = dataFormat(name, nextVal);
                textField.setText(nextVal);
                int Depth = getLevelList().get(getIndex(textField.getBounds().y));
                String key = entry.getKey();
                if (Depth == 0) {
                    changeJson(key, nextVal);
                } else {
                    changeData(key, textField, preVal, nextVal);
                }
            }
        });
    }

    public void createTextFieldByJsonArray(Map.Entry<String, Object> entry, String str, int x, int y, String name) {
        JTextField textField = new JTextField(str);
        panelCenter.add(textField);
        textField.setBounds(x, y, 150, 25);
        textField.setName(name);
        if (!name.contains(DATA_TYPE_STRING)) limitInput(textField, name);
        textField.addFocusListener(new FocusAdapter() {
            String preVal = "";
            String nextVal = "";

            @Override
            public void focusGained(FocusEvent e) {
                super.focusGained(e);
                preVal = textField.getText();
            }

            @Override
            public void focusLost(FocusEvent e) {
                super.focusLost(e);
                nextVal = textField.getText();
                nextVal = dataFormat(name, nextVal);
                textField.setText(nextVal);
                changeJSONArray(entry, nextVal, name);
            }
        });
    }

    public void createCheckBoxByJsonArray(Map.Entry<String, Object> entry, Boolean isSelected, int x, int y, String name) {
        JCheckBox checkBox = new JCheckBox();
        checkBox.setSelected(isSelected);
        panelCenter.add(checkBox);
        checkBox.setBounds(x, y, 25, 25);
        checkBox.addItemListener(e -> changeJSONArray(entry, String.valueOf(checkBox.isSelected()), name));
    }

    //    修改字符串
    public void changeData(String key, @NotNull JComponent component, String preVal, String nextVal) {
//                      1.根据位置推算除选中的控件对应的属性的在keyList中的位置
        int Index = getIndex(component.getBounds().y);
//                      2.找出外层的对象
        ArrayList<String> arr = findJsonObj(Index);
//                      3.根据外层的对象定位到设置的参数值在stringBuilder中的位置
        int position = findPosition(arr, key);
//                      4，修改stringBuilder中的值
        if (component.getName().equals("checkBox")) settingsCheckBox(key, (JCheckBox) component, position);
        if (component.getName().equals("textFieldForDouble"))
            settingsTextField(key, position, preVal, nextVal, "textFieldForDouble");
        if (component.getName().equals("comboBox")) settingsComboBox(key, position, preVal, nextVal);
        if (component.getName().equals("textFieldForString"))
            settingsTextField(key, position, preVal, nextVal, "textFieldForString");
    }

    //    修改json对象
    public void changeJson(String key, String nextVal) {
        JSONObject jsonObject = (JSONObject) JSON.parse(operationObjectStringBuilder.toString());
        if (nextVal.equals("true") || nextVal.equals("false")) {
            jsonObject.put(key, Boolean.parseBoolean(nextVal));
        } else {
            if (NumberUtil.isDouble(nextVal)) {
                jsonObject.put(key, Float.parseFloat(nextVal));
            } else if (NumberUtil.isInteger(nextVal)) {
                jsonObject.put(key, Integer.parseInt(nextVal));
            } else {
                jsonObject.put(key, nextVal);
            }
        }
        operationObjectStringBuilder.delete(0, operationObjectStringBuilder.length()).append(jsonObject);
    }

    public void changeJSONArray(Map.Entry<String, Object> entry, String nextVal, String name) {
        int indexInJSONArray = 0;
//                文本框name属性的最后一位是文本框的内容在JSONArray中的位置
        Matcher matcher = Pattern.compile("\\d{1,2}").matcher(name);
        while (matcher.find()) {
            indexInJSONArray = Integer.parseInt(matcher.group());
        }
//                当前选中的文本框的数据类型
//        System.out.println(dataTypesOfJSONArray);
        String dataType = (String) dataTypesOfJSONArray.get(indexInJSONArray);
        String key = entry.getKey();
        JSONArray jsonArray = (JSONArray) entry.getValue();
//                1.拼接出数组在stringBuilder中的样子
        String preStr = "\"" + key + "\":" + jsonArray;
//                2.根据数据类型修改jsonArray
        if (dataType.equals(DATA_TYPE_STRING)) jsonArray.set(indexInJSONArray, nextVal);
        if (dataType.equals(DATA_TYPE_INTEGER)) {
            if (nextVal.contains(".")) nextVal = nextVal.substring(0, nextVal.indexOf("."));
            jsonArray.set(indexInJSONArray, Integer.parseInt(nextVal));
        }
        if (dataType.equals(DATA_TYPE_DOUBLE)) jsonArray.set(indexInJSONArray, Double.valueOf(nextVal));
        if (dataType.equals(DATA_TYPE_BOOLEAN)) jsonArray.set(indexInJSONArray, Boolean.parseBoolean(nextVal));
        String nextStr = "\"" + key + "\":" + jsonArray;
//                3.找到数组在stringBuilder中的位置
        int start = operationObjectStringBuilder.indexOf(preStr);
        int end = operationObjectStringBuilder.indexOf(preStr) + preStr.length();
//                4.替换
        operationObjectStringBuilder.replace(start, end, nextStr);
    }

    //    限制输入
    public void limitInput(JTextField textField, String name) {

        textField.addKeyListener(new KeyAdapter() {
            java.util.Timer t = new java.util.Timer();

            @Override
            public void keyTyped(KeyEvent e) {
                super.keyTyped(e);
//                防止每一次输入都触发
                t.cancel();
                t = new Timer();
                t.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        String str = textField.getText();
                        if (name.contains(DATA_TYPE_DOUBLE)) {
//                         1.匹配除0-9和.以外的字符，用""替换
                            str = Pattern.compile("[^0-9.]").matcher(str).replaceAll("");
//                         2.以重复的0开头,只保留一个0
                            str = Pattern.compile("^0+").matcher(str).replaceAll("0");
//                         3.以0开头+非0数字，去0
                            Matcher matcher = Pattern.compile("^(0+)([1-9]*)$").matcher(str);
                            while (matcher.find()) {
                                if (!matcher.group(2).isEmpty()) str = matcher.group(2);
                            }
//                         4.以.开头,删除.
                            str = Pattern.compile("^\\.").matcher(str).replaceAll("");
//                         5.只能输入一个小数点:匹配出符合条件的字符串保留   0.xxx | xxx.xxx
                            Matcher matcher1 = Pattern.compile("\\d+\\.\\d*").matcher(str);
                            while (matcher1.find()) {
                                str = matcher1.group(0);
                            }
                        }
                        if (name.contains(DATA_TYPE_INTEGER)) {
                            str = Pattern.compile("[^0-9]").matcher(str).replaceAll("");
                            Matcher matcher = Pattern.compile("^(0+)([1-9]*)$").matcher(str);
                            while (matcher.find()) {
                                if (!matcher.group(2).isEmpty()) {
                                    str = matcher.group(2);
                                }
                            }
                        }
                        textField.setText("");
                        textField.setText(str);
                        textField.setCaretPosition(str.length());
                    }
                }, 2);
            }
        });
    }

    //  处理数据
    public String dataFormat(String name, String nextVal) {
        if (name.contains(DATA_TYPE_DOUBLE)) {
            if (nextVal.isEmpty()) nextVal = "0.0";
            if (!nextVal.contains(".")) nextVal = nextVal + ".0";
            //以小数点后多个0结尾
            nextVal = String.valueOf(Double.valueOf(nextVal));
        }
        if (name.contains(DATA_TYPE_INTEGER)) {
            if (nextVal.isEmpty()) nextVal = "0";
        }
        if (name.contains(DATA_TYPE_STRING)) {
            if (nextVal.isEmpty()) nextVal = "";
        }
        return nextVal;
    }

    @Override
    public JComponent getAssembleContainer() {
        return this;
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {

    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        injectedOperation.setOperationObject(JSON.parseObject(operationObjectStringBuilder.toString(), jsonObj.getClass()));
        return injectedOperation;
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

}
