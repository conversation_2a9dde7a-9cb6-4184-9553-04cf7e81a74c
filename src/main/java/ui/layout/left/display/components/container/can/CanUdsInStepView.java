package ui.layout.left.display.components.container.can;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.bus.CanConfig;
import ui.base.BaseView;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.layout.left.display.components.container.can.model.TreeNodeData;
import ui.layout.left.display.components.container.can.model.UdsModel;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;
import java.awt.*;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/7/3 17:13
 * @Version: 1.0
 * @Desc : 描述信息
 */
@Slf4j
public class CanUdsInStepView extends DeviceControlPanel implements BaseView {

    private final int channel;
    private final CanContainer canContainer;
    private MainModel mainModel;
    private JTextField requestIdField;
    private JTextField responseIdField;
    private JTextField requestPduField;
    private JTextField responsePduField;
    private JLabel responseStates;
    private CanConfig canConfig;
    private JTree serviceTree;
    private JTextField functionalAddressField;
    private JCheckBox enableFunctionalAddressCheckBox;
    private JCheckBox enable3eButton;
    private JCheckBox responseCheckBox;
    private static final String USER_ADDED_PREFIX = "[用户添加] ";
    private final AtomicInteger threadCounter = new AtomicInteger(0);


    public CanUdsInStepView(CanContainer canContainer, MainModel mainModel, int channel, CanConfig canConfig) {
        super(canContainer, mainModel);
        this.mainModel = mainModel;
        this.channel = channel;
        this.canContainer = canContainer;
        this.canConfig = canConfig;
        createView();
        restoreView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());

        // 左侧服务和故障码面板
        JPanel leftPanel = new JPanel(new BorderLayout());
        JTabbedPane serviceTabs = new JTabbedPane();

        // 服务标签页
        serviceTree = new JTree();
        DefaultMutableTreeNode root = new DefaultMutableTreeNode("【原始报文】");

        buildOriginalTreeNodes(root);

        serviceTree.setModel(new DefaultTreeModel(root));
        serviceTabs.addTab("服务", new JScrollPane(serviceTree));

        // 添加右键菜单支持
        serviceTree.setComponentPopupMenu(new JPopupMenu());
        JPopupMenu popupMenu = new JPopupMenu();
        JMenuItem addSubItemMenuItem = new JMenuItem("添加子功能");
        JMenuItem deleteItemMenuItem = new JMenuItem("删除节点");  // 新增删除菜单项
        popupMenu.add(addSubItemMenuItem);
        popupMenu.add(deleteItemMenuItem);  // 添加删除菜单项

        // 弹出窗口事件处理 - 添加子功能
        addSubItemMenuItem.addActionListener(e -> {
            DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) serviceTree.getLastSelectedPathComponent();
            if (selectedNode == null) return;

            // 创建弹窗
            JDialog dialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(CanUdsInStepView.this), "添加子功能", true);
            dialog.setLayout(new BorderLayout(10, 10));

            // 左侧输入框（数字）
            JPanel numberPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
            JLabel numberLabel = new JLabel("服务数据:");  // 现在会显示
            JTextField numberField = getjTextField();

            numberPanel.add(numberLabel);
            numberPanel.add(numberField);

            // 中间显示个 "-"
            JLabel dashLabel = new JLabel("-");

            // 右侧输入框（服务名称）
            JPanel namePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
            JLabel nameLabel = new JLabel("服务名称:");  // 现在会显示
            JTextField nameField = new JTextField(10);

            namePanel.add(nameLabel);
            namePanel.add(nameField);

            // 组合输入面板
            JPanel inputPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 5, 0));
            inputPanel.add(numberPanel);
            inputPanel.add(dashLabel);
            inputPanel.add(namePanel);

            // 按钮面板
            JButton confirmButton = new JButton("确认");
            JButton cancelButton = new JButton("取消");

            confirmButton.addActionListener(evt -> {
                String number = numberField.getText().trim();
                String name = nameField.getText().trim();

                if (!number.isEmpty() && !name.isEmpty()) {
                    String displayText = String.format("%s%s - %s", USER_ADDED_PREFIX, number, name);
                    DefaultMutableTreeNode newNode = new DefaultMutableTreeNode(displayText);
                    selectedNode.add(newNode);
                    ((DefaultTreeModel) serviceTree.getModel()).nodeStructureChanged(selectedNode);

                    // 保存到 UdsModel
                    saveTreeToUdsModel();
                    dialog.dispose();
                }
            });


            cancelButton.addActionListener(evt -> dialog.dispose());

            JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 0));
            buttonPanel.add(confirmButton);
            buttonPanel.add(cancelButton);

            // 添加组件到弹窗
            dialog.add(inputPanel, BorderLayout.CENTER);
            dialog.add(buttonPanel, BorderLayout.SOUTH);

            dialog.setSize(700, 200);
            dialog.setLocationRelativeTo(SwingUtilities.getWindowAncestor(CanUdsInStepView.this));
            dialog.setVisible(true);
        });

        // 删除节点事件处理
        deleteItemMenuItem.addActionListener(e -> {
            DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) serviceTree.getLastSelectedPathComponent();
            if (selectedNode != null &&
                    selectedNode.getParent() != null &&
                    selectedNode.getUserObject() instanceof String &&
                    ((String) selectedNode.getUserObject()).startsWith(USER_ADDED_PREFIX)) {
                DefaultMutableTreeNode parentNode = (DefaultMutableTreeNode) selectedNode.getParent();
                ((DefaultTreeModel) serviceTree.getModel()).removeNodeFromParent(selectedNode);
                // 更新配置
                saveTreeToUdsModel();
            }
        });

        // 鼠标右键点击弹出菜单 - 支持所有节点添加子功能
        serviceTree.addMouseListener(new MouseAdapter() {
            public void mousePressed(MouseEvent e) {
                if (SwingUtilities.isRightMouseButton(e)) {
                    int x = e.getX();
                    int y = e.getY();
                    TreePath path = serviceTree.getPathForLocation(x, y);
                    if (path != null) {
                        serviceTree.setSelectionPath(path);

                        DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) serviceTree.getLastSelectedPathComponent();
                        boolean isUserAddedNode = selectedNode != null &&
                                selectedNode.getUserObject() instanceof String &&
                                ((String) selectedNode.getUserObject()).startsWith(USER_ADDED_PREFIX);

                        deleteItemMenuItem.setEnabled(isUserAddedNode);
                        addSubItemMenuItem.setEnabled(true); // 所有节点都可添加子功能

                        popupMenu.show(serviceTree, x, y);
                    }
                }
            }
        });

        // 当节点被选中时，记录当前选中的节点
        serviceTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) serviceTree.getLastSelectedPathComponent();
            if (selectedNode != null) {
                boolean isRoot = selectedNode.isRoot();
                addSubItemMenuItem.setEnabled(true);
                deleteItemMenuItem.setEnabled(selectedNode != null &&
                        selectedNode.getUserObject() instanceof String &&
                        ((String) selectedNode.getUserObject()).startsWith(USER_ADDED_PREFIX));
                // 只有用户添加的节点（非原始默认加载）可以删除
                deleteItemMenuItem.setEnabled(selectedNode.getUserObject() instanceof String && !isRoot);
            }
        });

        leftPanel.add(serviceTabs, BorderLayout.CENTER);

        // 上侧请求ID和响应ID面板
        // 使用BoxLayout布局，允许将组件分为左右两部分对齐
        JPanel idPanel = new JPanel();
        idPanel.setLayout(new BoxLayout(idPanel, BoxLayout.X_AXIS));

        // 左侧组件容器
        JPanel leftGroupPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        
        // 请求ID（文本框紧贴文字）
        JPanel requestIdPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        JLabel requestIdLabel = new JLabel("请求ID:");
        requestIdField = new JTextField();
        requestIdField.setColumns(10);
        requestIdPanel.add(requestIdLabel);
        requestIdPanel.add(requestIdField);

        // 响应ID（文本框紧贴文字）
        JPanel responseIdPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        JLabel responseIdLabel = new JLabel("响应ID:");
        responseIdField = new JTextField();
        responseIdField.setColumns(10);
        responseIdPanel.add(responseIdLabel);
        responseIdPanel.add(responseIdField);

        // 功能寻址（文本框紧贴文字）
        JPanel functionalAddressPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        JLabel functionalAddressLabel = new JLabel("功能寻址:");
        functionalAddressField = new JTextField();
        functionalAddressField.setColumns(10);
        functionalAddressPanel.add(functionalAddressLabel);
        functionalAddressPanel.add(functionalAddressField);
        JPanel enableFunctionalAddressPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        enableFunctionalAddressCheckBox = new JCheckBox("开启功能寻址");
        enableFunctionalAddressPanel.add(enableFunctionalAddressCheckBox);

        // 添加左侧所有组到leftGroupPanel
        leftGroupPanel.add(requestIdPanel);
        leftGroupPanel.add(responseIdPanel);
        leftGroupPanel.add(functionalAddressPanel);
        leftGroupPanel.add(enableFunctionalAddressPanel);

        // 右侧组件容器 - service3ePanel
        JPanel rightGroupPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 0, 0));
        enable3eButton = new JCheckBox("3e服务开启");
        responseCheckBox = new JCheckBox("开启响应");
        rightGroupPanel.add(enable3eButton);
        rightGroupPanel.add(responseCheckBox);

        // 将两个主要组添加到主面板中
        idPanel.add(leftGroupPanel);
        idPanel.add(Box.createHorizontalGlue()); // 弹性空间使右侧组件靠右
        idPanel.add(rightGroupPanel);

        // 下侧PDU面板（垂直排列请求PDU、响应PDU和按钮）
        JPanel pduPanel = new JPanel(new BorderLayout(0, 5)); // 垂直间距

        // 请求PDU面板
        JPanel requestPduPanel = new JPanel(new BorderLayout(0, 0));
        JLabel requestPduLabel = new JLabel("请求PDU:");
        requestPduField = new JTextField();
        requestPduPanel.add(requestPduLabel, BorderLayout.WEST);
        requestPduPanel.add(requestPduField, BorderLayout.CENTER);

        // 响应PDU面板
        JPanel responsePduPanel = new JPanel(new BorderLayout(0, 0));
        JLabel responsePduLabel = new JLabel("响应PDU:");
        responsePduField = new JTextField();
        responsePduPanel.add(responsePduLabel, BorderLayout.WEST);
        responsePduPanel.add(responsePduField, BorderLayout.CENTER);

        // 响应状态
        JPanel responseState = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0)); // 增加水平间距
        JLabel responseStateLabel = new JLabel("响应状态");
        responseStates = new JLabel("");
        responseStates.setPreferredSize(new Dimension(60, 40)); // 设置为圆形指示灯大小
        responseStates.setOpaque(true);
        responseStates.setBackground(Color.WHITE);
        responseStates.setBorder(BorderFactory.createLineBorder(Color.GRAY, 1)); // 添加边框更像指示灯
        responseState.add(responseStateLabel);
        responseState.add(Box.createHorizontalStrut(5)); // 添加5像素隔离空间
        responseState.add(responseStates);

        // 组合PDU输入字段的面板
        JPanel pduFieldsPanel = new JPanel();
        pduFieldsPanel.setLayout(new BoxLayout(pduFieldsPanel, BoxLayout.Y_AXIS));
        pduFieldsPanel.add(requestPduPanel);
        pduFieldsPanel.add(Box.createVerticalStrut(5)); // 添加垂直间距
        pduFieldsPanel.add(responsePduPanel);
        pduFieldsPanel.add(Box.createVerticalStrut(5)); // 添加垂直间距
        pduFieldsPanel.add(responseState);

        // 发送服务按钮
        JButton sendServiceButton = new JButton("发送服务");
        // 添加到步骤脚本按钮
        JButton addToScriptButton = new JButton("添加[发送服务]到脚本");
        JPanel buttonPanel = new JPanel(new GridBagLayout());
        buttonPanel.add(sendServiceButton);
        buttonPanel.add(addToScriptButton);

        // 发送服务按钮动作监听器
        sendServiceButton.addActionListener(e -> {
            if (!checkUserInput()) {
                responseStates.setBackground(Color.WHITE);
                return;
            }
            Operation operation = Operation.buildOperation(canContainer.getDevice());
            operation.getOperationTarget().setChannel(getChannel());
            operation.setOperationMethod(DeviceMethods.responsiveServices);
            operation.setOperationObject(toModel());
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult != null) {
                if (operationResult.isOk()) {
                    responseStates.setBackground(Color.GREEN);
                } else {
                    responseStates.setBackground(Color.RED);
                }
            } else {
                responseStates.setBackground(Color.WHITE);
            }
        });

        // 添加到步骤脚本按钮动作监听器
        addToScriptButton.addActionListener(e -> {
            if (!checkUserInput()) {
                return;
            }
            Operation operation = Operation.buildOperation(canContainer.getDevice());
            operation.getOperationTarget().setChannel(getChannel());
            operation.setOperationMethod(DeviceMethods.responsiveServices);
            operation.setOperationObject(toModel());
            getMainModel().getOperationModel().updateOperation(operation);

        });

        // 将输入区域和按钮添加到pduPanel
        pduPanel.add(pduFieldsPanel, BorderLayout.NORTH);
        pduPanel.add(buttonPanel, BorderLayout.CENTER);

        // 组合所有部分
        JPanel rightPanel = new JPanel(new BorderLayout());
        rightPanel.add(idPanel, BorderLayout.NORTH);
        rightPanel.add(pduPanel, BorderLayout.CENTER);

        // 组合所有部分
        add(leftPanel, BorderLayout.WEST);
        add(rightPanel, BorderLayout.CENTER);

        // 添加树选择监听器
        serviceTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) serviceTree.getLastSelectedPathComponent();
            if (selectedNode == null) {
                return;
            }

            String selectedValue = selectedNode.getUserObject().toString();
            String serviceCode = extractServiceCode(selectedValue);

            // 构建请求PDU
            StringBuilder requestPdu = new StringBuilder();
            if (selectedNode.getParent() != null) {
                DefaultMutableTreeNode parentNode = (DefaultMutableTreeNode) selectedNode.getParent();
                String parentValue = parentNode.getUserObject().toString();
                String parentServiceCode = extractServiceCode(parentValue);
                
                // 如果父节点还有自己的父节点（即存在祖父节点），则取祖父节点的服务码
                if (parentNode.getParent() != null) {
                    DefaultMutableTreeNode grandParentNode = (DefaultMutableTreeNode) parentNode.getParent();
                    String grandParentValue = grandParentNode.getUserObject().toString();
                    String grandParentServiceCode = extractServiceCode(grandParentValue);
                    requestPdu.append(grandParentServiceCode);
                    requestPdu.append(parentServiceCode);
                } else {
                    // 否则只处理当前父子两级
                    requestPdu.append(parentServiceCode);
                }
                requestPdu.append(serviceCode);
            } else {
                requestPdu.append(serviceCode);
            }
            // 构建响应PDU - 始终基于父节点的服务代码
            StringBuilder responsePdu = new StringBuilder();
            if (!serviceCode.isEmpty()) {
                int hexValue;
                // 如果是子节点，使用父节点的服务代码
                if (selectedNode.isLeaf() && selectedNode.getParent() != null) {
                    DefaultMutableTreeNode parentNode = (DefaultMutableTreeNode) selectedNode.getParent();
                    String parentValue = parentNode.getUserObject().toString();
                    String parentServiceCode = extractServiceCode(parentValue);
                    if (!parentServiceCode.isEmpty()) {
                        hexValue = Integer.parseInt(parentServiceCode, 16);
                    } else {
                        hexValue = Integer.parseInt(serviceCode, 16);
                    }
                } else {
                    hexValue = Integer.parseInt(serviceCode, 16);
                }
                responsePdu.append(String.format("%02X", hexValue + 0x40));
            }

            // 设置文本框
            requestPduField.setText(requestPdu.toString());
            responsePduField.setText(responsePdu.toString());
        });
    }

    @NotNull
    private static JTextField getjTextField() {
        JTextField numberField = new JTextField(5);
        numberField.setToolTipText("请输入16进制数字");
        numberField.setInputVerifier(new InputVerifier() {
            @Override
            public boolean verify(JComponent input) {
                JTextField field = (JTextField) input;
                String text = field.getText();
                try {
                    if (!text.isEmpty()) {
                        Integer.parseInt(text, 16);
                    }
                    return true;
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        });
        return numberField;
    }

    public UdsModel toModel() {
        UdsModel model = new UdsModel(getChannel(), requestIdField.getText(), responseIdField.getText(), requestPduField.getText(), responsePduField.getText(), functionalAddressField.getText(), enableFunctionalAddressCheckBox.isSelected());

        DefaultMutableTreeNode root = (DefaultMutableTreeNode) serviceTree.getModel().getRoot();
        List<TreeNodeData> treeDataList = buildTreeData(root);

        // 将 treeDataList 转换为 JSON 字符串，例如使用 Gson
        String jsonTreeData = new Gson().toJson(treeDataList);
        model.setTreeData(jsonTreeData);

        return model;
    }

    private List<TreeNodeData> buildTreeData(DefaultMutableTreeNode node) {
        List<TreeNodeData> result = new ArrayList<>();
        for (int i = 0; i < node.getChildCount(); i++) {
            DefaultMutableTreeNode childNode = (DefaultMutableTreeNode) node.getChildAt(i);
            TreeNodeData data = new TreeNodeData();
            data.setName(childNode.getUserObject().toString());
            data.setChildren(buildTreeData(childNode));
            result.add(data);
        }
        return result;
    }

    public boolean checkUserInput() {
        String frameId = requestIdField.getText().trim();
        if (frameId.isEmpty()) {
            SwingUtil.showWarningDialog(this, "请求ID不能为空");
            return false;
        }
        return true;
    }

    private String extractServiceCode(String value) {
        if (value.startsWith("(")) {
            int endIndex = value.indexOf(")");
            if (endIndex > 0) {
                return value.substring(1, endIndex).trim();
            }
        } else if (value.contains("-")) {
            // 处理 "[用户添加] 06 - 描述" 这种格式
            if (value.startsWith(USER_ADDED_PREFIX)) {
                String[] parts = value.split("-");
                if (parts.length > 0) {
                    String codePart = parts[0].replace(USER_ADDED_PREFIX, "").trim();
                    if (!codePart.isEmpty()) {
                        return codePart;
                    }
                }
            } else {
                // 处理普通带短横线的格式，如 "02 - 扩展诊断会话"
                String[] parts = value.split("-");
                if (parts.length > 0) {
                    return parts[0].trim();
                }
            }
        }
        return "";
    }

    @Override
    public void restoreView() {
        requestIdField.setText(canConfig.getUdsModel().getRequestId());
        responseIdField.setText(canConfig.getUdsModel().getResponseId());

        String jsonTreeData = canConfig.getUdsModel().getTreeData();
        if (jsonTreeData == null || jsonTreeData.isEmpty()) return;

        DefaultMutableTreeNode root = new DefaultMutableTreeNode("【原始报文】");
        DefaultTreeModel model = new DefaultTreeModel(root);
        serviceTree.setModel(model);

        buildOriginalTreeNodes(root); // 构建原始节点

        // 解析 JSON 数据并重建树
        Type listType = new TypeToken<List<TreeNodeData>>(){}.getType();
        List<TreeNodeData> treeDataList = new Gson().fromJson(jsonTreeData, listType);
        
        // 创建映射用于快速查找现有节点
        Map<String, DefaultMutableTreeNode> nodeMap = new HashMap<>();
        collectExistingNodes(root, nodeMap);
        
        // 恢复用户添加的节点
        restoreUserNodesRecursively(root, treeDataList, nodeMap);

        model.reload();
    }

    /**
     * 收集现有树中的所有节点到映射中，便于后续查找匹配
     */
    private void collectExistingNodes(DefaultMutableTreeNode node, Map<String, DefaultMutableTreeNode> nodeMap) {
        for (int i = 0; i < node.getChildCount(); i++) {
            DefaultMutableTreeNode child = (DefaultMutableTreeNode) node.getChildAt(i);
            String nodeName = child.getUserObject().toString();
            nodeMap.put(nodeName, child);
            collectExistingNodes(child, nodeMap);
        }
    }

    /**
     * 递归恢复用户自定义节点，并确保结构正确
     */
    private void restoreUserNodesRecursively(DefaultMutableTreeNode parent, List<TreeNodeData> dataList, Map<String, DefaultMutableTreeNode> nodeMap) {
        for (TreeNodeData data : dataList) {
            // 先尝试从映射中获取已存在的节点（无论是否带USER_ADDED_PREFIX）
            DefaultMutableTreeNode existingNode = nodeMap.get(data.getName());
            
            DefaultMutableTreeNode currentNode;
            if (existingNode == null && data.getName().startsWith(USER_ADDED_PREFIX)) {
                // 如果是用户添加的节点且不存在，则创建新节点
                currentNode = new DefaultMutableTreeNode(data.getName());
                parent.add(currentNode);
                nodeMap.put(data.getName(), currentNode);
            } else if (existingNode != null) {
                // 如果节点已存在，则直接使用现有节点
                currentNode = existingNode;
            } else {
                // 对于非用户添加且不存在的节点，跳过恢复
                continue;
            }
            // 递归恢复子节点，无论节点类型
            if (!data.getChildren().isEmpty()) {
                restoreUserNodesRecursively(currentNode, data.getChildren(), nodeMap);
            }
        }
    }

    private void buildOriginalTreeNodes(DefaultMutableTreeNode root) {
        // 诊断会话控制
        DefaultMutableTreeNode sessionControlNode = new DefaultMutableTreeNode("(10) 诊断会话控制");
        sessionControlNode.add(new DefaultMutableTreeNode("01 - 默认会话"));
        sessionControlNode.add(new DefaultMutableTreeNode("02 - 编程会话"));
        sessionControlNode.add(new DefaultMutableTreeNode("03 - 扩展诊断会话"));
        sessionControlNode.add(new DefaultMutableTreeNode("04 - 安全系统诊断会话"));
        root.add(sessionControlNode);

        // ECU重置
        DefaultMutableTreeNode ecuResetNode = new DefaultMutableTreeNode("(11) ECU重置");
        ecuResetNode.add(new DefaultMutableTreeNode("01 - 硬重置"));
        ecuResetNode.add(new DefaultMutableTreeNode("02 - 点火钥匙关闭/重置"));
        ecuResetNode.add(new DefaultMutableTreeNode("03 - 软重置"));
        ecuResetNode.add(new DefaultMutableTreeNode("04 - 启动快速断电"));
        ecuResetNode.add(new DefaultMutableTreeNode("05 - 禁用快速断电"));
        root.add(ecuResetNode);

        // 清除诊断信息
        root.add(new DefaultMutableTreeNode("(14) 清除诊断信息"));

        // 读取DTC信息
        DefaultMutableTreeNode dtcInfoNode = new DefaultMutableTreeNode("(19) 读取DTC信息");
        dtcInfoNode.add(new DefaultMutableTreeNode("01 - 按状态掩码报告DTC..."));
        dtcInfoNode.add(new DefaultMutableTreeNode("02 - 按状态掩码报告DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("03 - 报告DTC快照标识"));
        dtcInfoNode.add(new DefaultMutableTreeNode("04 - 按DTC编号报告DTC..."));
        dtcInfoNode.add(new DefaultMutableTreeNode("05 - 按记录编号报告DTC..."));
        dtcInfoNode.add(new DefaultMutableTreeNode("06 - 按DTC编号报告DTC扩展数据记录"));
        dtcInfoNode.add(new DefaultMutableTreeNode("07 - 按严重性掩码记录报告DTC编号"));
        dtcInfoNode.add(new DefaultMutableTreeNode("08 - 按严重性掩码记录报告DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("09 - 报告DTC的严重性信息"));
        dtcInfoNode.add(new DefaultMutableTreeNode("0A - 报告受支持的DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("0B - 报告首个测试失败DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("0C - 报告首个确认DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("0D - 报告最新测试失败DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("0E - 报告最新确认的DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("0F - 按状态掩码报告镜像内存DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("10 - 按DTC编号报告镜像内存DTC扩展数据记录"));
        dtcInfoNode.add(new DefaultMutableTreeNode("11 - 按状态掩码报告镜像内存DTC数量"));
        dtcInfoNode.add(new DefaultMutableTreeNode("12 - 按状态掩码报告排放OBD DTC的数量"));
        dtcInfoNode.add(new DefaultMutableTreeNode("13 - 按报告掩码报告排放OBD DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("14 - 报告DTC故障检测计时器"));
        dtcInfoNode.add(new DefaultMutableTreeNode("15 - 报告为永久性状态的DTC"));
        dtcInfoNode.add(new DefaultMutableTreeNode("16 - 按记录编号报告DTC扩展数据记录"));
        dtcInfoNode.add(new DefaultMutableTreeNode("17 - 按状态掩码报告用户定义内存"));
        dtcInfoNode.add(new DefaultMutableTreeNode("18 - 按DTC编号报告用户定义内存DTC快照记录"));
        root.add(dtcInfoNode);

        // 其他服务项（按数字顺序：22, 23, 24, 27, 28, 2A, 2C, 2E, 2F, 31, 34, 3D, 83, 84, 85, 86, 87）
        root.add(new DefaultMutableTreeNode("(22) 按标识符读取数据"));
        root.add(new DefaultMutableTreeNode("(23) 按地址读取内容"));
        root.add(new DefaultMutableTreeNode("(24) 按标识符读取换算数据"));

        // 安全访问
        DefaultMutableTreeNode securityAccessNode = new DefaultMutableTreeNode("(27) 安全访问");
        securityAccessNode.add(new DefaultMutableTreeNode("01 - 请求Seed 1"));
        securityAccessNode.add(new DefaultMutableTreeNode("02 - 发送Key 2"));
        securityAccessNode.add(new DefaultMutableTreeNode("03 - 请求Seed 3"));
        securityAccessNode.add(new DefaultMutableTreeNode("04 - 发送Key 4"));
        securityAccessNode.add(new DefaultMutableTreeNode("05 - 请求Seed 5"));
        securityAccessNode.add(new DefaultMutableTreeNode("06 - 发送Key 6"));
        root.add(securityAccessNode);

        // 通讯控制
        DefaultMutableTreeNode communicationControlNode = new DefaultMutableTreeNode("(28) 通讯控制");
        communicationControlNode.add(new DefaultMutableTreeNode("00 - 启用Rx和Tx"));
        communicationControlNode.add(new DefaultMutableTreeNode("01 - 启动Rx和禁用Tx"));
        communicationControlNode.add(new DefaultMutableTreeNode("02 - 禁用Rx和启用Tx"));
        communicationControlNode.add(new DefaultMutableTreeNode("03 - 禁用Rx和Tx"));
        communicationControlNode.add(new DefaultMutableTreeNode("04 - 根据强化的地址信息启用Rx和禁用Tx"));
        communicationControlNode.add(new DefaultMutableTreeNode("05 - 根据强化的地址信息启用Rx和Tx"));
        root.add(communicationControlNode);

        root.add(new DefaultMutableTreeNode("(2A) 按周期性标识符读取数据"));

        // 动态定义数据标识符
        DefaultMutableTreeNode dynamicDefineDataIdentifierNode = new DefaultMutableTreeNode("(2C) 动态定义数据标识符");
        dynamicDefineDataIdentifierNode.add(new DefaultMutableTreeNode("01 - 按标识符定义"));
        dynamicDefineDataIdentifierNode.add(new DefaultMutableTreeNode("02 - 按内存地址定义"));
        dynamicDefineDataIdentifierNode.add(new DefaultMutableTreeNode("03 - 清除动态定义的数据标识符"));
        root.add(dynamicDefineDataIdentifierNode);

        root.add(new DefaultMutableTreeNode("(2E) 按标识符写数据"));
        root.add(new DefaultMutableTreeNode("(2F) 按标识符的输入输出控制"));

        // 例程控制
        DefaultMutableTreeNode routineControlNode = new DefaultMutableTreeNode("(31) 例程控制");
        routineControlNode.add(new DefaultMutableTreeNode("01 - 启动例程"));
        routineControlNode.add(new DefaultMutableTreeNode("02 - 停止例程"));
        routineControlNode.add(new DefaultMutableTreeNode("03 - 请求例程结果"));
        root.add(routineControlNode);

        root.add(new DefaultMutableTreeNode("(34) 文件下载"));
        root.add(new DefaultMutableTreeNode("(3D) 按地址写内存"));

        // 访问计时参数
        DefaultMutableTreeNode accessTimingParameterNode = new DefaultMutableTreeNode("(83) 访问计时参数");
        accessTimingParameterNode.add(new DefaultMutableTreeNode("01 - 读取扩展的计时参数集"));
        accessTimingParameterNode.add(new DefaultMutableTreeNode("02 - 计时参数设置为默认值"));
        accessTimingParameterNode.add(new DefaultMutableTreeNode("03 - 读取当前活动的计时参数"));
        accessTimingParameterNode.add(new DefaultMutableTreeNode("04 - 计时参数设置为指定值"));
        root.add(accessTimingParameterNode);

        root.add(new DefaultMutableTreeNode("(84) 受保护的数据传输"));

        // 控制DTC设置
        DefaultMutableTreeNode controlDTCSettingNode = new DefaultMutableTreeNode("(85) 控制DTC设置");
        controlDTCSettingNode.add(new DefaultMutableTreeNode("01 - 开"));
        controlDTCSettingNode.add(new DefaultMutableTreeNode("02 - 关"));
        root.add(controlDTCSettingNode);

        // 基于事件响应
        DefaultMutableTreeNode eventResponseNode = new DefaultMutableTreeNode("(86) 基于事件响应");
        eventResponseNode.add(new DefaultMutableTreeNode("00 - 停止基于事件响应"));
        eventResponseNode.add(new DefaultMutableTreeNode("01 - 关于DTC状态更改"));
        eventResponseNode.add(new DefaultMutableTreeNode("02 - 关于计时器中断"));
        eventResponseNode.add(new DefaultMutableTreeNode("03 - 关于数据标识符更改"));
        eventResponseNode.add(new DefaultMutableTreeNode("04 - 报告激活事件"));
        eventResponseNode.add(new DefaultMutableTreeNode("05 - 启动基于事件响应"));
        eventResponseNode.add(new DefaultMutableTreeNode("06 - 清除基于事件响应"));
        eventResponseNode.add(new DefaultMutableTreeNode("07 - 关于值对比"));
        root.add(eventResponseNode);

        // 链路控制
        DefaultMutableTreeNode linkControlNode = new DefaultMutableTreeNode("(87) 链路控制");
        linkControlNode.add(new DefaultMutableTreeNode("01 - 验证能否使用固定参数进行模式转换"));
        linkControlNode.add(new DefaultMutableTreeNode("02 - 验证能否使用特定参数进行模式转换"));
        linkControlNode.add(new DefaultMutableTreeNode("03 - 转换模式"));
        root.add(linkControlNode);
    }


    private void saveTreeToUdsModel() {
        DefaultMutableTreeNode root = (DefaultMutableTreeNode) serviceTree.getModel().getRoot();
        List<TreeNodeData> treeDataList = buildTreeData(root);

        String jsonTreeData = new Gson().toJson(treeDataList);

        UdsModel udsModel = canConfig.getUdsModel();
        udsModel.setTreeData(jsonTreeData);
        canContainer.getDevice().updateConfig(canConfig);
    }

    @Override
    public void createActions() {
        requestIdField.addActionListener(e -> updateRequestId());
        requestIdField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                updateRequestId();
            }
        });
        responseIdField.addActionListener(e -> updateResponseId());
        responseIdField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                updateResponseId();
            }
        });
        functionalAddressField.addActionListener(e -> updateFunctionalId());
        functionalAddressField.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                updateFunctionalId();
            }
        });
        enableFunctionalAddressCheckBox.addActionListener(e -> {
            if (enableFunctionalAddressCheckBox.isSelected()) {
                String functionalAddress = functionalAddressField.getText();
                if (functionalAddress == null || functionalAddress.trim().isEmpty()) {
                    JOptionPane.showMessageDialog(CanUdsInStepView.this, "请填写功能寻址，无法开启");
                    enableFunctionalAddressCheckBox.setSelected(false);
                } else {
                    updateIsFunctional();
                }
            } else {
                updateIsFunctional();
            }
        });
        responseCheckBox.addActionListener(e -> updateIsResponse());
        responseCheckBox.addFocusListener(new FocusAdapter() {
            @Override
            public void focusLost(FocusEvent e) {
                updateIsResponse();
            }
        });
        // 发送3e服务
        enable3eButton.addActionListener(e -> {
            Operation operation = Operation.buildOperation(canContainer.getDevice());
            operation.getOperationTarget().setChannel(getChannel());
            operation.setOperationMethod(DeviceMethods.send3EService);
            operation.setOperationObject(enable3eButton.isSelected());
            responseCheckBox.setEnabled(!enable3eButton.isSelected());
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult != null) {
                if (operationResult.isOk()) {
                    log.info("3e服务发送成功");
                } else {
                    log.info("3e服务发送失败");
                }
            } else {
                log.info("3e服务发送失败");
            }
        });
    }

    private void updateRequestId() {
        canConfig.getUdsModel().setRequestId(requestIdField.getText().trim());
        canContainer.getDevice().updateConfig(canConfig);
    }

    private void updateResponseId() {
        canConfig.getUdsModel().setResponseId(responseIdField.getText().trim());
        canContainer.getDevice().updateConfig(canConfig);
    }

    private void updateFunctionalId() {
        canConfig.getUdsModel().setFunctionId(functionalAddressField.getText().trim());
        canContainer.getDevice().updateConfig(canConfig);
    }

    private void updateIsFunctional() {
        canConfig.getUdsModel().setEnableFunction(enableFunctionalAddressCheckBox.isSelected());
        canContainer.getDevice().updateConfig(canConfig);
    }

    private void updateIsResponse() {
        canConfig.getUdsModel().setEnableResponse(responseCheckBox.isSelected());
        canContainer.getDevice().updateConfig(canConfig);
    }

    @Override
    public Integer getChannel() {
        return channel;
    }
}
