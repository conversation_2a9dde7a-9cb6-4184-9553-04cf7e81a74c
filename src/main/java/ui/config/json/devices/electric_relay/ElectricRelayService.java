package ui.config.json.devices.electric_relay;

import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static sdk.base.BaseHttpClient.postForString;
import static sdk.base.BaseHttpClient.postJsonResponse;

@Slf4j
public class ElectricRelayService {

    /**
     * 将设备延迟时间配置同步到后端服务
     */
    public static void syncDelayTimeToBackend(Map<String, Map<String, String>> deviceDelayConfigs) {
        try {
            String data = postForString(UrlConstants.RelayUrls.UPDATE_RELAY_DELAY_CONFIG, deviceDelayConfigs, true);
            log.info("继电器延迟时间同步成功: {}", data);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新继电器文本配置
     */
    public static void updateRelayTextConfig(ElectricRelayConfig electricRelayConfig) {
        try {
            String data = postForString(UrlConstants.RelayUrls.UPDATE_RELAY_TEXT_CONFIG, electricRelayConfig, true);
            log.info("继电器数据修改成功: {}", data);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新继电器状态配置
     */
    public static void updateRelayStatusConfig(ElectricRelayConfig electricRelayConfig) {
        try {
            String data = postForString(UrlConstants.RelayUrls.UPDATE_RELAY_STATUS_CONFIG, electricRelayConfig, true);
            log.info("继电器状态修改成功: {}", data);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取指定项目和别名的继电器配置数据
     */
    public static ElectricRelayConfig getRelayTextConfig(String project, String aliasName) {
        try {
            // 构建请求参数
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("project", project);
            paramsMap.put("aliasName", aliasName);

            // 发送POST请求获取继电器配置列表
            JsonResponse<List<ElectricRelayConfig>> response = postJsonResponse(UrlConstants.RelayUrls.GET_RELAY_TEXT_CONFIG, paramsMap, new TypeReference<JsonResponse<List<ElectricRelayConfig>>>() {}, null);

            // // 处理响应数据：如果为空则创建默认配置，否则返回第一条数据
            List<ElectricRelayConfig> data = response.getData();
            ElectricRelayConfig electricRelayConfig;
            if (data == null || data.isEmpty()) {
                electricRelayConfig = new ElectricRelayConfig();
                electricRelayConfig.setProject(project);
                electricRelayConfig.setAliasName(aliasName);
            } else {
                electricRelayConfig = data.get(0);
            }
            log.info("继电器数据获取成功: {}", electricRelayConfig.toString());
            return electricRelayConfig;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 二次面板获取指定项目的继电器配置数据
     */
    public static List<ElectricRelayConfig> panelGetRelayTextConfig(String project) {
        try {
            // 构建请求参数
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("project", project);

            // 发送POST请求获取继电器配置列表
            JsonResponse<List<ElectricRelayConfig>> response = postJsonResponse(UrlConstants.RelayUrls.GET_RELAY_TEXT_CONFIG, paramsMap, new TypeReference<JsonResponse<List<ElectricRelayConfig>>>() {}, null);

             // 处理响应数据：如果为空则创建默认配置，否则返回第一条数据
            List<ElectricRelayConfig> dataList = response.getData();
            log.info("二次面板获取继电器数据成功: {}", dataList);
            return dataList;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新继电器连接开关状态
     */
    public static void updateConnectSwitchStatus(ElectricRelayConfig electricRelayConfig) {
        try {
            String data = postForString(UrlConstants.RelayUrls.UPDATE_CONNECT_SWITCH_STATUS, electricRelayConfig, true);
            log.info("继电器连接开关状态修改成功: {}", data);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}