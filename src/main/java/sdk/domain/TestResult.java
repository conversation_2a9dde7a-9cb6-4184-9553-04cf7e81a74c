package sdk.domain;

/**
 * 测试结果
 */

public class TestResult {

    private Integer id;

    private String uuid;

    private Integer sumCycle;

    private Integer testCycle;

    private Integer failCycle;

    private String summary;

    public float getFailRate() {
        return (float) failCycle / testCycle * 100.0f;
    }

    @Override
    public String toString() {
        return String.format("%.2f%%(%d/%d)", getFailRate(), failCycle, testCycle);
    }
}
