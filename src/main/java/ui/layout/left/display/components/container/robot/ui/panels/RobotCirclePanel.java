package ui.layout.left.display.components.container.robot.ui.panels;

import common.constant.UiConstants;
import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.entity.RobotDevice;
import ui.base.BaseView;
import ui.config.json.devices.robot.RobotConfig;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.robot.CirclePoint;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.util.Objects;

/**
 * 画圆面板
 */
public class RobotCirclePanel extends JPanel implements BaseView {

    private final JSpinner radiusSpinner;//半径
    private final JLabel circleCenterLabel;
    private final JButton circleButton;
    private final JButton addToScriptButton;
    private final DeviceContainer deviceContainer;
    private final MainModel mainModel;
    private final RobotDevice robotDevice;

    public RobotCirclePanel(MainModel mainModel, DeviceContainer deviceContainer, RobotConfig robotConfig) {
        radiusSpinner = new JSpinner(new SpinnerNumberModel(0.1, 0.1, Double.MAX_VALUE, 1.0));
        this.deviceContainer = deviceContainer;
        this.mainModel = mainModel;
        robotDevice = (RobotDevice) deviceContainer.getDevice();
        circleCenterLabel = new JLabel();

        circleButton = SwingUtil.getDebugButton();
        addToScriptButton = SwingUtil.getAddToScriptButton();
        createView();
        createActions();
        SwingUtil.setPreferredWidth(radiusSpinner, UiConstants.DEFAULT_SPINNER_WIDTH);
        radiusSpinner.setMaximumSize(radiusSpinner.getPreferredSize());
    }

    @Override
    public void createView() {
        setLayout(new BoxLayout(this, BoxLayout.X_AXIS));
        add(Box.createHorizontalStrut(6));
        add(new JLabel("画圆操作:   "));
        add(circleCenterLabel);
        add(new JLabel("半径: "));
        add(radiusSpinner);
        add(new JLabel("cm"));
        add(circleButton);
        add(addToScriptButton);
        add(Box.createHorizontalGlue());
        setBorder(BorderFactory.createLineBorder(Color.black));
    }

    public void setCircleCenterCoordinate(String circleCenter) {
        circleCenterLabel.setText(circleCenter);
    }

    public void createActions() {
        circleButton.addActionListener(e -> drawCircle());
        addToScriptButton.addActionListener(e -> addToScriptButtonForDrawCircle());

    }

    private void drawCircle() {
        double radius = (double) radiusSpinner.getValue();
        String circleCenterName = circleCenterLabel.getText();
        if (Objects.equals(circleCenterName, "")) {
            JOptionPane.showMessageDialog(this, "请先设置圆心", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        if (radius == 0) {
            JOptionPane.showMessageDialog(this, "请输入半径", "提示", JOptionPane.INFORMATION_MESSAGE);
        } else {
            CirclePoint circlePoint = new CirclePoint();
            circlePoint.setCoordinateName(circleCenterName);
            circlePoint.setRadius(radius);
            Operation operation = Operation.buildOperation(robotDevice);
            operation.setOperationMethod(DeviceMethods.circle);
            operation.setOperationObject(circlePoint);
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isFailed()) {
                SwingUtil.showWebMessageDialog(this, operationResult.getMessage());
            }
        }
    }

    private void addToScriptButtonForDrawCircle() {
        double radius = (double) radiusSpinner.getValue();
        String circleCenterName = circleCenterLabel.getText();
        CirclePoint circlePoint = new CirclePoint();
        circlePoint.setCoordinateName(circleCenterName);
        circlePoint.setRadius(radius);
        Operation operation = Operation.buildOperation(robotDevice);
        operation.setOperationMethod(DeviceMethods.circle);
        operation.setOperationObject(circlePoint);
        mainModel.getOperationModel().updateOperation(operation);
    }
}
