package common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystem;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FileUtils {
    private static final Pattern FILE_NAME_INVALID_PATTERN_WIN = Pattern.compile("[\\\\/:*?\"<>|\r\n]");

    //\/:*?"<>|
    public static String replaceSpecialChar(String fileName) {
        return fileName.replaceAll("\\\\", "{backslash}")
                .replaceAll("/", "{slash}")
                .replaceAll(":", "{colon}")
                .replaceAll("\\*", "{asterisk}")
                .replaceAll("\\?", "{question}")
                .replaceAll("\"", "{double_quotation}")
                .replaceAll("<", "{less_than}")
                .replaceAll(">", "{more_than}")
                .replaceAll("\\|", "{vertical_line}");
    }

    public static File writeFileFromString(String path, String data, boolean append) throws IOException {
        return writeFileFromString(new File(path), data, append);
    }

    public static File writeFileFromString(File file, String data, boolean append) throws IOException {
        if (!file.exists()) {
            file.createNewFile();
        }
        if (data != null) {
            OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(file.getAbsoluteFile(), append), StandardCharsets.UTF_8);
            BufferedWriter bw = new BufferedWriter(osw);
            bw.write(data);
            bw.flush();
            bw.close();
        }
        return file;
    }


    public static String readStringFromFile(String filePath) throws IOException {
        return readStringFromFile(new File(filePath));
    }

    public static String readStringFromFile(File file) throws IOException {
        FileInputStream fin = new FileInputStream(file);
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(fin, StandardCharsets.UTF_8))) {
            String buf;
            while ((buf = reader.readLine()) != null) {
                sb.append(buf).append("\n");
            }
        }
        return sb.toString();
    }

    public static File createFile(String fileName) throws IOException {
        File newFile = new File(fileName);
        if (!newFile.exists()) {
            newFile.createNewFile();
        }
        return newFile;
    }

    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        // 如果文件路径只有单个文件
        if (file.exists() && file.isFile()) {
            return file.delete();
        }
        return false;
    }

    public static boolean deleteAllFile(String dir) {
        File dirFile = new File(dir);
        return deleteAllFile(dirFile);
    }

    public static boolean deleteAllFile(File dirFile) {
        // 如果dir不以文件分隔符结尾，自动添加文件分隔符
//        if (!dir.endsWith(File.separator))
//            dir = dir + File.separator;

        // 如果dir对应的文件不存在，或者不是一个目录，则退出
        if ((!dirFile.exists()) || (!dirFile.isDirectory())) {
            return false;
        }
        boolean flag = true;
        // 删除文件夹中的所有文件包括子文件夹
        File[] files = dirFile.listFiles();
        assert files != null;
        for (File file : files) {
            // 删除子文件
            if (file.isFile()) {
                flag = deleteFile(file.getAbsolutePath());
                if (!flag)
                    break;
            }
            // 删除子文件夹
            else if (file.isDirectory()) {
                flag = deleteAllFile(file.getAbsolutePath());
                if (!flag)
                    break;
            }
        }
        if (!flag) {
            return false;
        }
        // 删除当前文件夹
        return dirFile.delete();
    }


    public static void copyFile(String originalFilePath, String targetFilePath) {
        FileSystem system = FileSystems.getDefault();
        Path original = system.getPath(originalFilePath);
        Path target = system.getPath(targetFilePath);
        try {
            // Throws an exception if the original file is not found.
            Files.copy(original, target, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static String getFileNameByFilePath(String filePath) {
        String[] temp = filePath.replaceAll("\\\\", "/").split("/");
        String fileName = "";
        if (temp.length > 1) {
            fileName = temp[temp.length - 1];
        }
        return fileName;
    }

    public static boolean copyResourceToDirectoryWhenLastModified(String locationPattern, File destination) throws IOException {
        Resource resource = new PathMatchingResourcePatternResolver().getResource(locationPattern);
        return FileUtils.copyResourceToDirectoryWhenLastModified(resource, destination);
    }

    public static boolean copyResourceToDirectoryWhenLastModified(Resource resource, File destination) throws IOException {
        String filename = resource.getFilename();
        assert filename != null;
        File dstFile = new File(destination, filename);
        boolean noCopy = dstFile.exists() && dstFile.lastModified() >= resource.lastModified();
        if (!noCopy) {
            if (!StringUtils.isEmpty(filename)) {
                log.info("拷贝{}到{}", filename, destination);
                org.apache.commons.io.FileUtils.copyToFile(resource.getInputStream(), dstFile);
            }
            return true;
        }
        return false;
    }

    public static String getSystemDownloadPath() {
        String homeDir = System.getProperty("user.home");
        String downloadPath;
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            downloadPath = homeDir + "\\Downloads";
        } else { // 默认处理macOS、Linux及其他类Unix系统
            downloadPath = homeDir + "/Downloads";
        }
        return downloadPath;
    }

    public static String replaceInvalidPathCharacter(String filePath) {
        return FILE_NAME_INVALID_PATTERN_WIN.matcher(filePath).replaceAll("_");
    }

    public static List<Integer> getChannelNumbers(String channelConfigPath) {
        List<Integer> numbers = new ArrayList<>();
        File directory = new File(channelConfigPath);

        // 验证路径是否存在且是目录
        if (!directory.exists() || !directory.isDirectory()) {
            System.err.println("错误: 路径不存在或不是目录 - " + channelConfigPath);
            return numbers;
        }

        File[] files = directory.listFiles();
        if (files == null) {
            System.err.println("错误: 无法访问路径 - " + channelConfigPath);
            return numbers;
        }

        // 正则匹配"channel"开头后跟纯数字的文件夹名称
        Pattern pattern = Pattern.compile("^channel(\\d+)$");

        for (File file : files) {
            if (file.isDirectory()) {
                Matcher matcher = pattern.matcher(file.getName());
                if (matcher.find()) {
                    try {
                        // 提取数字部分并转换为整数
                        int num = Integer.parseInt(matcher.group(1));
                        numbers.add(num);
                    } catch (NumberFormatException e) {
                        System.err.println("警告: 数字转换失败 - " + file.getName());
                    }
                }
            }
        }

        return numbers;
    }


}
