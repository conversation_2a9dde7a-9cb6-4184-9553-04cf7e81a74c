package ui.layout.left.display.components.container.serial;

import lombok.Getter;
import sdk.constants.DeviceType;
import sdk.constants.MonitorType;
import sdk.domain.Device;
import sdk.domain.serial.SerialReceiveMessage;
import sdk.domain.monitor.LogDataMonitorListener;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import sdk.entity.SerialDevice;
import ui.config.json.devices.serial.SerialConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.serial.command.CommandSetsPanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static common.constant.DeviceConstants.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-15 18:20
 * @description :
 * @modified By :
 * @since : 2022-6-15
 */
public class SerialContainer extends DeviceContainer {
    private static final String MAIN_TAB = "常规操作";
    private static final String COMMAND_SET = "便捷发送";
    private static final String ADVANCED_SETTING = "高级设置";
    private static final String POINT_SETTING = "报点设置";
    private final JTabbedPane tabbedPane;
    private final BlockingQueue<Object> messageQueue; // 队列用于添加消息
    private ExecutorService executorService;
    @Getter
    private final SerialReceiveDataView serialDataReceiveView;
    @Getter
    private final SerialControlPanel serialControlPanel;
    private final CommandSetsPanel commandSetsPanel;
    private final SerialPointSettingView serialPointSettingView;
    private final JScrollPane scrollPane;

    private final SerialAdvancedSettingsPanel serialAdvancedSettingsPanel;
    private final AtomicInteger threadCounter = new AtomicInteger(0); // 线程计数器
    public SerialContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        messageQueue = new LinkedBlockingQueue<>(1000); // 队列用于接收和存储数据。
        String projectName = mainModel.getAppInfo().getProject();
        ScreenConfig screenConfig = OperationTargetHolder.getScreenKit().loadConfig(projectName);
        screenConfig.setSerialAliasName(device.getAliasName());
        SerialConfig serialConfig = ((SerialDevice) device).loadConfig(mainModel.getAppInfo().getProject());
        serialDataReceiveView = new SerialReceiveDataView(this, mainModel, serialConfig);
        commandSetsPanel = new CommandSetsPanel(this, mainModel, serialConfig);
        serialControlPanel = new SerialControlPanel(this, mainModel, serialConfig, clientView);
        serialAdvancedSettingsPanel = new SerialAdvancedSettingsPanel(clientView, (SerialDevice) device, serialConfig);
        serialPointSettingView = new SerialPointSettingView(mainModel, this, screenConfig, serialConfig);
        scrollPane = new JScrollPane(serialDataReceiveView);
        tabbedPane = new JTabbedPane();
        createView();
    }

    @Override
    public void createView() {
        super.createView();
        JPanel mainPanel = new JPanel(new BorderLayout());

        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(serialControlPanel, BorderLayout.SOUTH);
        tabbedPane.add(MAIN_TAB, mainPanel);
        tabbedPane.add(COMMAND_SET, commandSetsPanel);
        tabbedPane.add(POINT_SETTING, serialPointSettingView);
        tabbedPane.add(ADVANCED_SETTING, serialAdvancedSettingsPanel);
        add(tabbedPane, BorderLayout.CENTER);
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
        serialControlPanel.controlDisplay(isDeviceConnected);
    }

    /**
     * 设备断开
     */
    @Override
    public void deviceDisconnected(Device device) {

    }

    /**
     * 设备删除 关闭线程池,清空队列
     */
    @Override
    public void deviceRemoved(Device device) {
        // 1. 关闭线程池
        shutdownExecutor();
        // 2. 清空消息队列
        messageQueue.clear();
    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        super.deviceConnected(device, autoOpenChannel);
        if (device.getDeviceType().equals(DeviceType.DEVICE_SERIAL)) {
            // 启动串口日志监控
            ((SerialDevice) device).monitorLog();

            // 线程池
            executorService = Executors.newFixedThreadPool(2, r -> {
                // 获取线程类型
                String type = SERIAL_THREAD_TYPES[threadCounter.getAndIncrement() % SERIAL_THREAD_TYPES.length];
                // 创建对应类型的任务
                Runnable task = createTask(type, device);
                // 构造线程对象
                return new Thread(task, String.format("serialContainerThread-%s-%s", device.getDeviceName(), type));
            });
            // 启动线程池中的所有核心线程
            ((ThreadPoolExecutor) executorService).prestartAllCoreThreads();
        }
    }

    /**
     * 根据任务类型创建对应的Runnable任务
     * @return 对应的Runnable任务对象，如果类型不匹配则返回null
     */
    private Runnable createTask(String type,Device device) {
        switch (type) {
            case SERIAL_WEBSOCKET_MONITOR:
                //  启动WebSocket监听器，监听指定设备的日志数据
                return () -> startWebSocketMonitor(device);
            case SERIAL_MESSAGE_QUEUE:
                // 持续消费消息队列中的消息
                return this::consumeMessagesFromQueue;
        }
        return null;
    }

    /**
     * 启动WebSocket监听器，监听指定设备的日志数据
     *
     * @param device 要监听的设备对象，包含设备别名等信息
     */
    private void startWebSocketMonitor(Device device) {
        //监听串口数据
        OperationTargetHolder.getWebsocketDataMonitor().monitor(device.getAliasName(), MonitorType.LOG_DATA,
                new LogDataMonitorListener() {
                    @Override
                    public void monitorLog(String text) {
                        // 将文本消息加入队列
                        messageQueue.add(text);
                    }

                    @Override
                    public void monitorLog(SerialReceiveMessage message) {
                        // 过滤并处理设备专属的串口接收消息
                        if (message.getDeviceAliasName().equals(device.getAliasName())) {
                            messageQueue.add(message);
                        }
                    }
                });
    }

    /**
     * 持续消费消息队列中的消息，并将其分发给数据接收模型
     * 该方法会循环运行直到线程被中断
     */
    private void consumeMessagesFromQueue() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                // 处理队列消息：根据消息类型调用对应的接收方法
                Object item = messageQueue.take();
                if (item instanceof String) {
                    getMainModel().getDeviceReceiveDataModel().receiveMessage(item.toString());
                } else if (item instanceof SerialReceiveMessage) {
                    getMainModel().getDeviceReceiveDataModel().receiveMessage((SerialReceiveMessage) item);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 安全关闭线程池
     */
    private void shutdownExecutor() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdownNow(); // 立即中断所有线程
        }
    }
}
