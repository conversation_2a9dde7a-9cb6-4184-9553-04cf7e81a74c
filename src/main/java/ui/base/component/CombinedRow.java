package ui.base.component;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;

public class CombinedRow extends JPanel {

    private final JTextField textField;

    private final JButton addNewRowButton;


    public CombinedRow(String labelName) {
        textField = new JTextField(10);
        addNewRowButton = new JButton("+");
        setLayout(new BorderLayout());
        add(new JLabel(labelName), BorderLayout.WEST);
        add(textField, BorderLayout.CENTER);
        add(addNewRowButton, BorderLayout.EAST);
    }

    public void addActionListener(ActionListener listener) {
        addNewRowButton.addActionListener(listener);
    }
}
