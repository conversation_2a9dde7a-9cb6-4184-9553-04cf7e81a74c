package ui.layout.left.display.components.container.power.kikusui.pulseSequence;

import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.entity.PowerDevice;
import ui.base.BaseView;
import ui.config.xml.device.*;
import ui.config.xml.project.ProjectConfiguration;
import ui.layout.left.display.components.container.power.kikusui.KikusuiContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionListener;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PulseSequencePanel extends JPanel implements BaseView {
    private static final int memorySum = 16;
    private JRadioButton[] pulseRadioButtons;
    private JPanel waveFormPanel;
    private JPanel imagesPanel;
    private JLabel testRequirementLabel;
    private JLabel oscillogramLabel;
    private JButton loadPulseButton;
    private JButton stopPulseButton;
    private JButton addToScriptButton;
    private JButton createOrChangeWaveSequenceButton;
    private CreateOrChangePulseSequenceDialog createOrChangePulseSequenceDialog;

    private WavePulse currentWavePulse;
    private List<WavePulse> wavePulseList;

    private final KikusuiDeviceConfig kikusuiDeviceConfig;
    private final ProjectConfiguration projectConfiguration;

    private final PowerDevice powerDevice;
    private final MainModel mainModel;

    public PulseSequencePanel(MainModel mainModel, KikusuiContainer kikusuiContainer) {
        this.mainModel = mainModel;
        powerDevice = (PowerDevice) kikusuiContainer.getDevice();
        wavePulseList = new ArrayList<>();
        kikusuiDeviceConfig = GlobalDeviceConfiguration
                .getInstance()
                .getGlobalDeviceConfig()
                .getKikusuiDeviceConfig();
        projectConfiguration = ProjectConfiguration.getInstance(mainModel.getAppInfo().getProject());
        createView();
        createActions();
        loadConfigByBU();
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        createOrChangeWaveSequenceButton.setEnabled(isDeviceConnected);
        loadPulseButton.setEnabled(isDeviceConnected);
        stopPulseButton.setEnabled(false);
    }

    private void loadConfigByBU() {
        String department = mainModel.getAppInfo().getBuName();
        WaveConfig waveConfig = projectConfiguration.getDeviceXmlConfig().getKikusuiSequenceNameConfig();
        if (waveConfig == null) {
            //未配置
            if (department.contains("ICI")) {
                waveConfig = BuiltinWaveConfig.headUnitWaveConfig;
            } else if (department.contains("ADS")) {
                waveConfig = BuiltinWaveConfig.displayWaveConfig;
            } else {
                String[] optionNames = BuiltinWaveConfig.getSequencesFriendlyNames();
                int index = JOptionPane.showOptionDialog(this, "请选择一个标准脉冲序列", "脉冲序列选择",
                        JOptionPane.DEFAULT_OPTION, JOptionPane.INFORMATION_MESSAGE, null, optionNames, optionNames[0]);
                if (index == -1) {
                    waveConfig = BuiltinWaveConfig.undefinedWaveConfig;
                } else {
                    waveConfig = BuiltinWaveConfig.allWaveConfigs.get(index);
                }
            }
        } else {
            if (waveConfig.getSequenceName().equals("headUnit") || waveConfig.getSequenceName().equals("display")) {
                waveConfig = kikusuiDeviceConfig.getBuiltinWavePulseSequence(waveConfig.getSequenceName());
            } else {
                waveConfig = kikusuiDeviceConfig.getWavePulseSequenceOfUser(waveConfig.getSequenceName());
            }
        }
        loadFromWaveConfig(waveConfig);
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        JPanel panelInScrollPane = new JPanel(new BorderLayout());
        JScrollPane scrollPane = getJScrollPane(panelInScrollPane);

        //左边波形选择区域
        panelInScrollPane.add(getLeftPanel(), BorderLayout.WEST);
        //中间图片区域
        panelInScrollPane.add(getCenterPanel(), BorderLayout.CENTER);
        //添加滚动布局
        add(scrollPane, BorderLayout.CENTER);
        //底部按钮区域
        add(getBottomPanel(), BorderLayout.SOUTH);
    }

    @Override
    public void createActions() {
        createOrChangeWaveSequenceButton.addActionListener(e -> createOrChangePulseSequence());
        loadPulseButton.addActionListener(e -> loadPulse());
        stopPulseButton.addActionListener(e -> powerDevice.stopWavePulse());
        addToScriptButton.addActionListener(e -> addToScript());
    }

    /**
     * 导入波形
     */
    private void loadPulse() {
        if (currentWavePulse == null) {
            return;
        }
        new SwingWorker<OperationResult, Void>() {

            @Override
            protected OperationResult doInBackground() {
                stopPulseButton.setEnabled(true);
                loadPulseButton.setEnabled(false);
                return powerDevice.loadWavePulse(currentWavePulse.getMemory());
            }

            @Override
            protected void done() {
                stopPulseButton.setEnabled(false);
                loadPulseButton.setEnabled(true);
            }
        }.execute();
    }


    /**
     * 创建或修改波形序列对话框
     */
    private void createOrChangePulseSequence() {
        if (createOrChangePulseSequenceDialog == null) {
            createOrChangePulseSequenceDialog = new CreateOrChangePulseSequenceDialog(
                    this,
                    createOrChangeWaveSequenceButton.getText(),
                    kikusuiDeviceConfig,
                    this::loadFromWaveConfig);
        }
        createOrChangePulseSequenceDialog.showDialog();
    }


    private void addToScript() {
        if (currentWavePulse == null) {
            return;
        }
        Operation operation = Operation.buildOperation(powerDevice);
        operation.setOperationMethod(DeviceMethods.loadWavePulse);
        operation.setOperationObject(currentWavePulse.getMemory());
        operation.setFriendlyOperationObject(currentWavePulse.getName());
        mainModel.getOperationModel().updateOperation(operation);
    }

    private JScrollPane getJScrollPane(JPanel panel) {
        JScrollPane scrollPane = new JScrollPane(panel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        return scrollPane;
    }

    private JPanel getLeftPanel() {
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.setBorder(BorderFactory.createEmptyBorder());
        //波形类型panel 装radiobutton
        waveFormPanel = new JPanel(new GridLayout(16, 1));
        waveFormPanel.setBorder(BorderFactory.createTitledBorder("波形名称"));
        waveFormPanel.setMinimumSize(new Dimension(200, 0));
        leftPanel.add(waveFormPanel);
        ButtonGroup buttonGroup = new ButtonGroup();
        pulseRadioButtons = new JRadioButton[memorySum];
        //生成波形选择按钮
        for (int i = 0; i < memorySum; i++) {
            pulseRadioButtons[i] = new JRadioButton();
            pulseRadioButtons[i].setMargin(new Insets(0, 20, 0, 0));
            pulseRadioButtons[i].setEnabled(false);
            buttonGroup.add(pulseRadioButtons[i]);
            waveFormPanel.add(pulseRadioButtons[i]);

        }
        return leftPanel;
    }

    private JPanel getCenterPanel() {
        JPanel centerPanel = new JPanel(new BorderLayout());
        imagesPanel = new JPanel(new GridLayout(2, 1));

        centerPanel.setBorder(BorderFactory.createEmptyBorder());
        centerPanel.add(imagesPanel, BorderLayout.WEST);
        testRequirementLabel = new JLabel();
        testRequirementLabel.setOpaque(true);
        oscillogramLabel = new JLabel();
        oscillogramLabel.setOpaque(true);
        imagesPanel.add(testRequirementLabel);
        imagesPanel.add(oscillogramLabel);
        return centerPanel;
    }

    private JPanel getBottomPanel() {
        JPanel btnPanel = new JPanel(new GridLayout(1, 4));
        createOrChangeWaveSequenceButton = new JButton("创建或切换脉冲序列");
        loadPulseButton = new JButton("加载脉冲");
        stopPulseButton = new JButton("停止脉冲");
        addToScriptButton = SwingUtil.addNewScriptButton("添加到脚本步骤");
        btnPanel.add(createOrChangeWaveSequenceButton);
        btnPanel.add(loadPulseButton);
        btnPanel.add(stopPulseButton);
        btnPanel.add(addToScriptButton);
        stopPulseButton.setEnabled(false);
        return btnPanel;
    }

    /**
     * 从波形配置导入
     *
     * @param waveConfig 波形配置
     */
    private void loadFromWaveConfig(WaveConfig waveConfig) {
        if (!waveConfig.isValid()) {
            return;
        }
        int imageWidth = 550;
        int imageHeight = 250;
        wavePulseList = waveConfig.getPulses();
        //移除波形选择按钮ActionListener
        for (JRadioButton pulseRadioButton : pulseRadioButtons) {
            if (pulseRadioButton != null) {
                for (ActionListener listener : pulseRadioButton.getActionListeners()) {
                    pulseRadioButton.removeActionListener(listener);
                }
            }
        }
        waveFormPanel.setBorder(BorderFactory.createTitledBorder(waveConfig.getFriendlyName()));
        int index = 0;
        for (WavePulse wavePulse : wavePulseList) {
            pulseRadioButtons[index].setText(String.format("%02d.%s", wavePulse.getMemory(), wavePulse.getName()));
            pulseRadioButtons[index].setEnabled(!wavePulse.getName().trim().isEmpty());
            int finalIndex = index;
            pulseRadioButtons[index].addActionListener(e -> {
                ImageIcon testRequirementImg, oscillogramImg;
                if (pulseRadioButtons[finalIndex].isSelected()) {
                    currentWavePulse = wavePulseList.get(finalIndex);
                    imagesPanel.setBorder(BorderFactory.createTitledBorder(String.format("内存位置:%d", currentWavePulse.getMemory())));
                    if (waveConfig.getSequenceName().equals("headUnit") || waveConfig.getSequenceName().equals("display")) {
                        //系统内置波形
                        testRequirementImg = SwingUtil.getResourceAsImageIcon(wavePulse.fetchBuiltinTestRequirement(waveConfig.getSequenceName()));
                        oscillogramImg = SwingUtil.getResourceAsImageIcon(wavePulse.fetchBuiltinOscillogram(waveConfig.getSequenceName()));
                    } else {
                        //用户定义波形
                        File file = kikusuiDeviceConfig.getSequenceFolder().getFolder(waveConfig.getSequenceName()).toFile();
                        testRequirementImg = new ImageIcon(new File(file, wavePulse.getTestRequirement()).getAbsolutePath());
                        oscillogramImg = new ImageIcon(new File(file, wavePulse.getOscillogram()).getAbsolutePath());
                    }

                    //配置图片显示
                    if (testRequirementImg != null && testRequirementImg.getImage() != null) {
                        testRequirementImg.setImage(testRequirementImg.getImage().getScaledInstance(imageWidth, imageHeight, Image.SCALE_DEFAULT));
                    }
                    if (oscillogramImg != null && oscillogramImg.getImage() != null)
                        oscillogramImg.setImage(oscillogramImg.getImage().getScaledInstance(imageWidth, imageHeight, Image.SCALE_DEFAULT));

                    if (testRequirementImg == null) {
                        testRequirementLabel.setPreferredSize(new Dimension(imageWidth, imageHeight));
                    }
                    if (oscillogramImg == null) {
                        oscillogramLabel.setPreferredSize(new Dimension(imageWidth, imageHeight));
                    }
                    testRequirementLabel.setIcon(testRequirementImg);
                    oscillogramLabel.setIcon(oscillogramImg);
                }
            });
            index++;
        }
        pulseRadioButtons[0].doClick();
        projectConfiguration.getDeviceXmlConfig().setKikusuiSequenceNameConfig(waveConfig).save();
    }
}
