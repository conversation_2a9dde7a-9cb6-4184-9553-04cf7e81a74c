package ui.layout.left.display.components.container.screen;

import sdk.base.JsonResponse;
import sdk.domain.robot.FeedbackData;
import sdk.domain.robot.MoveEntity;
import sdk.domain.robot.RobotCoordinates;
import sdk.domain.screen.PreciousPoint;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.RobotDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.robot.ui.CoordinatesEditModel;
import ui.layout.left.display.components.container.robot.ui.RobotContainer;
import ui.layout.left.display.components.container.robot.ui.RobotCoordinatesTable;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.math.BigDecimal;
import java.util.List;

public class VertexSettingPanel extends ScreenManager implements BaseView {
    private final JLabel leftTopXLabel;
    private final JLabel leftTopYLabel;
    private final JLabel leftTopZLabel;
    private final JLabel leftTopRLabel;

    private final JLabel leftBottomXLabel;
    private final JLabel leftBottomYLabel;
    private final JLabel leftBottomZLabel;
    private final JLabel leftBottomRLabel;

    private final JLabel rightTopXLabel;
    private final JLabel rightTopYLabel;
    private final JLabel rightTopZLabel;
    private final JLabel rightTopRLabel;

    private final JLabel rightBottomXLabel;
    private final JLabel rightBottomYLabel;
    private final JLabel rightBottomZLabel;
    private final JLabel rightBottomRLabel;


    private final JButton setLeftBottomBtn;
    private final JButton setLeftTopBtn;
    private final JButton setRightTopBtn;
    private final JButton setRightBottomBtn;

    private final JButton runToLeftTopBtn;
    private final JButton runToLeftBottomBtn;
    private final JButton runToRightTopBtn;
    private final JButton runToRightBottomBtn;

    private final JButton clearAllBtn;


    private final ScreenConfig screenConfig;

    private final MainModel mainModel;
    private final RobotDevice robotDevice;
    private final RobotContainer robotContainer;

    public VertexSettingPanel(MainModel mainModel, RobotContainer robotContainer, ScreenConfig screenConfig) {
        this.screenConfig = screenConfig;
        this.robotContainer = robotContainer;
        this.mainModel = mainModel;
        robotDevice = (RobotDevice) robotContainer.getDevice();

        leftTopXLabel = new JLabel("X:");
        leftTopXLabel.setMinimumSize(new Dimension(130, 20));
        leftTopYLabel = new JLabel("Y:");
        leftTopYLabel.setMinimumSize(new Dimension(130, 20));
        leftTopZLabel = new JLabel("Z:");
        leftTopZLabel.setMinimumSize(new Dimension(130, 20));
        leftTopRLabel = new JLabel("R:");
        leftTopRLabel.setMinimumSize(new Dimension(130, 20));

        leftBottomXLabel = new JLabel("X:");
        leftBottomXLabel.setMinimumSize(new Dimension(130, 20));
        leftBottomYLabel = new JLabel("Y:");
        leftBottomYLabel.setMinimumSize(new Dimension(130, 20));
        leftBottomZLabel = new JLabel("Z:");
        leftBottomZLabel.setMinimumSize(new Dimension(130, 20));
        leftBottomRLabel = new JLabel("R:");
        leftBottomRLabel.setMinimumSize(new Dimension(130, 20));

        rightTopXLabel = new JLabel("X:");
        rightTopXLabel.setMinimumSize(new Dimension(130, 20));
        rightTopYLabel = new JLabel("Y:");
        rightTopXLabel.setMinimumSize(new Dimension(130, 20));
        rightTopZLabel = new JLabel("Z:");
        rightTopZLabel.setMinimumSize(new Dimension(130, 20));
        rightTopRLabel = new JLabel("R:");
        rightTopRLabel.setMinimumSize(new Dimension(130, 20));

        rightBottomXLabel = new JLabel("X:");
        rightTopXLabel.setMinimumSize(new Dimension(130, 20));
        rightBottomYLabel = new JLabel("Y:");
        rightTopXLabel.setMinimumSize(new Dimension(130, 20));
        rightBottomZLabel = new JLabel("Z:");
        rightTopZLabel.setMinimumSize(new Dimension(130, 20));
        rightBottomRLabel = new JLabel("R:");
        rightTopRLabel.setMinimumSize(new Dimension(130, 20));

        String runTo = "运行到该坐标";
        setLeftBottomBtn = new JButton("设置左下角坐标");
        runToLeftBottomBtn = new JButton(runTo);
        setLeftTopBtn = new JButton("设置左上角坐标");
        runToLeftTopBtn = new JButton(runTo);
        setRightTopBtn = new JButton("设置右上角坐标");
        runToRightTopBtn = new JButton(runTo);
        setRightBottomBtn = new JButton("设置右下角坐标");
        runToRightBottomBtn = new JButton(runTo);
        clearAllBtn = new JButton("清除所有坐标");

        createView();
        restoreView();
        createActions();
    }

    @Override
    public void createView() {
        JPanel panel = new JPanel(new GridBagLayout());
        int gridY = 0;
        panel.add(new JLabel("左上角坐标:"), new GridBagConstraints(0, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftTopXLabel, new GridBagConstraints(1, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftTopYLabel, new GridBagConstraints(2, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftTopZLabel, new GridBagConstraints(3, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftTopRLabel, new GridBagConstraints(4, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(setLeftTopBtn, new GridBagConstraints(5, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(runToLeftTopBtn, new GridBagConstraints(6, gridY++, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));

        panel.add(new JLabel("右上角坐标:"), new GridBagConstraints(0, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightTopXLabel, new GridBagConstraints(1, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightTopYLabel, new GridBagConstraints(2, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightTopZLabel, new GridBagConstraints(3, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightTopRLabel, new GridBagConstraints(4, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(setRightTopBtn, new GridBagConstraints(5, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(runToRightTopBtn, new GridBagConstraints(6, gridY++, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));

        panel.add(new JLabel("左下角坐标:"), new GridBagConstraints(0, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftBottomXLabel, new GridBagConstraints(1, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftBottomYLabel, new GridBagConstraints(2, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftBottomZLabel, new GridBagConstraints(3, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(leftBottomRLabel, new GridBagConstraints(4, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(setLeftBottomBtn, new GridBagConstraints(5, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(runToLeftBottomBtn, new GridBagConstraints(6, gridY++, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));

        panel.add(new JLabel("右下角坐标:"), new GridBagConstraints(0, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightBottomXLabel, new GridBagConstraints(1, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightBottomYLabel, new GridBagConstraints(2, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightBottomZLabel, new GridBagConstraints(3, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(rightBottomRLabel, new GridBagConstraints(4, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(setRightBottomBtn, new GridBagConstraints(5, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));
        panel.add(runToRightBottomBtn, new GridBagConstraints(6, gridY, 1, 1, 0, 0, GridBagConstraints.WEST, GridBagConstraints.BOTH, new Insets(0, 0, 5, 5), 1, 1));

        add(panel);
        add(clearAllBtn);
        setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), "屏幕顶点设置", TitledBorder.LEADING, TitledBorder.TOP, null, null));
    }

    @Override
    public void restoreView() {
        PreciousPoint leftTop = screenConfig.getDisplay().getLeftTop();
        if (leftTop != null) {
            leftTopPoint.setX(leftTop.getX());
            leftTopPoint.setY(leftTop.getY());
            leftTopXLabel.setText("X:" + toFriendLyShow(leftTopPoint.getX()));
            leftTopYLabel.setText("Y:" + toFriendLyShow(leftTopPoint.getY()));
            leftTopZLabel.setText("Z:" + toFriendLyShow(leftTop.getZ()));
            leftTopRLabel.setText("R:" + toFriendLyShow(leftTop.getR()));
        }

        PreciousPoint rightTop = screenConfig.getDisplay().getRightTop();
        if (rightTop != null) {
            rightTopPoint.setX(rightTop.getX());
            rightTopPoint.setY(rightTop.getY());
            rightTopXLabel.setText("X:" + toFriendLyShow(rightTopPoint.getX()));
            rightTopYLabel.setText("Y:" + toFriendLyShow(rightTopPoint.getY()));
            rightTopZLabel.setText("Z:" + toFriendLyShow(rightTop.getZ()));
            rightTopRLabel.setText("R:" + toFriendLyShow(rightTop.getR()));
        }

        PreciousPoint leftBottom = screenConfig.getDisplay().getLeftBottom();
        if (leftBottom != null) {
            leftBottomPoint.setX(leftBottom.getX());
            leftBottomPoint.setY(leftBottom.getY());
            leftBottomXLabel.setText("X:" + toFriendLyShow(leftBottomPoint.getX()));
            leftBottomYLabel.setText("Y:" + toFriendLyShow(leftBottomPoint.getY()));
            leftBottomZLabel.setText("Z:" + toFriendLyShow(leftBottom.getZ()));
            leftBottomRLabel.setText("R:" + toFriendLyShow(leftBottom.getR()));
        }

        PreciousPoint rightBottom = screenConfig.getDisplay().getRightBottom();
        if (rightBottom != null) {
            rightBottomPoint.setX(rightBottom.getX());
            rightBottomPoint.setY(rightBottom.getY());
            rightBottomXLabel.setText("X:" + toFriendLyShow(rightBottomPoint.getX()));
            rightBottomYLabel.setText("Y:" + toFriendLyShow(rightBottomPoint.getY()));
            rightBottomZLabel.setText("Z:" + toFriendLyShow(rightBottom.getZ()));
            rightBottomRLabel.setText("R:" + toFriendLyShow(rightBottom.getR()));
        }


    }

    @Override
    public void createActions() {
        runToLeftBottomBtn.addActionListener(e -> {
            MoveEntity moveEntity = new MoveEntity();
            moveEntity.setX(leftBottomPoint.getX().doubleValue());
            moveEntity.setY(leftBottomPoint.getY().doubleValue());
            moveEntity.setZ(screenConfig.getDisplay().getLeftBottom().getZ().doubleValue());
            moveEntity.setR(screenConfig.getDisplay().getLeftBottom().getR().doubleValue());
//            robot.enableRobot();
            robotDevice.touch(moveEntity);
        });
        runToLeftTopBtn.addActionListener(e -> {
            MoveEntity moveEntity = new MoveEntity();
            moveEntity.setX(leftTopPoint.getX().doubleValue());
            moveEntity.setY(leftTopPoint.getY().doubleValue());
            moveEntity.setZ(screenConfig.getDisplay().getLeftTop().getZ().doubleValue());
            moveEntity.setR(screenConfig.getDisplay().getLeftTop().getR().doubleValue());
//            robot.enableRobot();
            robotDevice.touch(moveEntity);
        });
        runToRightTopBtn.addActionListener(e -> {
            MoveEntity moveEntity = new MoveEntity();
            moveEntity.setX(rightTopPoint.getX().doubleValue());
            moveEntity.setY(rightTopPoint.getY().doubleValue());
            moveEntity.setZ(screenConfig.getDisplay().getRightTop().getZ().doubleValue());
            moveEntity.setR(screenConfig.getDisplay().getRightTop().getR().doubleValue());
//            robot.enableRobot();
            robotDevice.touch(moveEntity);
        });
        runToRightBottomBtn.addActionListener(e -> {
            MoveEntity moveEntity = new MoveEntity();
            moveEntity.setX(rightBottomPoint.getX().doubleValue());
            moveEntity.setY(rightBottomPoint.getY().doubleValue());
            moveEntity.setZ(screenConfig.getDisplay().getRightBottom().getZ().doubleValue());
            moveEntity.setR(screenConfig.getDisplay().getRightBottom().getR().doubleValue());
//            robot.enableRobot();
            robotDevice.touch(moveEntity);
        });


        setLeftBottomBtn.addActionListener(e -> {
            FeedbackData data = robotDevice.fetchFeedbackData();
            double x = data.getX();
            double y = data.getY();
            double z = data.getZ();
            double r = data.getR();
            leftBottomXLabel.setText("X:" + toFriendLyShow(x));
            leftBottomYLabel.setText("Y:" + toFriendLyShow(y));
            leftBottomZLabel.setText("Z:" + toFriendLyShow(z));
            leftBottomRLabel.setText("R:" + toFriendLyShow(r));
            screenConfig.getDisplay().setLeftBottom(new PreciousPoint(x, y, z, r));
            screenKitManager.updateConfig(screenConfig);
            leftBottomPoint.setX(BigDecimal.valueOf(x));
            leftBottomPoint.setY(BigDecimal.valueOf(y));
            performVertexChange();
            storeCoordinatesWithDetails("左下角坐标", x, y, z, r);
        });
        setLeftTopBtn.addActionListener(e -> {
            FeedbackData data = robotDevice.fetchFeedbackData();
            double x = data.getX();
            double y = data.getY();
            double z = data.getZ();
            double r = data.getR();
            leftTopXLabel.setText("X:" + toFriendLyShow(x));
            leftTopYLabel.setText("Y:" + toFriendLyShow(y));
            leftTopZLabel.setText("Z:" + toFriendLyShow(z));
            leftTopRLabel.setText("R:" + toFriendLyShow(r));
            screenConfig.getDisplay().setLeftTop(new PreciousPoint(x, y, z, r));
            screenKitManager.updateConfig(screenConfig);
            leftTopPoint.setX(BigDecimal.valueOf(x));
            leftTopPoint.setY(BigDecimal.valueOf(y));
            performVertexChange();
            storeCoordinatesWithDetails("左上角坐标", x, y, z, r);
        });
        setRightTopBtn.addActionListener(e -> {
            FeedbackData data = robotDevice.fetchFeedbackData();
            double x = data.getX();
            double y = data.getY();
            double z = data.getZ();
            double r = data.getR();
            rightTopXLabel.setText("X:" + toFriendLyShow(x));
            rightTopYLabel.setText("Y:" + toFriendLyShow(y));
            rightTopZLabel.setText("Z:" + toFriendLyShow(z));
            rightTopRLabel.setText("R:" + toFriendLyShow(r));
            screenConfig.getDisplay().setRightTop(new PreciousPoint(x, y, z, r));
            screenKitManager.updateConfig(screenConfig);
            rightTopPoint.setX(BigDecimal.valueOf(x));
            rightTopPoint.setY(BigDecimal.valueOf(y));
            performVertexChange();
            storeCoordinatesWithDetails("右上角坐标", x, y, z, r);
        });
        setRightBottomBtn.addActionListener(e -> {
            FeedbackData data = robotDevice.fetchFeedbackData();
            double x = data.getX();
            double y = data.getY();
            double z = data.getZ();
            double r = data.getR();
            rightBottomXLabel.setText("X:" + toFriendLyShow(x));
            rightBottomYLabel.setText("Y:" + toFriendLyShow(y));
            rightBottomZLabel.setText("Z:" + toFriendLyShow(z));
            rightBottomRLabel.setText("R:" + toFriendLyShow(r));
            screenConfig.getDisplay().setRightBottom(new PreciousPoint(x, y, z, r));
            screenKitManager.updateConfig(screenConfig);
            rightBottomPoint.setX(BigDecimal.valueOf(x));
            rightBottomPoint.setY(BigDecimal.valueOf(y));
            performVertexChange();
            storeCoordinatesWithDetails("右下角坐标", x, y, z, r);
        });
        clearAllBtn.addActionListener(e -> {
            screenConfig.getDisplay().setLeftBottom(new PreciousPoint());
            screenConfig.getDisplay().setLeftTop(new PreciousPoint());
            screenConfig.getDisplay().setRightTop(new PreciousPoint());
            screenConfig.getDisplay().setRightBottom(new PreciousPoint());
            screenKitManager.updateConfig(screenConfig);
            restoreView();
            performVertexChange();

        });
    }


    private void storeCoordinatesWithDetails(String coordinatesName, double x, double y, double z, double r) {
        coordinatesName = coordinatesName.trim();
        if (coordinatesName.isEmpty()) {
            SwingUtil.showWarningDialog(robotContainer.getRobotOperationPanel().getRobotControlPanel().getDeviceContainer(), "坐标名称不能为空");
            return;
        }

        RobotCoordinatesTable table = getRobotCoordinatesTable();
        RobotCoordinates robotCoordinates = createRobotCoordinates(x, y, z, r);

        if (isSlideRailAvailable()) {
            robotCoordinates.setSlideRail(getSlideRailDistance());
        }

        setCommonCoordinatesProperties(robotCoordinates);

        if (isCoordinateNameDuplicate(coordinatesName, table)) {
            SwingUtil.showWarningDialog(robotContainer.getRobotOperationPanel().getRobotControlPanel().getDeviceContainer(), "存在相同坐标名");
            return;
        }

        // 定义好坐标名，上传
        robotCoordinates.setName(coordinatesName);
        JsonResponse<RobotCoordinates> resp = robotDevice.addCoordinates(robotCoordinates);
        if (resp.isOk()) {
            // 返回正常
            table.addRowAtSelectedPos(resp.getData());
        } else {
            SwingUtil.showWebMessageDialog(robotContainer, resp.getMessage());
        }
    }

    private RobotCoordinatesTable getRobotCoordinatesTable() {
        return robotContainer.getRobotOperationPanel().getRobotActionPanel().getRobotCoordinatesTable();
    }

    private RobotCoordinates createRobotCoordinates(double x, double y, double z, double r) {
        RobotCoordinates robotCoordinates = new RobotCoordinates();
        robotCoordinates.setX(x);
        robotCoordinates.setY(y);
        robotCoordinates.setZ(z);
        robotCoordinates.setR(r);
        return robotCoordinates;
    }

    private boolean isSlideRailAvailable() {
        return robotContainer.getRobotOperationPanel().getRobotControlPanel().isContainSlideRail();
    }

    private double getSlideRailDistance() {
        return robotContainer.getRobotOperationPanel().getRobotControlPanel().getRobotSlideRailPanel().getSlideRailDistance();
    }

    private void setCommonCoordinatesProperties(RobotCoordinates robotCoordinates) {
        robotCoordinates.setProjectName(mainModel.getAppInfo().getProject());
        robotCoordinates.setDeviceUniqueCode(robotDevice.getDeviceUniqueCode());
    }

    private boolean isCoordinateNameDuplicate(String coordinatesName, RobotCoordinatesTable table) {
        List<String> nameList = table.getColumnList(CoordinatesEditModel.name.getColumnIndex(), String.class);
        return nameList.contains(coordinatesName);
    }

}
