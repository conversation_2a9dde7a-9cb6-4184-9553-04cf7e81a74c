package ui.layout.right.components.operate.header;

import common.constant.AppConstants;
import lombok.Getter;
import ui.base.BaseView;

import javax.swing.*;
import java.awt.*;

/**
 * @author: Haiyu
 * @description: TCP客户端地址端口号组件
 * @date: 2024/12/9 10:04
 */

@Getter
public class AddressPortClientHeaderHookComponent extends HeaderHookComponent implements BaseView {
    private final JTextField addressTextField = new JTextField(15);
    private final JSpinner portSpinner = new JSpinner(new SpinnerNumberModel(AppConstants.DEFAULT_PORT, 0, 65535, 1));

    public AddressPortClientHeaderHookComponent() {
        this(false);
    }

    public AddressPortClientHeaderHookComponent(boolean delayLoad) {
        setLayout(new FlowLayout(FlowLayout.LEFT));
        if (!delayLoad) {
            createView();
            createActions();
        }
    }

    @Override
    public void createView() {
        add(new JLabel("端口号:"));
        add(portSpinner);
    }

    /**
     * 设置端口号
     *
     * @param port 端口号
     */
    public void setIpPort(int port) {
        portSpinner.setValue(port);
    }

    /**
     * 获取端口号
     *
     * @return 端口号
     */
    public int getIpPort() {
        return (Integer) portSpinner.getValue();
    }


}
