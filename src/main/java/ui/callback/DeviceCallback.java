package ui.callback;

import sdk.domain.Device;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-27 18:52
 * @description :
 * @modified By :
 * @since : 2022-6-27
 */
//TODO：合并到DeviceManageObserver
public interface DeviceCallback {
    void deviceConnected(Device device, boolean autoOpenChannel);

    default void deviceChannelConnected(Device device) {

    }

    void deviceDisconnected(Device device);

    /**
     * 删除设备时执行该方法 释放资源
     * @param device 被删除的设备
     */
    default void deviceRemoved(Device device){};
}
