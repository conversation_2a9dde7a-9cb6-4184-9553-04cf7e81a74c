package ui.layout.left.display.components.container.can;

import cn.hutool.core.collection.CollectionUtil;
import com.desaysv.CANDbcPanel;
import com.desaysv.receivepanel.callback.CanDbcReceiveReturnListener;
import com.desaysv.receivepanel.model.R.CANMessageR;
import com.desaysv.receivepanel.model.S.CANMessage;
import com.desaysv.receivepanel.model.S.CANMessageDeserializer;
import com.desaysv.receivepanel.model.dto.RealTimeSaveConfigDto;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import javafx.embed.swing.JFXPanel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.SseSession;
import sdk.base.operation.Operation;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.bus.*;
import sdk.entity.CanDevice;
import sdk.entity.ResistanceDevice;
import ui.base.BaseView;
import ui.layout.left.display.components.container.can.model.DbcPathModel;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static common.constant.DeviceConstants.*;

@Slf4j
public class CanDbcReceiveSettingView extends BaseCanDbcSettingView implements DbcFileObserver, BaseView {

    private String savePath;
    private CanContainer canContainer;
    private SseSession sseSession;
    private ExecutorService executorService;
    private BlockingQueue<String> messageQueue;
    private BlockingQueue<String> messageListQueue;
    private int receiverChannel;
    private List<String> dbcPaths;
    @Getter
    private CANDbcPanel canPanel;
    private MainModel mainModel;
    private boolean skipControllerUpdate = false; //防止重复调用标识
    private static final Gson gson = new GsonBuilder()
            .registerTypeAdapter(CANMessage.class, new CANMessageDeserializer())
            .create();
    private final AtomicInteger threadCounter = new AtomicInteger(0); // 线程计数器

    public CanDbcReceiveSettingView(CanContainer canContainer, MainModel mainModel, CanConfig canConfig, int channel, String savePath, DbcPathModel model, boolean secondPanel) {
        super(canContainer, channel, canConfig, model);
        if (secondPanel) {
            this.receiverChannel = Integer.parseInt(String.format("%d%d", channel, channel));
        } else {
            this.receiverChannel = channel;
        }
        this.savePath = savePath;
        this.mainModel = mainModel;
        this.canContainer = canContainer;
        DbcConfig dbcConfig = canConfig.getDbcConfigs().get(channel + "");
        if (dbcConfig != null) {
            this.dbcPaths = dbcConfig.getDbcPaths();
        }
        DbcFileManager.getInstance().addObserver(channel, canContainer.getDevice().getDeviceName(), this);
        sseSession = new SseSession();
        JFXPanel jfxPanel = new JFXPanel();
        centerPanel.add(jfxPanel, BorderLayout.CENTER);
        messageQueue = new LinkedBlockingQueue<>(1000);
        messageListQueue = new LinkedBlockingQueue<>(1000);

        //  创建并初始化一个固定大小的线程池，用于处理CAN DBC接收任务
        executorService = Executors.newFixedThreadPool(4, r -> {
            // 获取线程类型
            String type = THREAD_TYPES[threadCounter.getAndIncrement() % THREAD_TYPES.length];
            // 创建对应类型的任务
            Runnable task = createTask(type);
            // 构造线程对象
            return new Thread(task, String.format("CanDbcReceiveThread-%d-%s", channel, type));
        });
        createView();
    }

//多态


    public CanDbcReceiveSettingView(CanContainer canContainer, MainModel mainModel, CanConfig canConfig, int channel, String savePath, DbcPathModel model) {
        super(canContainer, channel, canConfig, model);
        this.savePath = savePath;
        this.mainModel = mainModel;
        this.canContainer = canContainer;
        DbcConfig dbcConfig = canConfig.getDbcConfigs().get(channel + "");
        if (dbcConfig != null) {
            this.dbcPaths = dbcConfig.getDbcPaths();
        }
        DbcFileManager.getInstance().addObserver(channel, canContainer.getDevice().getDeviceName(), this);
        sseSession = new SseSession();
        JFXPanel jfxPanel = new JFXPanel();
        centerPanel.add(jfxPanel, BorderLayout.CENTER);
        messageQueue = new LinkedBlockingQueue<>(1000);
        messageListQueue = new LinkedBlockingQueue<>(1000);

        //  创建并初始化一个固定大小的线程池，用于处理CAN DBC接收任务
        executorService = Executors.newFixedThreadPool(4, r -> {
            // 获取线程类型
            String type = THREAD_TYPES[threadCounter.getAndIncrement() % THREAD_TYPES.length];
            // 创建对应类型的任务
            Runnable task = createTask(type);
            // 构造线程对象
            return new Thread(task, String.format("CanDbcReceiveThread-%d-%s", channel, type));
        });
        createView();
    }

    @Override
    public void createView() {
        canPanel = new CANDbcPanel(channel, savePath, dbcPaths, new CanDbcReceiveReturnListener() {

            @Override
            public void stop() {
                ((CanDevice) canContainer.getDevice()).stopDbcReceiver(channel);
            }

            @Override
            public void start() {
                ((CanDevice) canContainer.getDevice()).startDbcReceiver(channel, new DbcConfig(dbcPaths));
            }

            @Override
            public void onSelectDbcFile(List<String> filePaths) {
                skipControllerUpdate = true;
                updateUIComponents(filePaths);
                DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), filePaths);
            }

            @Override
            public void onSendMessage(CANMessageR canMessageR) {
                ((CanDevice) canContainer.getDevice()).sendCanMessage(channel, toMessage(canMessageR));

            }

            @Override
            public void stopSendMessage(CANMessageR canMessageR) {
                ((CanDevice) canContainer.getDevice()).stopCanMessage(channel, Integer.decode(canMessageR.getIdHex()));

            }

            @Override
            public void stopSendMessages(List<CANMessageR> canMessageRList) {
                for (CANMessageR msg : canMessageRList) {
                    ((CanDevice) canContainer.getDevice()).stopCanMessage(channel, Integer.decode(msg.getIdHex()));
                }
            }

            @Override
            public void startRealTimeDataForDbc(RealTimeSaveConfigDto dto) {
                CanMessageRealTimeSave canSave = new CanMessageRealTimeSave();
                canSave.setFilePath(dto.getFilePath());
                canSave.setFilterType(dto.getFilterType());
                canSave.setFilterText(dto.getFilterText());
                canSave.setSaveType(dto.getSaveType());
                canSave.setFileSizeMB(dto.getFileSizeMB());
                canSave.setRecordCount(dto.getRecordCount());
                ((CanDevice) canContainer.getDevice()).startRealTimeData(channel, canSave);
            }

            @Override
            public void stopRealTimeDataForDbc() {
                ((CanDevice) canContainer.getDevice()).stopRealTimeData(channel);
            }

            ResistanceDevice resistanceDevice;

            @Override
            public boolean registerResistanceDevice() {
                //FIXME:获取连接的设备
                Set<Device> devices = Device.getConnectedDevices("resistanceType");
                if (CollectionUtil.isNotEmpty(devices)) {
                    resistanceDevice = (ResistanceDevice) devices.toArray()[0];
                    return true;
                } else {
                    SwingUtil.showWarningDialog(null, "请先添加电阻板卡设备！");
                    return false;
                }
            }

            @Override
            public void resistanceValue(int resistance) {
                resistanceDevice.sendResistanceValue(resistance);
            }

            @Override
            public void addStartSaveToScript(String filePath, String fileType, String filterType,String filterText){
                Operation operation = Operation.buildOperation(((CanDevice) canContainer.getDevice()));
                operation.getOperationTarget().setChannel(channel);
                operation.setOperationObject(new CanLogRealTimeSaveParameter(filePath, fileType, filterType, filterText, new DbcConfig(dbcPaths)));
                operation.setFriendlyOperationObject("开始实时抓取canLog(DBC)");
                operation.setOperationMethod(DeviceMethods.startCaptureDbcCanLog);
                mainModel.getOperationModel().updateOperation(operation);
            }

            @Override
            public void addStopSaveToScript(){
                Operation operation = Operation.buildOperation(((CanDevice) canContainer.getDevice()));
                operation.getOperationTarget().setChannel(channel);
                operation.setFriendlyOperationObject("结束实时抓取canLog(DBC)");
                operation.setOperationMethod(DeviceMethods.stopCaptureDbcCanLog);
                mainModel.getOperationModel().updateOperation(operation);

            }
        });
        add(canPanel);

        // 启动线程池中的所有核心线程
        ((ThreadPoolExecutor) executorService).prestartAllCoreThreads();
    }


    /**
     * 创建一个指定类型的线程任务
     *
     * @param type 线程类型，用于指定要创建的线程任务类型
     * @return 返回一个可运行的线程任务对象
     */
    private Runnable createTask(String type) {
        switch (type) {
            case SINGLE_MSG:  // 单个消息处理任务
                // 持续从消息队列中获取SSE消息并进行处理
                return this::processSingleMessage;
            case LIST_MSG:  // 消息列表处理任务
                // 持续从消息列表队列中获取SSE消息列表并 批量进行处理
                return this::processBatchMessages;
            case SINGLE_SSE_READ:  // NICAN SSE读取任务
                // SSE流读取消息并放入单消息队列
                return this::readQueueSingleSSEMessage;
            case LIST_SSE_READ:  // TSCAN SSE读取任务
                // 从SSE流读取消息并放入批量消息队列
                return this::readQueueBatchSSEMessage;
        }
        return null;
    }

    /**
     * 从SSE流读取消息并放入批量消息队列
     */
    private void readQueueBatchSSEMessage() {
        try {
            sseSession.readStream(UrlConstants.getSseUrl("canDbcReceiverList" + receiverChannel), msg -> {
                try {
                    messageListQueue.put(msg);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        } catch (Exception e) {
            log.error("TSCAN读取失败", e);
        }
    }

    /**
     * 从SSE流读取消息并放入批量消息队列
     */
    private void readQueueSingleSSEMessage() {
        try {
            sseSession.readStream(UrlConstants.getSseUrl("canDbcReceiver" + receiverChannel), msg -> {
                try {
                    messageQueue.put(msg);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        } catch (Exception e) {
            log.error("NICAN读取失败", e);
        }
    }

    /**
     * 持续从消息队列中获取SSE消息并批量进行处理
     */
    private void processBatchMessages() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                String originalString = messageListQueue.take();
                handleSSEMessageList(originalString);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 持续从消息队列中获取SSE消息并进行处理
     */
    private void processSingleMessage() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                String originalString = messageQueue.take();
                handleSSEMessage(originalString);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    private void handleSSEMessage(String originalString) {
        try {
            String json = originalString.substring(originalString.indexOf("data:") + 5).trim();
            CANMessage messages = gson.fromJson(json, CANMessage.class);
            if (messages.getChn() != null && Integer.parseInt(messages.getChn()) == channel) {
                canPanel.updateData(messages);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void handleSSEMessageList(String originalString) {
        try {
            String json = originalString.substring(originalString.indexOf("data:") + 5).trim();
            json = json.substring(1, json.length() - 1);
            json = json.replace("\\\"", "\""); // 将转义的双引号转换回普通双引号
            List<CANMessage> messages = gson.fromJson(json, new TypeToken<List<CANMessage>>() {
            }.getType());
            messages.removeIf(msg -> Integer.parseInt(msg.getChn()) != channel); // 过滤掉不属于当前channel的消息
            if (!messages.isEmpty()) {
                canPanel.updateData(messages);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private CanMessage toMessage(CANMessageR msg) {
        CanMessage canMessage = new CanMessage();
        canMessage.setDlc(msg.getDlc());
        canMessage.setCanFd("CANFD".equals(msg.getEventType()) || "CANFD加速".equals(msg.getEventType()));
        canMessage.setData(msg.getData());
        canMessage.setChannel(Integer.valueOf(msg.getChn()));
        canMessage.setArbitrationId(Integer.decode(msg.getIdHex()));
        canMessage.setSendMethod("正常发送");
        canMessage.setSendTimes(msg.getSendCount() == 0 ? -1 : msg.getSendCount());
        canMessage.setPeriod(msg.getInterval() / 1000.0f);
        return canMessage;
    }

    /**
     * 释放资源并清理所有相关组件
     */
    public void dispose() {
        // 关闭线程池
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdownNow(); // 立即中断所有线程
        }
        // 清空消息队列
        messageQueue.clear();
        messageListQueue.clear();
        // 关闭SSE会话
        sseSession.closeAll();
        // 移除DBC文件观察者
        DbcFileManager.getInstance().removeObserver(channel, canContainer.getDevice().getDeviceName(), this);
        // 移除UI监听器
        model.removeListener(this::updateUIComponents);
    }

    @Override
    protected void onLoadDbcClicked(int index, String path) {
        SwingUtilities.invokeLater(() -> {
            canPanel.loadDBC(index, path);
        });
        //需要通知另一个面板添加DBC路径
        skipControllerUpdate = true;
        DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), getDbcPaths());
    }

    @Override
    protected void removeLastDbc(int index) {
        SwingUtilities.invokeLater(() -> {
            canPanel.removeDBC(index);
        });
        skipControllerUpdate = true;
        DbcFileManager.getInstance().notifyObservers(channel, canContainer.getDevice().getDeviceName(), getDbcPaths());
    }

    @Override
    public void onDbcFileChanged(List<String> dbcFilePaths) {
        try {
            dbcPaths = dbcFilePaths;
            if (!skipControllerUpdate && canPanel != null) {
                SwingUtilities.invokeLater(() -> canPanel.changeDBCPath(dbcFilePaths));
            }
        } finally {
            skipControllerUpdate = false;
        }
    }
}
