package ui.layout.left.display.components.treemenu.templatetree;

import ui.entry.ClientView;
import ui.layout.left.display.components.container.picture.PictureViewContainer;
import ui.model.MainModel;

import javax.swing.*;

public class TemplateListMgmtPane extends JTabbedPane {
    private final MainModel mainModel;
    private final ClientView clientView;

    public TemplateListMgmtPane(ClientView clientView, MainModel mainModel) {
        super();
        this.clientView = clientView;
        this.mainModel = mainModel;
        PictureViewContainer pictureViewContainer = new PictureViewContainer(clientView, mainModel);
        addTab("查看图像模板", pictureViewContainer);
    }
}
