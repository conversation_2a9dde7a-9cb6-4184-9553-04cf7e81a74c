package ui.layout.left.display.components.container.power.kikusui.entity;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;
import sdk.domain.CheckPointInfo;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: TODO
 * @date: 2024/7/2 20:05
 */

@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class VoltageTrigger extends OperationObject {
    private int symbol;
    private Double voltage;
    private int currentSymbol;
    private float current;
    private int actionOperator;
    private String coordinateName;
    private String portName;
    private int attempts;
    private int interval;
    private CheckPointInfo checkPointInfo;
    private List<String> coordinateNameList;

    public VoltageTrigger withCheckPointInfo(CheckPointInfo checkPointInfo) {
        this.checkPointInfo = checkPointInfo;
        return this;
    }


    @Override
    public String getFriendlyString() {
        String actionFormat;
        if (actionOperator == 0) {
            actionFormat = String.format("电流检测 %sA", current);
        } else if (actionOperator == 1) {
            actionFormat = String.format("报点检测 %s %s", coordinateName, portName);
        } else {
            actionFormat = String.format("滑动{%s}至{%s}", coordinateNameList.get(0), coordinateNameList.get(1));
        }
        return String.format("电压触发: %s %sV", symbol == 0 ? "大于等于" : "小于等于", voltage) + actionFormat;
    }

}

