package ui.layout.left.display.components.container.power.kikusui;

import sdk.domain.Device;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.power.kikusui.pulse_edit.PulseEditingPanel;
import ui.layout.left.display.components.container.power.kikusui.pulseSequence.PulseFunctionPanel;
import ui.layout.left.display.components.container.power.kikusui.pulseSequence.PulseSequencePanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * 菊水电源控制界面
 */
public class KikusuiContainer extends DeviceContainer {
    private final JTabbedPane tabbedPane;
    private static final String PULSE_SEQUENCE = "脉冲序列";
    private static final String PULSE_EDITING = "脉冲编辑";
    private final PulseSequencePanel pulseSequencePanel; //脉冲序列面板
    private final PulseEditingPanel pulseEditingPanel; //脉冲编辑面板

    private final PulseFunctionPanel pulseFunctionPanel;//脉冲功能面板

    public KikusuiContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        pulseSequencePanel = new PulseSequencePanel(mainModel, this);
        pulseEditingPanel = new PulseEditingPanel(mainModel, this);
        pulseFunctionPanel = new PulseFunctionPanel(mainModel, this);
        tabbedPane = new JTabbedPane();
        createView();
        createActions();
    }

    @Override
    public void createView() {
        super.createView();
        JPanel panel = new JPanel(new BorderLayout());
        tabbedPane.add(PULSE_SEQUENCE, pulseSequencePanel);
        tabbedPane.add(PULSE_EDITING, pulseEditingPanel);
        panel.add(tabbedPane, BorderLayout.CENTER);
        panel.add(pulseFunctionPanel, BorderLayout.SOUTH);
        add(panel, BorderLayout.CENTER);
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
        pulseSequencePanel.controlDisplay(isDeviceConnected);
        pulseEditingPanel.controlDisplay(isDeviceConnected);
    }

}
