package ui.layout.right.components.operate.header;

import common.constant.AppConstants;
import lombok.Getter;
import ui.base.BaseView;

import javax.swing.*;
import java.awt.*;

/**
 * 采样率连接面板组件
 */
@Getter
public class SampleRateHeaderHookComponent extends HeaderHookComponent implements BaseView {
    private final JComboBox<Integer> sampleRateComboBox = new JComboBox<>();

    public SampleRateHeaderHookComponent() {
        this(false);
    }

    public SampleRateHeaderHookComponent(boolean delayLoad) {
        setLayout(new FlowLayout(FlowLayout.LEFT));
        if (!delayLoad) {
            createView();
            createActions();
        }
    }

    @Override
    public void createView() {
        for (Integer baudRate : AppConstants.sampleRates) {
            sampleRateComboBox.addItem(baudRate);
        }
        sampleRateComboBox.setSelectedItem(AppConstants.defaultSampleRate);
        add(new JLabel("采样率:"));
        add(sampleRateComboBox);
    }

    public void setSampleRate(int sampleRate) {
        sampleRateComboBox.setSelectedItem(sampleRate);
    }

    public Integer getSampleRate() {
        return (Integer) sampleRateComboBox.getSelectedItem();
    }


}
