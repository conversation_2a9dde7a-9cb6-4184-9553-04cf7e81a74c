package ui.base.cosntants;

import lombok.Data;

@Data
public class ColumnNameConstants {
    public String no;
    public String testCaseID;
    public String testKey;
    public String initialCondition;
    public String action;
    public String expectedResult;
    public String preconditionSequences;
    public String operationStepSequences;
    public String expectedResultSequences;
    public String choose;
    public String testResult;
    public String actualResult;
    public String tester;
    public String testTime;
    public String remark;
    public String targetTestTimes;
    public String testedTimes;
    public String testedPassTime;
    public String testScenario;
    public String testLog;


    private volatile static ColumnNameConstants instance;

    public static ColumnNameConstants getInstance() {
        if (instance == null) {
            synchronized (ColumnNameConstants.class) {
                if (instance == null) {
                    instance = new ColumnNameConstants();
                }
            }
        }
        return instance;
    }

}
