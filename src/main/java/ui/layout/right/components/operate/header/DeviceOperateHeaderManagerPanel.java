package ui.layout.right.components.operate.header;

import common.constant.ResourceConstant;
import common.constant.UiConstants;
import common.exceptions.OperationException;
import common.utils.CopyUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import sdk.base.JsonResponse;
import sdk.domain.Device;
import ui.base.BaseView;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.event.PopupMenuEvent;
import javax.swing.event.PopupMenuListener;
import java.awt.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * 设备端口管理面板
 */
@Slf4j
public class DeviceOperateHeaderManagerPanel extends JPanel implements BaseView {

    private SwingWorker<List<Device>, String> worker;
    //    private boolean portStatusUpdating = false;
    @Getter
    private final List<Device> deviceList;
    @Getter
    private final Device deviceClient;

    @Setter
    private Integer deviceChannel;

//    @Getter
//    private Device device;

    private final Map<String, Object> deviceOperationParameters;

    @Getter
    private final JComboBox<String> portComboBox;
    private final HeaderHookComponent hookComponent;

    //    private final boolean laterOpen;
    @Getter
    private final JLabel connectButton;
    @Getter
    private final JLabel disconnectButton;
    @Getter
    private final JLabel addDeviceButton;
    @Getter
    private final JLabel configDeviceButton;

    @Getter
    private final JLabel label;
//    private boolean connected = false;

    private DeviceConfigDialog deviceConfigDialog;

    private final TextSettings textSettings;

    private final MainModel mainModel;

//    @Setter
//    @Getter
//    private DeviceCallback deviceCallback;

//    @Setter
//    private LoadingDeviceCallback loadingDeviceCallback;

//    @Setter
//    private DeviceOperateHeader deviceOperateHeader;

    public static DeviceOperateHeaderManagerPanel build(MainModel mainModel,
                                                        String labelText,
                                                        Device deviceClient,
                                                        Map<String, Object> deviceOperationParameters,
                                                        HeaderHookComponent hookComponent,
                                                        boolean laterOpen) {
        TextSettings textSettings = new TextSettings();
        textSettings.setLabelText(labelText);
        textSettings.setAcquiringText("正在获取中...");
        String deviceTypeName = deviceClient.getDeviceTypeName();
        textSettings.setAcquiringToOperateWaringText(String.format("正在获取%s列表中，请稍后操作!", deviceTypeName));
        textSettings.setEmptyItemNameWaringText(String.format("%s名称为空!", deviceTypeName));
        textSettings.setDisconnectDeviceOkText(String.format("%s断开成功!", deviceTypeName));
        textSettings.setNotConnectedWarningText(String.format("%s还未连接!", deviceTypeName));
        return new DeviceOperateHeaderManagerPanel(mainModel, textSettings, deviceClient, deviceOperationParameters, hookComponent, laterOpen);
    }

    public static DeviceOperateHeaderManagerPanel build(MainModel mainModel,
                                                        TextSettings textSettings,
                                                        Device deviceClient,
                                                        Map<String, Object> deviceOperationParameters,
                                                        HeaderHookComponent hookComponent,
                                                        boolean laterOpen) {
        return new DeviceOperateHeaderManagerPanel(mainModel, textSettings, deviceClient, deviceOperationParameters, hookComponent, laterOpen);
    }


    protected DeviceOperateHeaderManagerPanel(MainModel mainModel,
                                              TextSettings textSettings,
                                              Device deviceClient,
                                              Map<String, Object> deviceOperationParameters,
                                              HeaderHookComponent hookComponent,
                                              boolean laterOpen) {
        this.deviceClient = deviceClient;
        this.deviceList = new ArrayList<>();
        this.textSettings = textSettings;
        this.mainModel = mainModel;
        this.deviceOperationParameters = deviceOperationParameters;
        this.hookComponent = hookComponent;
//        this.laterOpen = laterOpen;

        label = new JLabel(textSettings.getLabelText());
        portComboBox = new JComboBox<>();
//        deviceIndexSpinner = new JSpinner(new SpinnerNumberModel(1, 1, Integer.MAX_VALUE, 1));
        SwingUtil.setPreferredWidth(portComboBox, UiConstants.MULTI_COMBO_BOX_WIDTH);
        float ratio = 2.0f;
//        deviceConfigButton = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.configDeviceIconPath, ratio + 0.5f);
        connectButton = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.connectDeviceIconPath, ratio);
        disconnectButton = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.disconnectDeviceIconPath, ratio);
        addDeviceButton = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.addDeviceIconPath, ratio);
        configDeviceButton = SwingUtil.makeImageLabel(ResourceConstant.RightLayout.configDeviceIconPath, ratio + 0.5f);
        //test
        disconnectButton.setEnabled(false);
        configDeviceButton.setEnabled(false);
        addDeviceButton.setEnabled(true);  //modify by lhy 2023/04/17,false--->true
        createView();
        createActions();
    }


    public void setDeviceConfigDialog(DeviceConfigDialog deviceConfigDialog) {
        this.deviceConfigDialog = deviceConfigDialog;
        configDeviceButton.setEnabled(true);
    }

    @Override
    public void createView() {
        setLayout(new GridBagLayout());
        int gridx = 0;
        int gridy = 0;
        int labelWeight = 5;
        int hookWeight = hookComponent != null ? 3 : 0;
        int buttonWeight = 5;
        int comboBoxWeight = 100 - (labelWeight + hookWeight + buttonWeight * 3);
//        int indexSpinnerWeight = 1;
        add(label, SwingUtil.getGridBagConstraints(gridx, gridy, labelWeight));
        add(portComboBox, SwingUtil.getGridBagConstraints(++gridx, gridy, comboBoxWeight));
//        add(new JLabel("设备索引:"), SwingUtil.getGridBagConstraints(++gridx, gridy, labelWeight));
//        add(deviceIndexSpinner, SwingUtil.getGridBagConstraints(++gridx, gridy, indexSpinnerWeight));
        if (hookComponent != null) {
            add(hookComponent, SwingUtil.getGridBagConstraints(++gridx, gridy, hookWeight));
        }
        add(connectButton, SwingUtil.getGridBagConstraints(++gridx, gridy, buttonWeight));
        add(disconnectButton, SwingUtil.getGridBagConstraints(++gridx, gridy, buttonWeight));
        add(addDeviceButton, SwingUtil.getGridBagConstraints(++gridx, gridy, buttonWeight));
        add(configDeviceButton, SwingUtil.getGridBagConstraints(++gridx, gridy, buttonWeight));
    }


    @Override
    public void createActions() {
        portComboBox.addPopupMenuListener(new PopupMenuListener() {
            @Override
            public void popupMenuWillBecomeVisible(PopupMenuEvent e) {
                updateComboBox();
            }

            @Override
            public void popupMenuWillBecomeInvisible(PopupMenuEvent e) {
            }

            @Override
            public void popupMenuCanceled(PopupMenuEvent e) {

            }
        });

//        connectButton.addMouseListener(new MouseAdapter() {
//            @Override
//            public void mouseClicked(MouseEvent e) {
//                if (!connectButton.isEnabled() || connected) {
//                    //已连接，该功能不生效
//                    return;
//                }
//                if (portStatusUpdating) {
//                    //端口状态更新中，该功能不生效
//                    SwingUtil.showWarningDialog(connectButton.getParent(), textSettings.getAcquiringToOperateWaringText());
//                    return;
//                }
//                String currentPortName = (String) portComboBox.getSelectedItem();
//                int currentPortNameIndex = portComboBox.getSelectedIndex();
//                if (StringUtils.isEmpty(currentPortName)) {
//                    //端口为空，该功能不生效
//                    SwingUtil.showWarningDialog(connectButton.getParent(), textSettings.getEmptyItemNameWaringText());
//                } else {
//                    Device targetDevice = deviceList.get(currentPortNameIndex);
//                    assert targetDevice != null;
//                    configTargetDeviceParameters(targetDevice);
//                    //配置设备参数
//                    targetDevice.setDeviceOperationParameter(deviceOperationParameters);
////                    targetDevice.setDeviceIndex((Integer) deviceIndexSpinner.getValue());
//                    deviceOperationParameters.put("project", mainModel.getAppInfo().getProject());
//                    SwingUtil.invokeLater(() -> {
//                        connectButton.setEnabled(false);
//                        connectButton.repaint();
//                    });
//                    new SwingWorker<JsonResponse<Device>, Void>() {
//                        @Override
//                        protected JsonResponse<Device> doInBackground() {
//                            JsonResponse<Device> response;
//                            if (laterOpen) {
//                                response = deviceClient.registerDevice(targetDevice);
//                            } else {
//                                response = deviceClient.registerAndOpenDevice(targetDevice);
//                            }
//                            return response;
//                        }
//
//                        @Override
//                        protected void done() {
//                            JsonResponse<Device> response;
//                            try {
//                                response = get();
//                                if (response.isOk()) {
//                                    if (device == null) {
//                                        device = response.getData();
//                                    } else {
//                                        //TODO：copyProperties为浅复制，后续考虑深复制
//                                        BeanUtil.copyProperties(response.getData(), device);
//                                    }
//                                    Device.addConnectedDevice(device);
//                                    connectDevice();
//                                } else {
//                                    SwingUtil.showWarningDialog(connectButton.getParent(), response.getMessage(), "提醒", "设备连接异常, 原因:");
//                                    connectButton.setEnabled(true);
//                                    connectButton.repaint();
//                                }
//                            } catch (InterruptedException | ExecutionException e) {
//                                log.error(e.getMessage(), e);
//                            }
//                        }
//                    }.execute();
//
//                }
//            }
//        });
//
//        disconnectButton.addMouseListener(new MouseAdapter() {
//            @Override
//            public void mouseClicked(MouseEvent e) {
//                if (!disconnectButton.isEnabled()) {
//                    return;
//                }
//                if (connected) {
//                    disconnectDevice();
//                } else {
//                    SwingUtil.showWarningDialog(connectButton.getParent(), textSettings.getNotConnectedWarningText());
//                }
//            }
//        });

//        configDeviceButton.addMouseListener(new MouseAdapter() {
//            @Override
//            public void mouseClicked(MouseEvent e) {
//                if (deviceConfigDialog != null) {
//                    deviceConfigDialog.setVisible(true);
//                    if (deviceConfigDialog.isConfirmed()) {
//                        deviceOperationParameters.putAll(deviceConfigDialog.getDeviceOperationParameters());
//                    }
//                }
//            }
//        });
//
//        addDeviceButton.addMouseListener(new MouseAdapter() {
//            @Override
//            public void mouseClicked(MouseEvent e) {
//                if (loadingDeviceCallback != null) {
//                    loadingDeviceCallback.addDeviceManagerPanel(true);
//                }
//            }
//        });
    }

    private void configTargetDeviceParameters(Device targetDevice) {
        if (hookComponent != null) {
            //钩子组件不为空
            if (hookComponent instanceof CommProtocolBaudRateHeaderHookComponent) {
                targetDevice.setBaudRate(((CommProtocolBaudRateHeaderHookComponent) hookComponent).getBaudRete());
                deviceOperationParameters.put("protocol", ((CommProtocolBaudRateHeaderHookComponent) hookComponent).getCommProtocol());
            } else if (hookComponent instanceof AddressBaudRateHeaderHookComponent) {
                targetDevice.setBaudRate(((AddressBaudRateHeaderHookComponent) hookComponent).getBaudRete());
                deviceOperationParameters.put("address", ((AddressBaudRateHeaderHookComponent) hookComponent).getAddress());
            } else if (hookComponent instanceof ParityBaudRateHeaderHookComponent) {
                targetDevice.setBaudRate(((ParityBaudRateHeaderHookComponent) hookComponent).getBaudRete());
                deviceOperationParameters.put("parity", ((ParityBaudRateHeaderHookComponent) hookComponent).getParityIndex());
            } else if (hookComponent instanceof BaudRateHeaderHookComponent) {
                targetDevice.setBaudRate(((BaudRateHeaderHookComponent) hookComponent).getBaudRete());
            } else if (hookComponent instanceof AddressPortHeaderHookComponent) {
                deviceOperationParameters.put("ipPort", ((AddressPortHeaderHookComponent) hookComponent).getIpPort());
            } else if (hookComponent instanceof AdbCommandHeaderHookComponent) {
                deviceOperationParameters.put("commands", ((AdbCommandHeaderHookComponent) hookComponent).getCommands());
            } else if (hookComponent instanceof SampleRateHeaderHookComponent) {
                targetDevice.setSampleRate(((SampleRateHeaderHookComponent) hookComponent).getSampleRate());
            }
        }
    }

    public Device getConfigDevice() {
        String currentItemName = (String) portComboBox.getSelectedItem();
        int currentIndex = portComboBox.getSelectedIndex();
        if (StringUtils.isEmpty(currentItemName)) {
            SwingUtil.showWarningDialog(this, textSettings.getEmptyItemNameWaringText());
        } else {
            Device targetDevice = deviceList.get(currentIndex);
            assert targetDevice != null;
            configTargetDeviceParameters(targetDevice);
            targetDevice.setDeviceOperationParameter(deviceOperationParameters);
            targetDevice.setDeviceTypeName(deviceClient.getDeviceTypeName());
            targetDevice.setDeviceType(deviceClient.getDeviceType());
            targetDevice.setDeviceModel(deviceClient.getDeviceModel());
            return targetDevice;
        }
        return null;
    }

    public void configDeviceOperationParameters() {
        deviceOperationParameters.putAll(deviceConfigDialog.getDeviceOperationParameters());
    }

//    private void controlButtons(boolean isEnabled) {
//        portComboBox.setEnabled(!isEnabled);
//        connectButton.setEnabled(!isEnabled);
//        configDeviceButton.setEnabled(!isEnabled);
//        disconnectButton.setEnabled(isEnabled);
//    }

//    //FIXME：迁移到设备管理器
//    private void onConnectDeviceSuccess() {
//        device.setConnected(true);
//        // 记录设备-端口连接历史
//        addDeviceConnectedHistory(device);
//
//        //TODO:去除deviceCallback
//        if (deviceCallback != null) {
//            deviceCallback.deviceConnected(device, false);
//        }
//        connected = true;
//        controlButtons(true);
//        mainModel.getDeviceManageModel().deviceConnected(device);
//        DeviceJsonManager.getDeviceJsonManager()
//                .addDeviceConfig(
//                        device.getDeviceType(),
//                        device.getDeviceModel(),
//                        0, device)
//                .save();
//    }

//    private void connectDevice() {
//        //FIXME：每次连接都会生成device新实例，尝试单例模式
//        if (device != null) {
//            log.info("连接设备:{}", device);
//            onConnectDeviceSuccess();
//        }
//    }
//
//
//    private void disconnectDevice() {
//        if (device != null) {
//            JsonResponse<String> response = device.unregisterDevice();
//            if (response.isOk()) {
//                log.info("注销设备:{}", device);
//                device.setConnected(false);
//                if (deviceCallback != null) {
//                    deviceCallback.deviceDisconnected(device);
//                }
//                connected = false;
//                controlButtons(false);
//                mainModel.getDeviceManageModel().deviceDisconnected(device);
//            } else {
//                SwingUtil.showWarningDialog(connectButton.getParent(), response.getMessage());
//            }
//
//        }
//    }


    @SuppressWarnings("unchecked")
    public void updateComboBox() {
        if (worker == null || worker.isDone()) {
            worker = new SwingWorker<List<Device>, String>() {
                @Override
                protected List<Device> doInBackground() throws OperationException {
//                    portStatusUpdating = true;
                    publish(textSettings.getAcquiringText());
                    deviceClient.setChannel(deviceChannel);
                    if (hookComponent instanceof CommProtocolBaudRateHeaderHookComponent) {
                        deviceClient.setCommProtocol(((CommProtocolBaudRateHeaderHookComponent) hookComponent).getCommProtocol());
                    }
                    if (deviceClient.onlySearchPortNames()) {
                        List<String> deviceNames = deviceClient.queryPortNames();
                        //拷贝deviceClient
                        int i = 1;
                        List<Device> devices = new ArrayList<>();
                        for (String deviceName : deviceNames) {
                            try {
                                Device device = CopyUtils.deepCopy(deviceClient);
                                device.setAliasName(device.getDeviceModel() + "#" + i++);
                                device.setDeviceName(deviceName);
                                device.setDeviceUniqueCode(deviceName);
                                devices.add(device);
                            } catch (IOException | ClassNotFoundException e) {
                                throw new OperationException(e.getMessage());
                            }
                        }
                        return devices;
                    } else {
                        JsonResponse<List<? extends Device>> response = deviceClient.queryDevices();
                        if (response.isOk()) {
                            return (List<Device>) response.getData();
                        } else {
                            throw new OperationException(response.getMessage());
                        }
                    }
                }

                @Override
                protected void process(List<String> chunks) {
                    portComboBox.removeAllItems();
                    for (String chunk : chunks) {
                        portComboBox.addItem(chunk);
                    }
                }

                @Override
                protected void done() {
                    try {
                        List<Device> devices = get();
                        if (devices != null) {
                            portComboBox.removeAllItems();
                            deviceList.clear();
                            for (Device device : devices) {
                                deviceList.add(device);
                                //改为设备名称显示到框内
                                portComboBox.addItem(device.getFriendlyName());
                            }
                            if (portComboBox.getItemCount() > 0) {
                                portComboBox.setSelectedIndex(0);
                            } else {
                                SwingUtil.showWarningDialog(getParent(), deviceClient.getDeviceModel() + "未找到设备端口");
                            }
                        }
                    } catch (InterruptedException ex) {
                        log.error(ex.getMessage(), ex);
                    } catch (ExecutionException ex) {
                        SwingUtil.showWarningDialog(getParent(), ex.getMessage());
                    }

                }
            };
            worker.execute();
        }
    }


    public void setButtonVisible(boolean visible) {
        connectButton.setVisible(visible);
        disconnectButton.setVisible(visible);
        addDeviceButton.setVisible(visible);
    }

    public void setPortComboBox(Object object) {
        portComboBox.setSelectedItem(object);
        repaint();
    }

}