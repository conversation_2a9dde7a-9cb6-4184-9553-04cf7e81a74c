package sdk.domain.bus;

import lombok.Data;

@Data
public class CanConfigParameter extends BusConfigParameter{

    private String protocol;//协议
    private boolean canFdStandard;//CANFD标准
    private boolean accelerate;//CANFD加速 "是"/"否"
    private String arbitrationBps;//仲裁域波特率
    private String dataBps;//数据域波特率
    private String userDefinedBaudrate;//自定义波特率
    private boolean normalWorkMode;//工作模式 "只听模式"/"正常模式"
    private boolean terminalResistanceEnabled;//终端电阻 "使能"/"禁能"
    private boolean canBusUtilizationRateEnabled;//上报总线利用率 "使能"/"禁能"
    private int canBusUtilizationRateCycle;//总线利用率周期(ms)
    private String sendRetry;//发送重试
    private boolean canFd;

}
