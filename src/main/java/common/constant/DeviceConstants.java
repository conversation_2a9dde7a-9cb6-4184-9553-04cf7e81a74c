package common.constant;

import java.awt.*;

public class DeviceConstants {
    public static final String PARITY = "parity";

    // 线程类型 用于SerialContainer
    public static final String SERIAL_WEBSOCKET_MONITOR = "serial_websocket_monitor"; // 串口WebSocket数据监控线程
    public static final String SERIAL_MESSAGE_QUEUE = "serial_message_queue"; // 串口消息队列处理线程
    public static final String[] SERIAL_THREAD_TYPES = {SERIAL_WEBSOCKET_MONITOR, SERIAL_MESSAGE_QUEUE};

    // 文件路径
    public final static String PROJECT_PATH = "D:\\FlyTest\\data\\server\\projects";

    // 线程类型 用于canDbcReceiveSettingView
    public static final String SINGLE_MSG = "single_msg"; // 单条消息处理
    public static final String LIST_MSG = "list_msg"; // 批量消息处理
    public static final String SINGLE_SSE_READ = "single_sse_read"; // SSE单次读取 NICAN
    public static final String LIST_SSE_READ = "list_sse_read"; // SSE批量读取 TSCAN
    public static final String[] THREAD_TYPES = {SINGLE_MSG, LIST_MSG, SINGLE_SSE_READ, LIST_SSE_READ};

    // 继电器开关状态
    public static final String SWITCH_OFF = "OFF";
    public static final String SWITCH_ON = "ON";

    // 继电器连接状态 1连接 0断开
    public static final String CONNECTED_STATUS_1 = "1";
    public static final String CONNECTED_STATUS_0 = "0";

    // 继电器通道名称常量定义
    public static final String RELAY_CHANNEL_NAME_1 = "点火KL15";
    public static final String RELAY_CHANNEL_NAME_2 = "蓄电池KL30";
    public static final String RELAY_CHANNEL_NAME_3 = "ACC";

    // 电源 开关状态
    public static final String POWER_ON = "电源开启";
    public static final String POWER_OFF = "电源关闭";

    // 电源设置电阻的键
    public static final String POWER_VALUE = "powerValue";

    // 设备状态颜色常量定义
    public static final Color ON_COLOR = Color.GREEN; // 绿色
    public static final Color OFF_COLOR = Color.lightGray; // 灰色

    public static final String RELAY_CHANNEL = "通道"; // 通道
}
