package ui.layout.left.display.components.container.base;

import lombok.Getter;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-28 18:13
 * @description :
 * @modified By :
 * @since : 2022-6-28
 */
@Getter
public class DeviceControlPanel extends JPanel {
    private final DeviceContainer deviceContainer;
    private final MainModel  mainModel;

    public DeviceControlPanel(DeviceContainer deviceContainer, MainModel mainModel) {
        this.deviceContainer = deviceContainer;
        this.mainModel = mainModel;
        setLayout(new BorderLayout());
    }

    public JButton addNewScriptButton() {
        return SwingUtil.getAddToScriptButton();
    }


    public Integer getChannel() {
        return null;
    }

}
