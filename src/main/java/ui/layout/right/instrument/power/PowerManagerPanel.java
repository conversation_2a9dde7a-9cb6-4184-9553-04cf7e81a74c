package ui.layout.right.instrument.power;

import common.constant.DeviceConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import ui.base.BaseView;
import ui.service.PanelManager;
import ui.service.PowerControlService;

import javax.swing.*;
import java.awt.*;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.util.Map;

/**
 * 电源管理面板类，用于控制电源相关参数
 * 包含以下组件：
 * - 功率调节滑块(JSlider)
 * - 功率数值输入框(JSpinner)
 * - 电源开关按钮(JToggleButton)
 */
@Slf4j
public class PowerManagerPanel extends JPanel implements BaseView {

    private DoubleJSlider powerSlider;        // 功率调节滑块组件
    private JSpinner powerValueInput;   // 功率数值输入组件
    private JToggleButton powerSwitch; // 电源开关按钮组件
    // 在PowerManagerPanel类中添加
    private volatile boolean isManualInput = false;

    // 用于管理电源控制相关操作
    private final PowerControlService powerControlService = new PowerControlService();
    Map<String, String> powerData = PanelManager.getInstance().getPowerData();
    /**
     * 构造函数
     * 初始化时创建界面组件并设置事件监听
     */
    public PowerManagerPanel() {
        createView();      // 创建界面组件
        createActions();   // 设置事件监听
    }

    /**
     * 创建界面视图
     * 1. 设置面板布局
     * 2. 添加标题面板
     * 3. 添加滑动条控制面板
     */
    @Override
    public void createView() {
        // 设置面板为边界布局
        setLayout(new BorderLayout());

        // 添加顶部标题面板
        add(getTitlePanel(), BorderLayout.NORTH);

        // 添加中央控制面板
        add(getSliderPanel(), BorderLayout.CENTER);
    }

    @Override
    public void createActions() {
        // 当功率滑动条值变化时，调用更新功率值显示的方法
        powerSlider.addChangeListener(e -> updatePowerValue());

        // 当功率输入框值变化时，调用同步滑动条位置的方法
        powerValueInput.addChangeListener(e -> {
            updatePowerSliderFromInput();
        });

        // 确保监听器添加到正确的组件上（输入框的编辑组件）回车执行命名
        JComponent editorComponent = (JComponent) powerValueInput.getEditor().getComponent(0);
        editorComponent.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER && !e.isConsumed()) {
                    e.consume();  // 防止事件冒泡
                    updatePowerSliderWithControl();
                }
            }
        });

        // 电源开关切换事件：根据选择状态更新按钮文本显示
        powerSwitch.addActionListener(e -> {
            boolean isOn = powerSwitch.isSelected();
            log.info("电源开关状态: {}", isOn);
            powerSwitch.setBackground(isOn ? DeviceConstants.ON_COLOR : DeviceConstants.OFF_COLOR);
            powerSwitch.setText(isOn ? DeviceConstants.POWER_OFF : DeviceConstants.POWER_ON);  // 更新按钮文本
            powerControlService.controlPowerOutput(isOn);
        });
    }

    /**
     * 创建并返回标题面板
     * 面板包含"Power Control"标题，使用左对齐布局
     * 标题字体为Arial粗体14号
     */
    private JPanel getTitlePanel() {
        JPanel titlePanel = new JPanel();
        titlePanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        JLabel titleLabel = new JLabel("Power Control");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 14));
        titlePanel.add(titleLabel);
        return titlePanel;
    }

    /**
     * 创建并返回包含所有滑动条组件的面板
     * 通过调用基础面板创建方法和组件添加方法完成
     */
    private JPanel getSliderPanel() {
        JPanel sliderPanel = createBaseSliderPanel();
        addSliderComponents(sliderPanel);
        return sliderPanel;
    }

    /**
     * 创建基础滑动条面板
     * 使用左对齐的流式布局
     */
    private JPanel createBaseSliderPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new FlowLayout(FlowLayout.LEFT));
        return panel;
    }

    /**
     * 向面板添加所有滑动条相关组件
     * @param sliderPanel 目标面板
     */
    private void addSliderComponents(JPanel sliderPanel) {
        // 创建组件
        createPowerSlider();
        createPowerValueInput();
        createPowerSwitch();

        // 添加组件并设置间距
        sliderPanel.add(powerSlider);
        sliderPanel.add(Box.createHorizontalStrut(10));
        sliderPanel.add(powerValueInput);
        sliderPanel.add(Box.createHorizontalStrut(10));
        sliderPanel.add(powerSwitch);
    }

    /**
     * 创建电源滑块组件
     * 设置范围0-36，主刻度间隔5，次刻度间隔1
     * 显示刻度线和标签
     */
    private void createPowerSlider() {
        double scale = 10.0; // 缩放因子
        powerSlider = new DoubleJSlider(0, 36, 0, scale);
        powerSlider.setName("powerControlSlider");

        // 添加边框
        powerSlider.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createTitledBorder("电压调节"),
                BorderFactory.createEmptyBorder(5, 5, 5, 5)
        ));

        // 初始化值
        double initValue = getInitValue();
        powerSlider.setDoubleValue(initValue);
    }

    /**
     * 创建电源数值输入组件
     * 设置尺寸60x40，字体Arial常规14号
     * 数值范围0.0-36.0，步长0.1
     */
    private void createPowerValueInput() {
        powerValueInput = new JSpinner();
        powerValueInput.setPreferredSize(new Dimension(100, 40));
        powerValueInput.setFont(new Font("Arial", Font.PLAIN, 14));
        powerValueInput.setModel(new SpinnerNumberModel(0, 0.0, 36, 0.1));
        powerValueInput.setName("powerValueInput");
        // 初始化值
        double initValue = getInitValue();
        powerValueInput.setValue(initValue);
    }

    /**
     * 获取功率滑块的初始值
     * 1. 从powerData中读取配置的功率值
     * 2. 当配置值为空时返回默认值0
     * 3. 数值转换未处理异常，调用方需确保配置值合法
     */
    private double getInitValue() {
        String powerSliderValue = powerData.get(DeviceConstants.POWER_VALUE);
        return StringUtils.isNotBlank(powerSliderValue) ?
                Double.parseDouble(powerSliderValue) : 0;
    }

    /**
     * 创建电源开关按钮
     * 初始状态为"OFF"
     */
    private void createPowerSwitch() {
        powerSwitch = new JToggleButton(DeviceConstants.POWER_ON);
        powerSwitch.setBackground(DeviceConstants.OFF_COLOR);
        powerSwitch.setName("powerToggleSwitch");

        powerSwitch.setOpaque(true);
        powerSwitch.setBorderPainted(false);
    }


    /**
     * 从功率输入框更新滑动条数值（基础版）
     * 核心功能：
     * 1. 解析输入框数值
     * 2. 校验数值范围(0-36)
     * 3. 更新滑动条位置
     * 4. 保存功率值到配置数据
     *
     * @throws NumberFormatException 当输入内容无法转换为数字时抛出
     */
    private void updatePowerSliderFromInput() {
        try {
            double value = Double.parseDouble(powerValueInput.getValue().toString());
            if (value >= 0 && value <= 36) {
                powerSlider.setDoubleValue(value);
                powerData.put(DeviceConstants.POWER_VALUE, String.valueOf(value));
            } else {
                JOptionPane.showMessageDialog(this,
                        "请输入" + 0 + "到" + 36 + "之间的整数",
                        "输入错误",
                        JOptionPane.WARNING_MESSAGE);
            }
        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(this,
                    "请输入有效数字",
                    "输入错误",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 从功率输入框更新滑动条数值（控制增强版）
     * 与原版updatePowerSliderFromInput的区别：
     * 1. 新增功率控制服务交互（powerControlService.setVoltage）
     * 2. 更明确的业务场景命名（WithControl）
     * 3. 保持相同的数值校验和异常处理机制
     * <p>
     * 适用场景：需要同步控制硬件功率输出的情况
     */
    private void updatePowerSliderWithControl() {
        try {
            double value = Double.parseDouble(powerValueInput.getValue().toString());
            if (value >= 0 && value <= 36) {
                powerSlider.setDoubleValue(value);
                powerControlService.setVoltage(value);
                powerData.put(DeviceConstants.POWER_VALUE, String.valueOf(value));
            } else {
                JOptionPane.showMessageDialog(this,
                        "请输入" + 0 + "到" + 36 + "之间的整数",
                        "输入错误",
                        JOptionPane.WARNING_MESSAGE);
            }
        } catch (NumberFormatException ex) {
            JOptionPane.showMessageDialog(this,
                    "请输入有效数字",
                    "输入错误",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    // 修改updatePowerValue方法保持双向同步
    private void updatePowerValue() {
        double value = powerSlider.getDoubleValue();
        powerValueInput.setValue(value);
    }

    public static void main(String[] args) {
        // 测试代码
        JFrame frame = new JFrame("Power Manager Panel");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.add(new PowerManagerPanel());
        frame.pack();
        frame.setSize(400, 150);
        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
    }

}
