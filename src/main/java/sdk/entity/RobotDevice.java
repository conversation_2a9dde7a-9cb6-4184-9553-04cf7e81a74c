package sdk.entity;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import common.constant.DeviceCategoryConstants;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.constants.UrlConstants;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.domain.complex.CoordinatesRoi;
import sdk.domain.robot.*;
import sdk.domain.screen.events.ArcTouchEvent;
import sdk.domain.screen.events.ClickEvent;
import sdk.domain.screen.events.LineTouchEvent;
import sdk.entity.interfaces.IConfigurable;
import sdk.entity.interfaces.IRobot;
import sdk.entity.interfaces.IRobotCoordinates;
import ui.config.json.devices.robot.RobotConfig;

import java.awt.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-19 15:52
 * @description :
 * @modified By :
 * @since : 2022-7-19
 */
public class RobotDevice extends Device implements IRobot, IRobotCoordinates, IConfigurable<RobotConfig> {

    private static final Map<String, RobotDevice> deviceMap = new HashMap<>();

    public static RobotDevice getDevice(String deviceModel) {
        if (!deviceMap.containsKey(deviceModel)) {
            deviceMap.put(deviceModel, new RobotDevice(deviceModel));
        }
        return deviceMap.get(deviceModel);
    }

    public RobotDevice() {
        super(DeviceModel.Robot.DOBOT_MG400);
    }

    public RobotDevice(String deviceModel) {
        super(deviceModel);
    }

    @Override
    public String getDeviceTypeName() {
        return DeviceCategoryConstants.ROBOT.getDeviceOfficialName();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ROBOT;
    }

    @Override
    public JsonResponse<Device> registerDevice(Device device) {
        return registerDevice(device, new TypeReference<JsonResponse<RobotDevice>>() {
        });
    }

    @Override
    public JsonResponse<Device> registerAndOpenDevice(Device device) {
        return registerAndOpenDevice(device, new TypeReference<JsonResponse<RobotDevice>>() {
        });
    }

    @Override
    public JsonResponse<List<? extends Device>> queryDevices() {
        String url;
        if (getDeviceModel().equals(DeviceModel.Robot.DOBOT_MG400)) {
            url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllIpRobotPorts(getDeviceModel());
        } else {
            url = UrlConstants.DeviceUrls.PhysicalPortUrls.getAllSerialPorts(getDeviceModel());
        }
        JsonResponse<List<RobotDevice>> resp = defaultGetJsonResponse(url, new TypeReference<JsonResponse<List<RobotDevice>>>() {
        });
        return JsonResponse.retrieve(resp);
    }

    @Override
    public OperationResult moveJog(String command) {
        return callOperationMethod(DeviceMethods.moveJog, command);
    }

    @Override
    public OperationResult stopMoveJog() {
        return callOperationMethod(DeviceMethods.stopMoveJog);
    }

    @Override
    public FeedbackData fetchFeedbackData() {
        OperationResult response = callOperationMethod(DeviceMethods.fetchFeedbackData);
        if (response.isOk()) {
            return JSON.parseObject(JSON.toJSONString(response.getData()), FeedbackData.class);
        }
        return new FeedbackData();
    }

    @Override
    public void enablePoseMonitor() {
        callOperationMethod(DeviceMethods.enablePoseMonitor);
    }

    @Override
    public void resumePoseMonitor() {
        callOperationMethod(DeviceMethods.resumePoseMonitor);
    }

    @Override
    public void pausePoseMonitor() {
        callOperationMethod(DeviceMethods.pausePoseMonitor);
    }

    @Override
    public OperationResult moveLine(MoveEntity moveEntity) {
        return callOperationMethod(DeviceMethods.moveLine, moveEntity);
    }

    @Override
    public OperationResult moveJoint(MoveEntity moveEntity) {
        return callOperationMethod(DeviceMethods.moveJoint, moveEntity);
    }

    @Override
    public OperationResult arc(ArcEntity arcEntity) {
        return callOperationMethod(DeviceMethods.arc, arcEntity);
    }

    @Override
    public OperationResult returnHome() {
        return callOperationMethod(DeviceMethods.returnHome);
    }

    @Override
    public OperationResult autoScreenCalibration(String serialAliasName) {
        return callOperationMethod(DeviceMethods.autoScreenCalibration, serialAliasName);
    }

    @Override
    public OperationResult touch(MoveEntity moveEntity) {
        return callOperationMethod(DeviceMethods.touch, moveEntity);
    }

    @Override
    public OperationResult visionTouch(Point pixelPoint) {
        return callOperationMethod(DeviceMethods.visionTouch, pixelPoint);
    }

    @Override
    public OperationResult setVisionGuideZ(double visionGuideZ) {
        return callOperationMethod(DeviceMethods.setVisionGuideZ, visionGuideZ);
    }

    @Override
    public OperationResult setVisionGuideCalibrationData(VisionGuideCalibrationData visionGuideCalibrationData) {
        return callOperationMethod(DeviceMethods.setVisionGuideCalibrationData, visionGuideCalibrationData);
    }

    @Override
    public OperationResult fetchVisionGuideConfig() {
        return callOperationMethod(DeviceMethods.fetchVisionGuideConfig);
    }

//    @Override
//    public OperationResult fetchSpeedFactor() {
//        return callOperationMethod(DeviceMethods.fetchSpeedFactor);
//    }

    public OperationResult setSpeedFactor(int factor) {
        return callOperationMethod(DeviceMethods.setSpeedFactor, factor);
    }

    @Override
    public OperationResult setPayLoad(int weight) {
        return callOperationMethod(DeviceMethods.setPayLoad, weight);
    }

    //重写设置和获取速度和加速度的方法
//    @Override
//    public OperationResult setAccSpeed(int factor) {
//        return callOperationMethod(DeviceMethods.setAccSpeed, factor);
//    }

//    @Override
//    public OperationResult fetchAccelerationSpeedFactor() {
//        return callOperationMethod(DeviceMethods.fetchAccelerationSpeedFactor);
//    }

    @Override
    public OperationResult enableRobot() {
        return callOperationMethod(DeviceMethods.enableRobot);
    }

    @Override
    public OperationResult disableRobot() {
        return callOperationMethod(DeviceMethods.disableRobot);
    }

    @Override
    public OperationResult slide(List<MoveEntity> moveEntityList) {
        return callOperationMethod(DeviceMethods.robotSwipe, moveEntityList);
    }

    @Override
    public OperationResult move(List<MoveEntity> moveEntityList) {
        return callOperationMethod(DeviceMethods.robotMove, moveEntityList);
    }

    @Override
    public OperationResult randomTouch(List<MoveEntity> moveEntityList) {
        return callOperationMethod(DeviceMethods.randomTouchByName, moveEntityList);
    }
//    @Override
//    public OperationResult slideByName(List<String> coordinateNameList) {
//        return callOperationMethod(DeviceMethods.swipeByName, coordinateNameList);
//    }

    @Override
    public OperationResult slideRail(MovJExtEntity movJExtEntity) {
        return callOperationMethod(DeviceMethods.moveJointExt, movJExtEntity);
    }

    @Override
    public OperationResult clearError() {
        return callOperationMethod(DeviceMethods.clearError);
    }

    @Override
    public OperationResult searchError() {
        return callOperationMethod(DeviceMethods.searchError);
    }

    @Override
    public JsonResponse<FunctionalRobotCoordinates> addFunctionalRobotCoordinates(FunctionalRobotCoordinates functionalRobotCoordinates) {
        return defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.ADD_FUNCTIONAL_ROBOT_COORDINATES,
                functionalRobotCoordinates, new TypeReference<JsonResponse<FunctionalRobotCoordinates>>() {
                });
    }

    @Override
    public JsonResponse<FunctionalRobotCoordinates> updateFunctionalRobotCoordinates(FunctionalRobotCoordinates functionalRobotCoordinates) {
        return defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.UPDATE_FUNCTIONAL_ROBOT_COORDINATES,
                functionalRobotCoordinates, new TypeReference<JsonResponse<FunctionalRobotCoordinates>>() {
                });
    }

    @Override
    public JsonResponse<List<FunctionalRobotCoordinates>> queryFunctionalRobotCoordinates(FunctionalRobotCoordinatesQueryVo coordinatesQueryVo) {
        return defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.QUERY_FUNCTIONAL_ROBOT_COORDINATES, coordinatesQueryVo,
                new TypeReference<JsonResponse<List<FunctionalRobotCoordinates>>>() {
                });
    }

    @Override
    public JsonResponse<RobotCoordinates> addCoordinates(RobotCoordinates robotCoordinates) {
        return defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.ADD_ROBOT_COORDINATES,
                robotCoordinates, new TypeReference<JsonResponse<RobotCoordinates>>() {
                });
    }

    @Override
    public JsonResponse<CoordinatesRoi> addRobotRoi(CoordinatesRoi coordinatesRoi) {
        return defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.ADD_ROBOT_ROI,
                coordinatesRoi, new TypeReference<JsonResponse<CoordinatesRoi>>() {
                });
    }

    @Override
    public JsonResponse<String> deleteRobotRoi(String coordinatesUUID) {
        return defaultDeleteJsonResponse(UrlConstants.RobotCoordinatesUrls.DELETE_ROBOT_ROI + "/" + coordinatesUUID,
                new TypeReference<JsonResponse<String>>() {
                });
    }

    @Override
    public JsonResponse<CoordinatesRoi> fetchRobotRoiByCoordinatesUUID(String coordinatesUUID) {
        return defaultGetJsonResponse(UrlConstants.RobotCoordinatesUrls.GET_ROBOT_ROI + "/" + coordinatesUUID,
                new TypeReference<JsonResponse<CoordinatesRoi>>() {
                });
    }

    @Override
    public JsonResponse<RobotCoordinates> deleteCoordinates(RobotCoordinates robotCoordinates) {
        return defaultDeleteJsonResponse(UrlConstants.RobotCoordinatesUrls.DELETE_ROBOT_COORDINATES,
                robotCoordinates, new TypeReference<JsonResponse<RobotCoordinates>>() {
                });
    }

    @Override
    public JsonResponse<String> clearCoordinates(RobotCoordinatesQuery robotCoordinatesQuery) {
        return defaultDeleteJsonResponse(UrlConstants.RobotCoordinatesUrls.CLEAR_ROBOT_COORDINATES,
                robotCoordinatesQuery, new TypeReference<JsonResponse<String>>() {
                });
    }

    @Override
    public JsonResponse<RobotCoordinates> updateCoordinates(RobotCoordinates robotCoordinates) {
        return defaultUpdate(UrlConstants.RobotCoordinatesUrls.UPDATE_ROBOT_COORDINATES,
                robotCoordinates, new TypeReference<JsonResponse<RobotCoordinates>>() {
                });
    }

    @Override
    public JsonResponse<List<RobotCoordinates>> getAllRobotCoordinates(RobotCoordinatesQuery robotCoordinatesQuery) {
        return defaultPostJsonResponse(UrlConstants.RobotCoordinatesUrls.GET_ALL_ROBOT_COORDINATES,
                robotCoordinatesQuery, new TypeReference<JsonResponse<List<RobotCoordinates>>>() {
                });
    }

    @Override
    public RobotConfig loadConfig(String projectName) {
        OperationResult operationResult = callOperationMethod(DeviceMethods.loadConfig, projectName);
        RobotConfig config;
        JSONObject object = ((JSONObject) operationResult.getData());
        if (operationResult.isOk()) {
            if (object == null) {
                config = new RobotConfig();
            } else {
                config = object.to(RobotConfig.class);
            }
        } else {
            config = new RobotConfig();
        }
        config.setProject(projectName);
        return config;
    }

    @Override
    public OperationResult setUserCoordinate(int userIndex) {
        return callOperationMethod(DeviceMethods.setUserCoordinate, userIndex);

    }

    @Override
    public OperationResult touchAndCheckTouchPoints(ClickEvent clickEvent) {
        return callOperationMethod(DeviceMethods.touchAndCheckTouchPoint, clickEvent);
    }

    @Override
    public OperationResult swipeAndCheckTouchPoints(LineTouchEvent lineTouchPoint) {
        return callOperationMethod(DeviceMethods.swipeAndCheckTouchPoint, lineTouchPoint);
    }

    @Override
    public OperationResult arcAndCheckTouchPoints(ArcTouchEvent arcTouchPoint) {
        return callOperationMethod(DeviceMethods.arcAndCheckTouchPoint, arcTouchPoint);
    }

    @Override
    public OperationResult longTouchCoordinate(LongTouchRobotCoordinate longTouchRobotCoordinate) {
        return callOperationMethod(DeviceMethods.longTouch, longTouchRobotCoordinate);
    }

    @Override
    public OperationResult circle(MoveEntity moveEntity) {
        return callOperationMethod(DeviceMethods.circle, moveEntity);
    }

    @Override
    public OperationResult air(boolean pressure) {
        return callOperationMethod(DeviceMethods.air, pressure);
    }


}
