package ui.layout.right.components.operate.header;

import common.constant.AppConstants;
import lombok.Getter;

import javax.swing.*;

/**
 * 通讯协议/波特率组件
 */
@Getter
public class CommProtocolBaudRateHeaderHookComponent extends BaudRateHeaderHookComponent {

    private final JComboBox<String> commProtocolComboBox = new JComboBox<>();

    public CommProtocolBaudRateHeaderHookComponent() {
        super(true);
        createView();
        createActions();
    }

    @Override
    public void createView() {
        super.createView();
        for (String protocol : AppConstants.serialProtocols) {
            commProtocolComboBox.addItem(protocol);
        }
//        commProtocolComboBox.setSelectedItem(AppConstants.rs232Protocol);
        add(new JLabel("通讯线:"));
        add(commProtocolComboBox);
    }

    /**
     * 设置通讯协议
     *
     * @param protocol 通讯协议
     */
    public void setCommProtocol(String protocol) {
        commProtocolComboBox.setSelectedItem(protocol);
    }

    /**
     * 获取通讯协议
     *
     * @return 通讯协议
     */
    public String getCommProtocol() {
        return (String) commProtocolComboBox.getSelectedItem();
    }

}
