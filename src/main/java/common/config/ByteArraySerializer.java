package common.config;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;

import java.lang.reflect.Type;
import java.util.Base64;

public class ByteArraySerializer implements ObjectWriter<byte[]> {

    @Override
    public void write(JSONWriter jsonWriter, Object value, Object fieldName, Type fieldType, long features) {
        if (value != null) {
            String base64String = Base64.getEncoder().encodeToString((byte[]) value);
            jsonWriter.writeString(base64String);
        } else {
            jsonWriter.writeNull();
        }
    }
}
