package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import excelcase.config.json.CaseConfigJson;
import lombok.Getter;
import ui.config.xml.app.AppConfiguration;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.File;

public class SingleTestSettingDialog extends JDialog {
    private JCheckBox cancelCheckAllCheckBox;
    private JCheckBox cancelAnyCheckCheckBox;
    private JButton okButton;
    private static SingleTestSettingDialog instance;
    private MainModel mainModel;

    public static SingleTestSettingDialog getInstance(MainModel mainModel) {
        if (instance == null) {
            instance = new SingleTestSettingDialog(mainModel);
        }
        return instance;
    }

    public SingleTestSettingDialog(MainModel mainModel) {
        this.mainModel = mainModel;
        initializeUI();
    }

    private void initializeUI() {
        setTitle("单步调试设置");
        setLayout(new BorderLayout());
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        cancelCheckAllCheckBox = new JCheckBox("取消单步调试checkAll动作");
        cancelAnyCheckCheckBox = new JCheckBox("取消单步调试所有check动作");
        okButton = new JButton("确定");
        okButton.addActionListener(this::confirmAndClose);
        panel.add(cancelCheckAllCheckBox);
        panel.add(cancelAnyCheckCheckBox);
        add(panel, BorderLayout.CENTER);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.add(okButton);
        add(buttonPanel, BorderLayout.SOUTH);

        setSize(500, 200);
        setLocationRelativeTo(null); // 居中显示
        setDefaultCloseOperation(DISPOSE_ON_CLOSE);

        cancelCheckAllCheckBox.setSelected(CaseConfigJson.getInstance().isSelectedCancelCheckAll());
        cancelAnyCheckCheckBox.setSelected(CaseConfigJson.getInstance().isSelectedCancelAnyCheck());
    }


    private void confirmAndClose(ActionEvent e) {
        mainModel.getTestCaseTableModel().saveSingleTestSetting(cancelCheckAllCheckBox.isSelected(), cancelAnyCheckCheckBox.isSelected());
        setVisible(false);
    }


}
