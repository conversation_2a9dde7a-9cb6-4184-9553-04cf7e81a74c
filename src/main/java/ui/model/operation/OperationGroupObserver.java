package ui.model.operation;

import sdk.base.operation.OperationGroup;
import ui.model.ModelObserver;

public interface OperationGroupObserver extends ModelObserver {
    default boolean addOperationGroup(OperationGroup operationGroup) {
        return true;
    }

    default boolean deleteOperationGroup(String groupName) {
        return true;
    }
    default void loadOperationGroup(String operationGroupName) {
        loadOperationGroup(operationGroupName, false);
    }

    void loadOperationGroup(String operationGroupName, boolean autoSwitchEditorTab);

}
