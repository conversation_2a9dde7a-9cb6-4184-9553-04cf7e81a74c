package ui.layout.left.display.components.container.can;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;
import ui.layout.left.display.components.container.can.model.SignalMeasurement;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
public class CanSignalInspectorRequest extends OperationObject {

    private String messageName;

    private List<SignalMeasurement> signalMeasurements = new ArrayList<>();

    @Override
    public String getFriendlyString() {
        if (signalMeasurements != null && !signalMeasurements.isEmpty()) {
            String signalInfo = signalMeasurements.stream()
                    .map(sm -> sm.getSignalName() + ":" + sm.getSignalValue())
                    .collect(Collectors.joining(", "));
            return String.format("报文:%s 信号:[%s]", messageName, signalInfo);
        } else {
            return String.format("报文:%s", messageName);
        }
    }
}
