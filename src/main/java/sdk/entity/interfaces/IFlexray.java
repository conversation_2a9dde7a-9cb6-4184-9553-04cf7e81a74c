package sdk.entity.interfaces;

import sdk.base.operation.OperationResult;
import sdk.domain.bus.FlexrayMessage;

public interface IFlexray {
    OperationResult startTsMaster();

    OperationResult stopTsMaster();

    OperationResult sendFlexrayMessage(Integer channel, FlexrayMessage flexrayMessage);

    OperationResult fetchRunningFlexrayMessage(Integer channel);

    OperationResult readFlexrayDataByIdCycle(Integer channel, FlexrayMessage flexrayMessage);

    OperationResult stopFlexrayMessage(Integer channel, int messageId);

    OperationResult stopAllFlexrayMessage(Integer channel);

    OperationResult closeOpenButNotConfigDevice();
}
