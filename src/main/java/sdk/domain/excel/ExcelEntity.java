package sdk.domain.excel;

import lombok.Data;

import java.util.List;

@Data
public class ExcelEntity {
    private List<String> allSheetNames;  //所有的工作表名称

    private String templateFilePath; //复制的Excel路径

    private List<String> selectedSheetNames; //选中的工作表名称

    private String readOption; //读取选项

    private int headerRowNumber; //表头行数

    private String originalFilePath; //原始的Excel路径

    private String operateSheetName; //当前操作的工作表名称

    private String exportFilePath;   //导出报告的文件路径

    private String exportType; //导入本地文件、测试生成的报告文件、手动导出的报告文件

    private boolean reloadProject;  //是否重启应用加载用例

    private String projectName; //项目名称
}
