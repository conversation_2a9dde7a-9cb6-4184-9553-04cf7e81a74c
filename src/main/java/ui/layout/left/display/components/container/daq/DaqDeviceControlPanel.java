package ui.layout.left.display.components.container.daq;

import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.DataAcquirementConfig;
import sdk.entity.DaqDevice;
import ui.base.BaseView;
import ui.base.component.TimeSelector;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.base.DeviceControlPanel;
import ui.layout.left.display.components.toolkit.monitor.CurrentMonitorPanel;
import ui.layout.left.display.components.toolkit.monitor.MonitorParameterConfig;
import ui.layout.left.display.components.toolkit.monitor.VoltageMonitorPanel;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;

public class DaqDeviceControlPanel extends DeviceControlPanel implements BaseView {

    private final JButton currentAcquireDebugButton;

    private final TimeSelector currentAcquireTimeSelector;

    private final JSpinner currentAcquireIntervalSpinner;

    private final JCheckBox foreverCurrentAcquireCheckbox;

    private final JButton staticCurrentAcquireAddToScriptButton;

    private final VoltageMonitorPanel voltageMonitorPanel;
    private final CurrentMonitorPanel currentMonitorPanel;

    private final DaqDevice device;

    public DaqDeviceControlPanel(DeviceContainer deviceContainer, MainModel mainModel) {
        super(deviceContainer, mainModel);
        device = (DaqDevice) getDeviceContainer().getDevice();

        currentAcquireDebugButton = SwingUtil.getDebugButton();
        currentAcquireTimeSelector = new TimeSelector();
        currentAcquireIntervalSpinner = new JSpinner(new SpinnerNumberModel(1.0f, 0.1f, 1000.0f, 1.0f));
        foreverCurrentAcquireCheckbox = new JCheckBox();
        staticCurrentAcquireAddToScriptButton = SwingUtil.getAddToScriptButton();
        MonitorParameterConfig parameterConfig = new MonitorParameterConfig() {
            @Override
            public Integer getChannel() {
                return DaqDeviceControlPanel.this.getChannel();
            }

            @Override
            public Integer getMaxValue() {
                return 60;
            }
        };
        voltageMonitorPanel = new VoltageMonitorPanel(mainModel, device, parameterConfig);
        currentMonitorPanel = new CurrentMonitorPanel(mainModel, device, parameterConfig);
        voltageMonitorPanel.setOnlyDC(false);
        currentMonitorPanel.setOnlyDC(false);
        createView();
        createActions();
    }

    @Override
    public void createView() {
        Box box = Box.createVerticalBox();

        Box staticCurrentBox = Box.createHorizontalBox();
        staticCurrentBox.add(new JLabel("监控持续时间(s):"));
        staticCurrentBox.add(currentAcquireTimeSelector);

        staticCurrentBox.add(new JLabel("一直采集:"));
        staticCurrentBox.add(foreverCurrentAcquireCheckbox);
        staticCurrentBox.add(Box.createHorizontalGlue());

        Box intervalBox = Box.createHorizontalBox();
        intervalBox.add(new JLabel("监控间隔时间(s):"));
        intervalBox.add(currentAcquireIntervalSpinner);

        Box mainbox = Box.createVerticalBox();
        mainbox.setBorder(BorderFactory.createTitledBorder(new LineBorder(new Color(0, 0, 0)), "(静态)电流采集:", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        mainbox.add(staticCurrentBox);
        mainbox.add(Box.createVerticalStrut(10));//创建垂直间隔
        mainbox.add(intervalBox);

        Box assistBox = Box.createVerticalBox();
        assistBox.add(currentAcquireDebugButton);
        assistBox.add(staticCurrentAcquireAddToScriptButton);

        Box durationBox = Box.createHorizontalBox();
        durationBox.add(mainbox, BorderLayout.WEST);
        durationBox.add(assistBox, BorderLayout.EAST);

        box.add(durationBox);
        box.add(voltageMonitorPanel);
        box.add(currentMonitorPanel);
        add(box);
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        currentAcquireDebugButton.setEnabled(isDeviceConnected);
        voltageMonitorPanel.controlDisplay(isDeviceConnected);
    }

    public int getDuration() {
        if (foreverCurrentAcquireCheckbox.isSelected()) {
            return -1;
        }
        return currentAcquireTimeSelector.getSeconds();
    }

    public float getInterval() {
        return ((Double) currentAcquireIntervalSpinner.getValue()).floatValue();
    }

    @Override
    public void createActions() {
        foreverCurrentAcquireCheckbox.addActionListener(e ->
                SwingUtil.setPanelEnabled(currentAcquireTimeSelector, !foreverCurrentAcquireCheckbox.isSelected()));
        currentAcquireDebugButton.addActionListener(e -> {
            OperationResult operationResult = device.fetchCurrent();
            if (operationResult.isOk()) {
                SwingUtil.showInformationDialog(this, "当前静态电流:" + operationResult.getData());
            } else {
                SwingUtil.showWebMessageDialog(this, operationResult.getMessage());
            }
        });
        staticCurrentAcquireAddToScriptButton.addActionListener(e -> {
            int duration = getDuration();
            float interval = getInterval();
            if (duration == 0) {
                SwingUtil.showWarningDialog(DaqDeviceControlPanel.this, "请设置时间");
            } else {
                DataAcquirementConfig dataAcquirementConfig = new DataAcquirementConfig();
                dataAcquirementConfig.setDuration(duration);
                dataAcquirementConfig.setInterval(interval);
                Operation operation = Operation.buildOperation(device);
                operation.setOperationMethod(DeviceMethods.staticCurrentAcquire);
                operation.setOperationObject(dataAcquirementConfig);
                getMainModel().getOperationModel().updateOperation(operation);
            }
        });
    }
}
