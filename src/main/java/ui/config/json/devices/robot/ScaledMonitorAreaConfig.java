package ui.config.json.devices.robot;

import lombok.Data;
import ui.base.picture.ScaledPoint;
import ui.layout.left.display.components.container.picture.RoiRect;

@Data
public class ScaledMonitorAreaConfig {

    //起点
    private Double startX;
    //起点y
    private Double startY;
    //终点x
    private Double endX;
    //终点y
    private Double endY;

    public RoiRect toRoiRect() {
        RoiRect roiRect = new RoiRect();
        roiRect.setPointStart(new ScaledPoint(startX, startY));
        roiRect.setPointEnd(new ScaledPoint(endX, endY));
        return roiRect;
    }

    public void clear() {
        startX = null;
        startY = null;
        endX = null;
        endY = null;
    }

    public void build(RoiRect calibrationRoi) {
        startX = calibrationRoi.getPointStart().getX();
        startY = calibrationRoi.getPointStart().getY();
        endX = calibrationRoi.getPointEnd().getX();
        endY = calibrationRoi.getPointEnd().getY();
    }
}