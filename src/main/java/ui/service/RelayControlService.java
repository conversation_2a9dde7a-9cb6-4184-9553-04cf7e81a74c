package ui.service;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import sdk.constants.DeviceType;
import sdk.domain.Device;
import sdk.entity.ElectricRelayDevice;
import ui.layout.left.display.components.container.electric_relay.ElectricRelayContainer;
import ui.utils.SwingUtil;

import java.util.Set;

/**
 * 继电器控制服务
 * 作为CanDbcSendSettingView和ElectricRelayContainer之间的中间层
 */
@Slf4j
public class RelayControlService {
    private volatile static RelayControlService instance;

    /**
     * 获取单例实例
     *
     * @return 服务实例
     */
    public static RelayControlService getInstance() {
        if (instance == null) {
            synchronized (RelayControlService.class) {
                if (instance == null) {
                    instance = new RelayControlService();
                }
            }
        }
        return instance;
    }



    /**
     * 触发继电器通道
     *
     * @param deviceName 设备名称
     * @param channel    通道号
     * @return 是否成功触发
     */
    public boolean triggerRelayChannel(String deviceName, int channel) {
        return false;
    }

    /**
     * 通知刷新继电器面板
     */
    public void notifyRefreshRelayPanel() {
        log.info("通知刷新继电器面板");

    }


    /**
     * 获取连接的继电器设备
     *
     * @return 继电器设备，如果没有找到则返回null
     */
    public ElectricRelayDevice getConnectedRelayDevice() {
        for (Device device : Device.getConnectedDevices("relayType")) {
            if (device instanceof ElectricRelayDevice) {
                return (ElectricRelayDevice) device;
            }
        }
        return null;
    }


    /**
     * 触发指定继电器的通道点击事件
     *
     * @param relayName 继电器设备别名，用于识别目标继电器
     * @param channel 要触发的通道编号
     */
    public void relayClick(String relayName, Integer channel) {
        // 从已连接的继电器设备中查找匹配目标别名的设备
        ElectricRelayDevice relayDevice = null;
        Set<Device> devices = Device.getConnectedDevices(DeviceType.DEVICE_ELECTRIC_RELAY);
        if (CollectionUtil.isNotEmpty(devices)) {
            for (Device device : devices) {
                if (device.getAliasName().equals(relayName)) {
                    relayDevice = (ElectricRelayDevice) device;
                    break;
                }
            }
        } else {
            SwingUtil.showWarningDialog(null, "请先添加继电器设备！");
            return;
        }
        // 如果找到目标设备，则触发对应通道的点击事件
        if (relayDevice != null) {
            ElectricRelayContainer electricRelayContainer = PanelManager.getInstance().getRelayContainers().get(relayName);
            if (electricRelayContainer != null) {
                // 点击继电器对应的通道
                electricRelayContainer.triggerChannel(channel);
            }
        }
    }
}