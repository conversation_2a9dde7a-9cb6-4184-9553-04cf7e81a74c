package ui.layout.left.display.components.container.robot;

import lombok.Data;
import lombok.EqualsAndHashCode;
import sdk.base.operation.OperationObject;

import java.util.Arrays;
import java.util.List;

/**
 * @projectName: aitestxclient
 * @package: ui.layout.left.display.components.container.robot
 * @className: SwipeQuicklyPoint
 * @author: <PERSON><PERSON><PERSON>
 * @description: TODO
 * @date: 2024/3/13 21:37
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SwipeQuicklyPoint extends OperationObject {
    private String coordinateName;
    private int direction;
    private int dynamics;

    @Override
    public String getFriendlyString() {
        List<String> directionList = Arrays.asList("↗", "→", "↘", "↓", "↑", "↖", "←", "↙");
        return String.format("坐标:%s / 滑动方向:%s / 滑动力度:%s", coordinateName, directionList.get(direction - 1), dynamics);
    }
}
