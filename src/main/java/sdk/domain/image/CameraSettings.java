package sdk.domain.image;

import lombok.Builder;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: TODO
 * @date: 2024/7/5 13:38
 */

@Data
@Builder
public class CameraSettings {
    private int autoExposureMode;
    private int autoExposureTimeLower;
    private int autoExposureTimeUpper;
    private float frameRate;
    private float exposureTime;

    public CameraSettings(int autoExposureMode, int autoExposureTimeLower, int autoExposureTimeUpper, float frameRate, float exposureTime) {
        this.autoExposureMode = autoExposureMode;
        this.autoExposureTimeLower = autoExposureTimeLower;
        this.autoExposureTimeUpper = autoExposureTimeUpper;
        this.frameRate = frameRate;
        this.exposureTime = exposureTime;
    }

    public CameraSettings() {
        this.autoExposureMode = 0;
        this.autoExposureTimeLower = 44;
        this.autoExposureTimeUpper = 5050;
        this.frameRate = 30.0f;
        this.exposureTime = 3000;
    }
}
