package ui.base.picture;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;

public class ImageDisplayDialog extends JDialog {

    private static final long serialVersionUID = -2216276219179107707L;
    private static final int WIDTH = 800;
    private static final int HEIGHT = 600;

    @Data
    @AllArgsConstructor
    public static class Image {
        private String imageName;
        private String imagePath;
        private double ratio;
    }

    public ImageDisplayDialog(String title, String text, Image... images) {
        setTitle(title);
        Container container = getContentPane();
//        container.setLayout(new BorderLayout());
        Dimension dialogDimension = new Dimension(WIDTH, HEIGHT);
        setSize(dialogDimension);
        Box imageBox = Box.createHorizontalBox();
        for (Image image : images) {
            JPanel borderPane = new JPanel(new BorderLayout());
            borderPane.setBorder(new TitledBorder(image.imageName));
            ImageDisplayPanel photoPanel = new ImageDisplayPanel();
            photoPanel.setImagePath(image.imagePath);
            photoPanel.setPreferredSize(new Dimension(photoPanel.getImgWidth(), photoPanel.getImgHeight()));
            borderPane.add(photoPanel, BorderLayout.CENTER);
            imageBox.add(borderPane);
        }
        container.add(imageBox, BorderLayout.CENTER);
        if (text != null) {
            container.add(new JLabel(text), BorderLayout.SOUTH);
        }
        finalSetting();
    }

    private void finalSetting() {
        setLocationRelativeTo(null);
        setModal(true);
        setResizable(true);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        setVisible(true);
    }

}