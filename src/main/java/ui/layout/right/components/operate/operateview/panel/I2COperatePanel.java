package ui.layout.right.components.operate.operateview.panel;

import sdk.entity.OperationTargetHolder;
import sdk.entity.I2CDevice;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.model.MainModel;

public class I2COperatePanel extends DeviceOperatePanel {

    public I2COperatePanel(MainModel mainModel) {
        super(mainModel);
        I2CDevice httpClient = OperationTargetHolder.getI2cDeviceManager();
        setDeviceClient(httpClient);
        createView();
        createActions();
    }
}
