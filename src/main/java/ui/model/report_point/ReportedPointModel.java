package ui.model.report_point;

import sdk.domain.PointInt;
import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.util.ArrayList;
import java.util.List;

public class ReportedPointModel implements ModelObservable , ReportedPointObserver {
    private final List<ModelObserver> modelObservers = new ArrayList<>();
    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void transportReportedPoint(PointInt touchPoint) {
        for (ModelObserver modelObserver : modelObservers) {
            ((ReportedPointObserver) modelObserver).transportReportedPoint(touchPoint);
        }
    }
}
