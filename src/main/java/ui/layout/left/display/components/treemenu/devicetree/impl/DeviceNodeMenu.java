package ui.layout.left.display.components.treemenu.devicetree.impl;

import lombok.extern.slf4j.Slf4j;
import ui.base.treelist.AbstractMenu;
import ui.entry.ClientView;
import ui.layout.left.display.components.treemenu.devicetree.dialog.base.DeviceDialog;
import ui.layout.left.display.components.treemenu.devicetree.dialog.dialogs.AddDeviceDialog;
import ui.layout.left.display.components.treemenu.devicetree.manager.DialogPaneManager;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;


/**
 * <AUTHOR> lhy
 * @date : Created in 2023/7/4 11:08
 * @description :
 * @modified By :
 * @since : 2023/7/4
 **/
@Slf4j
public class DeviceNodeMenu extends AbstractMenu implements ActionListener {
    private final MainModel mainModel;
    private final ClientView clientView;
    private final DialogPaneManager dialogPaneManager;

    public DeviceNodeMenu(ClientView clientView,
                          MainModel mainModel,
                          DialogPaneManager dialogPaneManager) {
        this.mainModel = mainModel;
        this.clientView = clientView;
        this.dialogPaneManager = dialogPaneManager;
        JMenuItem addDeviceMenuItem = new JMenuItem("添加设备");
        addDeviceMenuItem.addActionListener(this);
        add(addDeviceMenuItem);
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        showAddDeviceDialog();
    }

    private void showAddDeviceDialog() {
        new SwingWorker<Void, Void>() {
            DeviceOperateTabWrapper deviceWrapperPane;

            @Override
            protected Void doInBackground() {
                try {
                    deviceWrapperPane = dialogPaneManager.getWrapperByDeviceType(((DeviceNode) getRelatedNode()).getDeviceType());
                } catch (Exception e) {
                    log.error("获取设备面板失败", e);
                }
                return null;
            }
            @Override
            protected void done() {
                if (deviceWrapperPane == null) {
                    log.warn("设备面板为空");
                    return;
                }
                DeviceDialog deviceDialog = new AddDeviceDialog((DeviceNode) getRelatedNode(),
                        deviceWrapperPane, mainModel, clientView);
                deviceDialog.setModal(true);//确保弹出的窗口在其他窗口前面
                deviceDialog.setVisible(true);
            }
        }.execute();
    }


//    @Override
//    public void editDevice(AbstractTreeNode parentNode, Device device) {
//        if (device != null) {
////            this.parentNode = parentNode;
//            addDevice(device);
//            TreeDeviceConfigManager.getTreeDeviceConfigManager()
//                    .addTreeDeviceConfig(
//                            device.getDeviceType(),
//                            device.getDeviceModel(),
//                            0, device)
//                    .save();
//        }
//    }
}
