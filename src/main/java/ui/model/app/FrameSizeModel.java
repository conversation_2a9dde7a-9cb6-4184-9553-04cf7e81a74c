package ui.model.app;

import ui.model.ModelObservable;
import ui.model.ModelObserver;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class FrameSizeModel  implements ModelObservable,FrameSizeObserver {
    private final List<ModelObserver> modelObservers = new ArrayList<>();
    @Override
    public void registerObserver(ModelObserver modelObserver) {
        modelObservers.add(modelObserver);
    }

    @Override
    public void removeObserver(ModelObserver modelObserver) {
        modelObservers.remove(modelObserver);
    }

    @Override
    public void getFrameSize(Dimension frameSize) {
        for (ModelObserver modelObserver : modelObservers) {
            ((FrameSizeObserver) modelObserver).getFrameSize(frameSize);
        }
    }
}
