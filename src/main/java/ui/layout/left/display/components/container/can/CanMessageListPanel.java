package ui.layout.left.display.components.container.can;

import javax.swing.*;
import java.awt.*;

public class CanMessageListPanel extends JPanel {
    public CanMessageListPanel() {
        setPreferredSize(new Dimension(1220, 50));
        JCheckBox checkBox = new JCheckBox(" 0");
        checkBox.setBounds(0, 0, 50, 50);
        this.add(checkBox);

        JLabel state = new JLabel("��");
        state.setBounds(78, 16, 21, 18);
        this.add(state);

        JLabel frameIDInList = new JLabel("508");
        frameIDInList.setBounds(144, 16, 24, 18);
        this.add(frameIDInList);

        JComboBox<String> protocolComboBox = new JComboBox<>();
        protocolComboBox.setModel(new DefaultComboBoxModel<>(new String[]{"CAN", "CANFD", "CANFD加速"}));
        protocolComboBox.setBounds(212, 2, 79, 46);
        this.add(protocolComboBox);

        JComboBox<String> lengthComboBox = new JComboBox<>();
        lengthComboBox.setModel(new DefaultComboBoxModel<>(new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8"}));
        lengthComboBox.setBounds(297, 2, 50, 46);
        this.add(lengthComboBox);

        JLabel framesPerSendLabelInList = new JLabel("1");
        framesPerSendLabelInList.setBounds(660, 16, 72, 18);
        this.add(framesPerSendLabelInList);

        JLabel sendTimeLabelInList = new JLabel("-1");
        sendTimeLabelInList.setBounds(762, 16, 72, 18);
        this.add(sendTimeLabelInList);

        JLabel everyIntervalInList = new JLabel("10");
        everyIntervalInList.setBounds(848, 16, 72, 18);
        this.add(everyIntervalInList);
    }
}
