package sdk.entity.interfaces;

import sdk.base.operation.OperationResult;
import sdk.domain.CriticalVoltage;
import sdk.domain.CustomizePulse;
import sdk.domain.StepVoltage;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-27 16:41
 * @description :
 * @modified By :
 * @since : 2022-6-27
 */
public interface IPower extends IMonitorable {

    OperationResult outputOn();

    OperationResult outputOn(int channel);

    OperationResult outputOff();

    OperationResult outputOff(int channel);

    OperationResult setVoltage(float voltage);

    OperationResult setVoltage(int channel, float voltage);

    OperationResult setVoltageWithUnit(int channel, String voltage);


    OperationResult setStepVoltage(Integer channel, StepVoltage stepVoltage);

    OperationResult loadWavePulse(int memory);

    OperationResult stopWavePulse();

    OperationResult loadCustomizePulse(CustomizePulse customizePulse);

    OperationResult searchMinimumCriticalVoltage(CriticalVoltage criticalVoltage);

    OperationResult setCurrentWithUnit(int channel, String current);

    /**
     * 获取电压
     *
     * @return 电压
     */
    OperationResult fetchVoltage();

    OperationResult fetchOutput();
}
