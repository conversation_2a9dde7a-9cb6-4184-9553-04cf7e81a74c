package ui.base;

import lombok.Getter;

import javax.swing.*;

@Getter
public abstract class NamedSwingWorker<T, V> extends SwingWorker<T, V> {
    private final String taskName;

    public NamedSwingWorker(String name) {
        this.taskName = name;
    }

    protected void beforeTask() {

    }

    @Override
    protected T doInBackground() throws Exception {
        // 保存原始线程名称
        String previousName = Thread.currentThread().getName();
        try {
            Thread.currentThread().setName(taskName);
            beforeTask();
            return performTask();
        } finally {
            // 恢复原始线程名称
            Thread.currentThread().setName(previousName);
        }
    }

    protected abstract T performTask() throws Exception;
}