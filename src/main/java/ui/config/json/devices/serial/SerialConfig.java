package ui.config.json.devices.serial;

import lombok.Data;
import lombok.EqualsAndHashCode;
import ui.config.json.devices.DeviceConfig;

import java.util.HashMap;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class SerialConfig extends DeviceConfig {

    private boolean sendOrReceiveWithHex; //是否hex发送接收

    private String commandsConfigPath; //指令集合配置路径

    private boolean saveLogFlag; //是否开启自动保存日志

    private int logSaveType;//log保存方式 0-日期 1-大小 2-脚本

    private String logSaveTimePeriod;//日志保存时间周期(日期) hour day

    private String logMaxFileSize;//日志最大保存量

    private Map<Integer, SerialMessage> textMapSendWhenOpen = new HashMap<>();
    private boolean useCustomLogPath; // 是否启用自定义日志路径

    private String customLogPath; // 自定义日志文件路径

    private float receiveTimeout; //序列全局等待时间

    public SerialMessage getSerialMessage(int index) {
        if (!textMapSendWhenOpen.containsKey(index)) {
            textMapSendWhenOpen.put(index, new SerialMessage());
        }
        return textMapSendWhenOpen.get(index);
    }


}
