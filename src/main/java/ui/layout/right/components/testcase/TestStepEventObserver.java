package ui.layout.right.components.testcase;

import sdk.domain.action_sequence.ActionSequence;
import sdk.domain.action_sequence.ActionSequenceCheckReporter;
import ui.model.ModelObserver;

import java.util.List;
import java.util.Map;

public interface TestStepEventObserver extends ModelObserver {

    default void handleTestStepScrollPane(int row) {

    }

    default void switchTestStep(String columnName) {

    }

    default void groupingByStepType(List<TestStep> testStepList) {

    }

    default void renderTestStepCheckReport(ActionSequenceCheckReporter actionSequenceCheckReporter) {

    }

    default void renderTestStepRowHeaderMarker(String stepType) {

    }


    default void renderTestStepExecuteResultColor(ActionSequence actionSequence) {

    }


    default void renderTestStepRowColorByUuid(String testStepUuid) {

    }

    default void renderTestStepErrorColor(Map<Integer, ActionSequence> actionSequenceErrorMap) {

    }

    default void clearTestStepErrorColor() {

    }

    default void clearTestStepExecuteColor() {

    }

    default void setExcelTableRowId(int row) {

    }


    default void switchPanel(boolean isTestStepPanel) {

    }
}