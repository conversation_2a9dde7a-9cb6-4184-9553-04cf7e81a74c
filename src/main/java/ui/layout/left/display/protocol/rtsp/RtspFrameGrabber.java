package ui.layout.left.display.protocol.rtsp;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-6 18:41
 * @description : RTSP帧获取
 * @modified By :
 * @since : 2022-4-6
 */
@Deprecated
//@Slf4j
public class RtspFrameGrabber {
//    private static boolean loop = true;
//
//    static {
//        try {
//            FFmpegFrameGrabber.tryLoad();
//            FFmpegLogCallback.set();
//        } catch (FrameGrabber.Exception e) {
//            log.error(e.getMessage(), e);
//        }
//        Runtime.getRuntime().addShutdownHook(new Thread(() -> loop = false));
//    }
//
//    private static FFmpegFrameGrabber startGrabber(String rtspUrl) throws FFmpegFrameGrabber.Exception {
//        FFmpegFrameGrabber grabber = FFmpegFrameGrabber.createDefault(rtspUrl);
//        // 设置读取的最大数据，单位字节
//        grabber.setOption("probesize", "100000");
//        // 设置分析的最长时间，单位微秒
//        grabber.setOption("analyzeduration", "0");
//        grabber.setOption("rtsp_transport", "tcp");
//        grabber.setVideoCodec(avcodec.AV_CODEC_ID_MPEG4);
//        grabber.setVideoBitrate(8000000);
//        grabber.start(false);
//        return grabber;
//    }
//
//    //    public static SynchronousQueue<BufferedImage> grab(String cameraName) throws IOException {
//    public static SynchronousQueue<BufferedImage> grab(Device device) throws IOException {
//        String deviceModel = device.getDeviceModel();
//        Integer devicePort = device.getDevicePort();
//        String rtspUrl = UrlConstants.DeviceUrls.CameraUrls.getCameraRtspUrl(deviceModel, devicePort);
//        log.debug("grab rtsp:{}", rtspUrl);
//        FFmpegFrameGrabber grabber = startGrabber(rtspUrl);
//        log.debug("start grabber");
//        SynchronousQueue<BufferedImage> synchronousQueue = new SynchronousQueue<>();
//        Java2DFrameConverter converter = new Java2DFrameConverter();
//        new Thread(() -> {
//            while (loop) {
//                Frame frame;
//                BufferedImage bufferedImage;
//                try {
//                    frame = grabber.grab(); //拉流
//                    bufferedImage = converter.convert(frame);
////                    System.out.println("bufferedImage:" + bufferedImage);
//                    if (bufferedImage != null) {
//                        synchronousQueue.put(bufferedImage);
//                    }
//                    //TODO：增加rtsp被重置后刷新按钮, rtsp_yaml：readTimeout设置小点进行测试
//                } catch (InterruptedException | IOException e) {
//                    log.error(e.getMessage(), e);
//                }
//            }
//            try {
//                grabber.close();
//            } catch (FrameGrabber.Exception e) {
//                log.error(e.getMessage(), e);
//            }
//        }).start();
//        return synchronousQueue;
//    }
//
//    public static void main(String[] args) throws Exception {
//        String inputPath = "rtsp://127.0.0.1:8554/camera/TTQ_HD_Camera";
//        FFmpegFrameGrabber grabber = FFmpegFrameGrabber.createDefault(inputPath);
//        // 设置读取的最大数据，单位字节
//        grabber.setFormat("rtsp");
//        grabber.setOption("probesize", "10000");
//        // 设置分析的最长时间，单位微秒
//        grabber.setOption("analyzeduration", "10000");
//        grabber.setOption("rtsp_transport", "tcp");
//        System.out.println("start grabber");
//        //开启采集器
//        grabber.start();
//        System.out.println("start successful");
//        //直播播放窗口
//        CanvasFrame canvasFrame = new CanvasFrame("grabber " + inputPath);
//        canvasFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
//        canvasFrame.setAlwaysOnTop(true);
//        System.out.println("getFrameRate:" + grabber.getFrameRate());
//        System.out.println("getVideoBitrate:" + grabber.getVideoBitrate());
//        System.out.println("getImageWidth:" + grabber.getImageWidth());
//        System.out.println("getImageHeight:" + grabber.getImageHeight());
//        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
//            System.out.println("do shutdown hook");
//            try {
//                grabber.close();
//                grabber.release();
//            } catch (FrameGrabber.Exception e) {
//                log.error(e.getMessage(), e);
//            }
//        }));
//        //播流
//        while (true) {
//            Frame frame = grabber.grab();  //拉流
//            System.out.println(frame);
//            if (frame != null) {
//                canvasFrame.showImage(frame);   //播放
//            }
//            Thread.sleep(10);
//        }
//    }

}
