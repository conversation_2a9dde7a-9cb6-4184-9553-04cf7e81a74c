package excelcase.exportcase;

import lombok.Data;

import java.io.File;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/5/8 16:31
 * @description :
 * @modified By :
 * @since : 2023/5/8
 **/
@Data
public class CaseReportFileConfig {
    private volatile static CaseReportFileConfig caseReportFileConfig;
    //report配置文件
    private File functionalTestReportFile;
    private File templateFile;
    private File smokingTestReportFile;

    public static CaseReportFileConfig getInstance() {
        if (caseReportFileConfig == null) {
            synchronized (CaseReportFileConfig.class) {
                if (caseReportFileConfig == null) {
                    caseReportFileConfig = new CaseReportFileConfig();
                }
            }
        }
        return caseReportFileConfig;
    }

}
