package ui.layout.left.display.components.container.power.kikusui.pulse_edit;

public class PulseEditTableHeaderValue {

    private static int index = 0;
    public static final int COLUMN_INDEX = index++;
    public static final int COLUMN_TIME = index++;
    public static final int COLUMN_VOLTAGE = index++;
    public static final int COLUMN_INTERVAL = index++;
    public static final int COLUMN_TRANSITION = index++;
    public static final int COLUMN_START_V = index++;
    public static final int COLUMN_AC = index++;
    public static final int COLUMN_WAVEFORM = index++;
    public static final int COLUMN_START_VPP = index++;
    public static final int COLUMN_END_VPP = index++;
    public static final int COLUMN_START_HZ = index++;
    public static final int COLUMN_END_HZ = index++;
    public static final int COLUMN_F_SWEEP= index++;
    public static final int COLUMN_PHASE_DEG= index++;
}
