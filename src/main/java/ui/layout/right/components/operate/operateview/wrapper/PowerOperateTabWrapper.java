package ui.layout.right.components.operate.operateview.wrapper;

import lombok.Getter;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperatePanel;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.*;
import ui.model.MainModel;

public class PowerOperateTabWrapper extends DeviceOperateTabWrapper {
    private final MainModel mainModel;
    @Getter
    private DeviceOperatePanel it68xxPowerOperatePanel;
    @Getter
    private DeviceOperatePanel it6322PowerOperatePanel;
    @Getter
    private DeviceOperatePanel it63xxPowerOperatePanel;
    @Getter
    private DeviceOperatePanel it69xxPowerOperatePanel;
    @Getter
    private DeviceOperatePanel it65xxPowerOperatePanel;
    @Getter
    private DeviceOperatePanel it67xxPowerOperatePanel;
    @Getter
    private DeviceOperatePanel powerBoxOperatePanel;
    @Getter
    private DeviceOperatePanel kikusuiPowerOperatePanel;
    @Getter
    private DeviceOperatePanel n6700PowerOperatePanel;
    @Getter
    private DeviceOperatePanel mps3610HPowerOperatePanel;
    @Getter
    private DeviceOperatePanel pvd8V50EPowerOperatePanel;

    public PowerOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
        this.mainModel = mainModel;
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_POWER;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        initOperatePane();
        addOperatePanel("IT6831(*)", it68xxPowerOperatePanel);
        addOperatePanel("IT6322", it6322PowerOperatePanel);
        addOperatePanel("IT6322B(*)", it63xxPowerOperatePanel);
        addOperatePanel("IT6932A(*)", it69xxPowerOperatePanel);
        addOperatePanel("IT6512(*)", it65xxPowerOperatePanel);
        addOperatePanel("IT6722(*)", it67xxPowerOperatePanel);
        addOperatePanel("电源控制盒", powerBoxOperatePanel);
        addOperatePanel("菊水电源", kikusuiPowerOperatePanel);
        addOperatePanel("N6700高精度电源", n6700PowerOperatePanel);
        addOperatePanel("MPS-3610/201系列", mps3610HPowerOperatePanel);
        addOperatePanel("PVD8V50E", pvd8V50EPowerOperatePanel);
    }

    private void initOperatePane() {
        String usbMessage = "<html>USB连接方式需安装VISA驱动<br>";
//        it68xxPowerOperatePanel = new PowerOperatePanel(mainModel, DeviceModel.Power.IT68xx,
//                "<html>适用于IT6821/IT6822/IT6823/IT6831/IT6832/IT6833<br>" +
//                        "/IT6834/IT6832A/IT6833A/IT6861A/IT6862A/IT6863A<br>" +
//                        "/IT6872A/IT6873A/IT6874A程控电源</html>");
        it68xxPowerOperatePanel = new ComplexPowerOperatePanel(mainModel, DeviceModel.Power.IT68xx,
                "<html>适用于IT6821/IT6822/IT6823/IT6831/IT6832/IT6833<br>" +
                        "/IT6834/IT6832A/IT6833A/IT6861A/IT6862A/IT6863A<br>" +
                        "/IT6872A/IT6873A/IT6874A程控电源<br>" + "需安装VISA驱动" + "</html>");
        it6322PowerOperatePanel = new PowerOperatePanel(mainModel, DeviceModel.Power.IT6322);
        it63xxPowerOperatePanel = new ComplexPowerOperatePanel(mainModel, DeviceModel.Power.IT63xx,
                "<html>适用于IT6322A/IT6332A/IT6333A/IT6322B/IT6332B/IT6333B程控电源<br>" +
                        "支持RS232 UGreen通讯线/USB通讯线<br>" + usbMessage + "</html>");
        it69xxPowerOperatePanel = new ComplexPowerOperatePanel(mainModel, DeviceModel.Power.IT69xx,
                "<html>适用于IT6922A/IT6932A/IT6933A/IT6942A/IT6952A<br>" +
                        "/IT6953A/IT6922B/IT6932B/IT6942B/IT6952B程控电源<br>" +
                        "支持RS232 通讯线/USB通讯线<br>" + usbMessage + "</html>");
        it65xxPowerOperatePanel = new ComplexPowerOperatePanel(mainModel, DeviceModel.Power.IT65xx,
                "<html>适用于IT6512/IT6513/IT6512A/IT6513A<br>" +
                        "/IT6522A/IT6502D/IT6512D程控电源<br>" + usbMessage + "</html>");
        it67xxPowerOperatePanel = new ComplexPowerOperatePanel(mainModel, DeviceModel.Power.IT67xx,
                "<html>适用于IT6722/IT6722A/IT6723/IT6723B/IT6723C<br>" +
                        "/IT6723G/IT6723H/IT6724/IT6724B/IT6724C/IT6724G<br>" +
                        "/IT6724H/IT6726B/IT6726C/IT6726G/IT6726H/IT6726V程控电源<br>" +
                        usbMessage +
                        "</html>");
        powerBoxOperatePanel = new PowerOperatePanel(mainModel, DeviceModel.Power.POWER_BOX, false, "驱动:CH341/340");
        kikusuiPowerOperatePanel = new KikusuiOperatePanel(mainModel, DeviceModel.Power.KIKUSUI);
        n6700PowerOperatePanel = new N6700OperatePanel(mainModel, DeviceModel.Power.N6700);
        mps3610HPowerOperatePanel = new PowerOperatePanel(mainModel, DeviceModel.Power.MPS3610H, "使用前请确保安装驱动");
        pvd8V50EPowerOperatePanel = new PowerOperatePanel(mainModel, DeviceModel.Power.PVD8V50E);
    }

}
