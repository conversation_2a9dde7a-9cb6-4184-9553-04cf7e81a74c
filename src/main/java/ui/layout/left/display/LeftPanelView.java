package ui.layout.left.display;

import lombok.Getter;
import ui.base.BaseView;
import ui.entry.ClientView;
import ui.layout.bottom.BottomPanelView;
import ui.layout.left.display.components.tappane.DisplayTabPaneView;
import ui.layout.left.display.components.toolkit.DisplayToolkitView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * FlyTest左边面板视图
 */
public class LeftPanelView extends JPanel implements BaseView {
    @Getter
    private final DisplayTabPaneView displayTabPane;
    private final DisplayToolkitView displayToolkit;
    private final LeftPanelController controller;
    private final MainModel mainModel;
    @Getter
    private final BottomPanelView bottomPanelView;
    private final ClientView clientView;

    public LeftPanelView(LeftPanelController controller, MainModel mainModel) {
        super();
        this.controller = controller;
        this.mainModel = mainModel;
        clientView = controller.getClientView();
        displayTabPane = new DisplayTabPaneView(clientView, mainModel);
        bottomPanelView = new BottomPanelView(clientView, mainModel);
        displayToolkit = new DisplayToolkitView();
        createView();
        createActions();
    }

    public void createView() {
        setBackground(Color.gray);
        setLayout(new BorderLayout());
//        add(bottomPanelView, BorderLayout.SOUTH);
        displayToolkit.addToolKitEventListener(displayTabPane);
        JScrollPane scrollPane = new JScrollPane();

        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        //scrollPane.setPreferredSize(new Dimension(800,800));
        //displayTabPane.setPreferredSize(new Dimension(800,800));
        scrollPane.setPreferredSize(new Dimension((int) screenSize.getWidth() / 2, (int) screenSize.getHeight() / 2));
        displayTabPane.setPreferredSize(new Dimension((int) screenSize.getWidth() / 2, (int) screenSize.getHeight() / 2));

        scrollPane.setViewportView(displayTabPane);
        scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_NEVER);
        add(scrollPane);
//        add(displayTabPane);
        updateUI();

    }
}
