package ui.layout.left.display.components.toolkit.monitor;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class DeviceMonitorConstant {
    private DeviceMonitorType deviceMonitorType;
    private String monitorTitle;
    private String monitorLabel;

    public static DeviceMonitorConstant voltageMonitor = new DeviceMonitorConstant(DeviceMonitorType.VOLTAGE, "电压监控", "电压");
    public static DeviceMonitorConstant currentMonitor = new DeviceMonitorConstant(DeviceMonitorType.CURRENT, "电流监控", "电流");
    public static DeviceMonitorConstant volumeMonitor = new DeviceMonitorConstant(DeviceMonitorType.VOLUME, "音量监控", "音量");
}