package ui.config.xml.app;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import ui.config.base.FileManager;
import ui.config.base.Folder;

import java.io.File;
import java.lang.reflect.InvocationTargetException;

@Getter
@Slf4j
public class AppFileManager extends FileManager {

    private volatile static AppFileManager appFileManager;

    public static AppFileManager getInstance() {
        if (appFileManager == null) {
            synchronized (AppFileManager.class) {
                if (appFileManager == null) {
                    appFileManager = new AppFileManager();
                }
            }
        }
        return appFileManager;
    }

    public static final File baseAppPath = new File(baseAppDataPath, "app");

    private final Folder configFolder;

    private DeviceFolder devicesFolder;

    static {
        createFolder(baseAppPath);
    }

    private AppFileManager() {
        configFolder = createFolder(baseAppPath, "config");
        try {
            devicesFolder = createFolder(configFolder, "devices", DeviceFolder.class);
        } catch (InvocationTargetException | InstantiationException | IllegalAccessException |
                 NoSuchMethodException e) {
            log.error(e.getMessage(), e);
        }
    }

}
