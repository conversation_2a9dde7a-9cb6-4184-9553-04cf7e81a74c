package ui.layout.right.components.testscript.toolkit;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import sdk.base.operation.Operation;
import sdk.domain.CmdCommand;
import ui.base.BaseView;
import ui.base.OperationAssembler;
import ui.layout.left.display.dialogs.ConfirmDialog;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.UnsupportedFlavorException;
import java.awt.dnd.DnDConstants;
import java.awt.dnd.DropTarget;
import java.awt.dnd.DropTargetDropEvent;
import java.awt.event.ItemEvent;
import java.io.File;
import java.io.IOException;
import java.util.List;

@Slf4j
public class RunAnythingDialog extends ConfirmDialog implements BaseView, OperationAssembler<CmdCommand> {
    private final JPanel panel;
    private final JTextArea textArea;

    private final JCheckBox killLastCommandCheckbox;
    private final JPanel bottomPanel;
    private final JCheckBox returnJudgeCheckbox;
    private final JTextField returnJudgeTextField;
    private final JCheckBox showCmdWindowCheckbox;

    // 新增的组件
    private final JCheckBox mustContainCheckBox;
    private final JCheckBox forbidContainCheckBox;
    private final ButtonGroup pauseConditionGroup;
    private final JPanel pauseConditionPanel;

    public RunAnythingDialog() {
        panel = new JPanel(new BorderLayout(0, 10));
        textArea = new JTextArea(6, 40);
        bottomPanel = new JPanel(new GridLayout(5, 1));
        killLastCommandCheckbox = new JCheckBox("自动终止上一次命令");
        killLastCommandCheckbox.setSelected(true);
        returnJudgeCheckbox = new JCheckBox("返回值判断");
        returnJudgeTextField = new JTextField(30);

        showCmdWindowCheckbox = new JCheckBox("显示CMD窗口");
        showCmdWindowCheckbox.setSelected(false); // 默认不显示CMD窗口

        // 初始化新增的组件
        mustContainCheckBox = new JCheckBox("必须包含返回值");
        forbidContainCheckBox = new JCheckBox("不允许包含返回值");
        mustContainCheckBox.setSelected(true); // 默认选择

        pauseConditionGroup = new ButtonGroup();
        pauseConditionGroup.add(mustContainCheckBox);
        pauseConditionGroup.add(forbidContainCheckBox);
        pauseConditionPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        pauseConditionPanel.add(mustContainCheckBox);
        pauseConditionPanel.add(forbidContainCheckBox);
        pauseConditionPanel.setVisible(false);

        createView();
        createActions();
    }

    @Override
    public void createView() {
        super.createView();
        setTitle("Run Anything");
        setSize(new Dimension(600, 300));

        panel.add(new JLabel("输入要执行的任何指令或者文件(支持拖动粘贴)"), BorderLayout.NORTH);
        panel.add(new JScrollPane(textArea), BorderLayout.CENTER);
        bottomPanel.add(showCmdWindowCheckbox);
        bottomPanel.add(killLastCommandCheckbox);
        bottomPanel.add(returnJudgeCheckbox);
        returnJudgeTextField.setVisible(false);
        bottomPanel.add(returnJudgeTextField);
        bottomPanel.add(pauseConditionPanel); // 添加新的组件
        panel.add(bottomPanel, BorderLayout.SOUTH);
        textArea.setDropTarget(new DropTarget() {
            @SuppressWarnings("unchecked")
            @Override
            public synchronized void drop(DropTargetDropEvent evt) {
                try {
                    evt.acceptDrop(DnDConstants.ACTION_COPY);
                    List<File> droppedFiles = (List<File>) evt.getTransferable().getTransferData(DataFlavor.javaFileListFlavor);
                    for (File file : droppedFiles) {
                        textArea.setText(file.getAbsolutePath());
                    }
                } catch (IOException | UnsupportedFlavorException e) {
                    log.error(e.getMessage(), e);
                }
            }
        });
        pack();
        setLocationRelativeTo(null);
    }

    @Override
    public void createActions() {
        super.createActions();
        returnJudgeCheckbox.addItemListener(e -> {
            boolean isSelected = e.getStateChange() == ItemEvent.SELECTED;
            returnJudgeTextField.setVisible(isSelected);
            pauseConditionPanel.setVisible(isSelected && returnJudgeCheckbox.isSelected());
        });
    }

    @Override
    protected boolean performConfirm() {
        return checkUserInput();
    }

    public CmdCommand getCmdCommand() {
        CmdCommand cmdCommand = new CmdCommand();
        cmdCommand.setCommand(textArea.getText());
        cmdCommand.setKillLastCommand(killLastCommandCheckbox.isSelected());
        cmdCommand.setJudge(returnJudgeCheckbox.isSelected());
        if (returnJudgeCheckbox.isSelected()) {
            cmdCommand.setJudgeText(returnJudgeTextField.getText());
            cmdCommand.setMustContained(mustContainCheckBox.isSelected());
        }
        cmdCommand.setShowWindow(showCmdWindowCheckbox.isSelected());
        return cmdCommand;
    }

    @Override
    public Component makeCenterPanel() {
        return panel;
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) {
        CmdCommand cmdCommand = JSON.to(CmdCommand.class, injectedOperation.getOperationObject());
        textArea.setText(cmdCommand.getCommand());
        showCmdWindowCheckbox.setSelected(cmdCommand.isShowWindow());
        killLastCommandCheckbox.setSelected(cmdCommand.isKillLastCommand());
        returnJudgeCheckbox.setSelected(cmdCommand.isJudge());
        boolean isJudgeSelected = cmdCommand.isJudge();
        returnJudgeTextField.setVisible(isJudgeSelected);
        pauseConditionPanel.setVisible(isJudgeSelected && returnJudgeCheckbox.isSelected());

        if (isJudgeSelected && cmdCommand.getJudgeText() != null) {
            returnJudgeTextField.setText(cmdCommand.getJudgeText());
            mustContainCheckBox.setSelected(cmdCommand.isMustContained());
            forbidContainCheckBox.setSelected(!cmdCommand.isMustContained());
        }
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        CmdCommand cmdCommand = getCmdCommand(); // 使用 getCmdCommand 方法获取当前界面状态
        injectedOperation.setOperationObject(cmdCommand);
        return injectedOperation;
    }

    @Override
    public boolean checkUserInput() {
        if (getCmdCommand().getCommand().isEmpty()) {
            SwingUtil.showWarningDialog(this, "命令不能为空");
            return false;
        }
        if (returnJudgeCheckbox.isSelected()) {
            if (returnJudgeTextField.getText().isEmpty()) {
                SwingUtil.showWarningDialog(this, "返回值判断不能为空");
                return false;
            }
        }
        return true;
    }
}