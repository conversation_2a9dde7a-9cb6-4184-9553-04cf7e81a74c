package ui.layout.right.components.operate.header;

import lombok.Getter;

import javax.swing.*;

/**
 * 奇偶位/波特率组件
 */
@Getter
public class ParityBaudRateHeaderHookComponent extends BaudRateHeaderHookComponent {

    private JComboBox<String> parityComboBox;
    private final String[] parityOptions = {"None", "Odd", "Even", "Mark", "Space"};

    public ParityBaudRateHeaderHookComponent() {
        super(true);
        createView();
        createActions();

    }

    @Override
    public void createView() {
        super.createView();
        parityComboBox = new JComboBox<>();

        for (String parity : parityOptions) {
            parityComboBox.addItem(parity);
        }
        parityComboBox.setSelectedItem("None");
        add(new JLabel("奇偶位:"));
        add(parityComboBox);
    }

    public void setParity(String parity) {
        parityComboBox.setSelectedItem(parity);
    }
    public String getParity() {
        return (String) parityComboBox.getSelectedItem();
    }

    public Integer getParityIndex() {
        for (int i = 0; i < parityOptions.length; i++) {
            if (parityOptions[i].equalsIgnoreCase((String) parityComboBox.getSelectedItem())) {
                return i;
            }
        }
        return 0;
    }
    public void setParityEnabled(boolean isEnabled) {
        parityComboBox.setEnabled(isEnabled);
    }

}
