package ui.layout.left.display.components.container.power;

import com.alibaba.fastjson2.JSON;
import common.constant.UiConstants;
import common.exceptions.JsonAssembleException;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.CriticalVoltage;
import sdk.entity.PowerDevice;
import ui.base.BaseView;
import ui.base.OperationAssembler;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.*;
import java.util.Objects;

public class AutoVoltageSearchPanel extends JPanel implements BaseView, OperationAssembler<CriticalVoltage> {
    private final JSpinner lowerCriticalVoltageSpinner;
    private final JSpinner upperCriticalVoltageSpinner;
    private final JButton autoSearchCriticalVoltageButton;
    private final JButton autoSearchCriticalVoltageAddToScriptButton;

    private final JPanel criticalVoltagePanel;
    private final PowerDevice powerDevice;
    private final MainModel mainModel;

    public AutoVoltageSearchPanel() {
        this(null, null);
    }

    public AutoVoltageSearchPanel(MainModel mainModel, PowerDevice powerDevice) {
        this.mainModel = mainModel;
        this.powerDevice = powerDevice;
        autoSearchCriticalVoltageButton = new JButton("自动搜索临界电压");
        criticalVoltagePanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        lowerCriticalVoltageSpinner = new JSpinner();
        upperCriticalVoltageSpinner = new JSpinner();
        autoSearchCriticalVoltageAddToScriptButton = SwingUtil.getAddToScriptButton();
        lowerCriticalVoltageSpinner.setModel(new SpinnerNumberModel(0.0, 0.0, 30, 0.1));
        upperCriticalVoltageSpinner.setModel(new SpinnerNumberModel(0.0, 0.0, 30, 0.1));
        SwingUtil.setPreferredWidth(lowerCriticalVoltageSpinner, UiConstants.DEFAULT_SPINNER_WIDTH);
        SwingUtil.setPreferredWidth(upperCriticalVoltageSpinner, UiConstants.DEFAULT_SPINNER_WIDTH);
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new FlowLayout(FlowLayout.LEFT));

        criticalVoltagePanel.add(new JLabel("电压搜索下限(V):"));
        criticalVoltagePanel.add(lowerCriticalVoltageSpinner);
        criticalVoltagePanel.add(new JLabel("电压搜索上限(V):"));
        criticalVoltagePanel.add(upperCriticalVoltageSpinner);

        add(criticalVoltagePanel);
        add(autoSearchCriticalVoltageButton);
        add(autoSearchCriticalVoltageAddToScriptButton);
    }

    @Override
    public void createActions() {
        autoSearchCriticalVoltageButton.addActionListener(e -> {
            if (Objects.equals(lowerCriticalVoltageSpinner.getValue(), 0.0) || Objects.equals(upperCriticalVoltageSpinner.getValue(), 0.0)) {
                JOptionPane.showMessageDialog(null, "上下限不能为空");
                return;
            }
            CriticalVoltage criticalVoltage = new CriticalVoltage();
            criticalVoltage.setMinimumLowerVoltage(((Double) lowerCriticalVoltageSpinner.getValue()).floatValue());
            criticalVoltage.setMinimumUpperVoltage(((Double) upperCriticalVoltageSpinner.getValue()).floatValue());
            OperationResult operationResult = powerDevice.searchMinimumCriticalVoltage(criticalVoltage);
            if (operationResult.isOk()) {
                SwingUtil.showInformationDialog(null, operationResult.getData());
            } else {
                SwingUtil.showWarningDialog(null, operationResult.getMessage());
            }
        });
        autoSearchCriticalVoltageAddToScriptButton.addActionListener(e -> {
            //TODO：和上面代码精简合并
            if (Objects.equals(lowerCriticalVoltageSpinner.getValue(), 0) || Objects.equals(upperCriticalVoltageSpinner.getValue(), 0)) {
                JOptionPane.showMessageDialog(null, "上下限不能为空");
                return;
            }
            Operation operation = Operation.buildOperation(powerDevice);
            operation.setOperationMethod(DeviceMethods.searchMinimumCriticalVoltage);
            CriticalVoltage criticalVoltage = new CriticalVoltage();
            criticalVoltage.setMinimumLowerVoltage(((Double) lowerCriticalVoltageSpinner.getValue()).floatValue());
            criticalVoltage.setMinimumUpperVoltage(((Double) upperCriticalVoltageSpinner.getValue()).floatValue());
            operation.setOperationObject(criticalVoltage);
            mainModel.getOperationModel().updateOperation(operation);
        });
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        autoSearchCriticalVoltageButton.setEnabled(isDeviceConnected);
    }

    @Override
    public void buildUIFromOperation(Operation injectedOperation) throws JsonAssembleException {
        CriticalVoltage criticalVoltage = JSON.to(CriticalVoltage.class, injectedOperation.getOperationObject());
        lowerCriticalVoltageSpinner.setValue((double) criticalVoltage.getMinimumLowerVoltage());
        upperCriticalVoltageSpinner.setValue((double) criticalVoltage.getMinimumUpperVoltage());
    }

    @Override
    public Operation assembleOperation(Operation injectedOperation) {
        CriticalVoltage criticalVoltage = new CriticalVoltage();
        criticalVoltage.setMinimumLowerVoltage(((Double) lowerCriticalVoltageSpinner.getValue()).floatValue());
        criticalVoltage.setMinimumUpperVoltage(((Double) upperCriticalVoltageSpinner.getValue()).floatValue());
        injectedOperation.setOperationObject(criticalVoltage);
        return injectedOperation;
    }

    @Override
    public boolean checkUserInput() {
        return true;
    }

    @Override
    public JComponent getAssembleContainer() {
        return criticalVoltagePanel;
    }

}
