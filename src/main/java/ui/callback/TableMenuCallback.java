package ui.callback;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-26 14:54
 * @description :
 * @modified By :
 * @since : 2022-7-26
 */
public interface TableMenuCallback<T extends Serializable> {
    default void clearClipBoardActivated() {

    }

    default void deleteRowsActivated(int[] rows) {

    }

    default boolean clearTableActivated() {
        return false;
    }

    //add by lhy
    default void pasteRowsActivated(List<T> clipBoard) {

    }


    default void selectedRangeActivated() {

    }

    default void copyRowsActivated() {

    }

    default void pasteRowsActivated() {

    }

}
