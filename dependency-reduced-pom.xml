<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.desaysv</groupId>
  <artifactId>flyTestClient</artifactId>
  <version>1.0-SNAPSHOT</version>
  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
    <finalName>flyTestClient</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <outputDirectory>D:\00-Code\05-Web_Backend\AITestX\00-编译\FlyTest安装包</outputDirectory>
              <finalName>flyTestClient</finalName>
              <transformers>
                <transformer>
                  <manifestEntries>
                    <Main-Class>MainApp</Main-Class>
                    <Build-Number>123</Build-Number>
                  </manifestEntries>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <properties>
    <lombok.version>1.18.22</lombok.version>
    <hutool.version>5.2.5</hutool.version>
    <maven.compiler.target>8</maven.compiler.target>
    <commons.lang3.verson>3.12.0</commons.lang3.verson>
    <okhttp.version>4.9.3</okhttp.version>
    <commons.text.verson>1.9</commons.text.verson>
    <logback.version>1.2.13</logback.version>
    <jackson.version>2.13.2.2</jackson.version>
    <websocket.version>1.5.3</websocket.version>
    <maven.compiler.source>8</maven.compiler.source>
    <fastjson.version>1.2.75</fastjson.version>
  </properties>
</project>
