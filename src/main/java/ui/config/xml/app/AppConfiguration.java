package ui.config.xml.app;

import common.utils.XMLUtils;
import lombok.Getter;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import ui.config.base.XmlConfiguration;

import java.io.IOException;

/**
 * APP文件夹配置
 * TODO:后续支持xml+json配置
 */
@Getter
public class AppConfiguration {

    private volatile static AppConfiguration appConfiguration;

    private final AppConfig appConfig;

    public static AppConfiguration getInstance() {
        if (appConfiguration == null) {
            synchronized (AppConfiguration.class) {
                if (appConfiguration == null) {
                    appConfiguration = new AppConfiguration();
                }
            }
        }
        return appConfiguration;
    }

    private AppConfiguration() {
        XmlConfiguration xmlConfiguration = new XmlConfiguration();
        try {
            xmlConfiguration.setConfigFile(AppFileManager.getInstance().getConfigFolder().createFile("appConfig.xml"));
            xmlConfiguration.setDoc(XMLUtils.getDocument(xmlConfiguration.getConfigFile()));
        } catch (IOException | DocumentException e) {
            xmlConfiguration.setDoc(DocumentHelper.createDocument());
        }
        appConfig = new AppConfig(xmlConfiguration);
    }

}
