package ui.config.base;

import common.utils.FileUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

public class Folder {

    private final File directory;

    public Folder(File directory) {
        this.directory = directory;
    }

    /**
     * 文件夹是否存在
     *
     * @return 是否存在
     */
    public boolean exists() {
        return directory != null && directory.exists();
    }

    public Folder createFolder(String subFolderName) {
        File newFolder = new File(directory, subFolderName);
        if (!newFolder.exists()) {
            newFolder.mkdir();
        }
        return new Folder(newFolder);
    }

    public File createFile(String fileName) throws IOException {
        File newFile = new File(directory, fileName);
        if (!newFile.exists()) {
            newFile.createNewFile();
        }
        return newFile;
    }

    public String getPath() {
        return directory.getPath();
    }

    public String getAbsolutePath() {
        return directory.getAbsolutePath();
    }

    public File[] files() {
        return directory.listFiles();
    }

    public boolean delete(String fileOrFolder) {
        File target = new File(directory, fileOrFolder);
        if (target.isDirectory()) {
            return FileUtils.deleteAllFile(target);
        } else {
            return target.delete();
        }
    }

    public boolean delete() {
        if (directory.isDirectory()) {
            return FileUtils.deleteAllFile(directory);
        } else {
            return directory.delete();
        }
    }

    public boolean rename(String name) {
        File newFile = new File(directory.getParentFile(), name);
        return directory.renameTo(newFile);
    }


    public Path copy(File file) throws IOException {
        Path src = file.toPath();
        Path target = new File(directory, file.getName()).toPath();
        if (!src.equals(target)) {
            Files.copy(src, target);
        }
        return target;
    }

    public File toFile() {
        return directory;
    }

    public Folder getFolder(String folder) {
        return new Folder(new File(directory, folder));
    }

    public File getFile(String file) {
        return new File(directory, file);
    }

}
