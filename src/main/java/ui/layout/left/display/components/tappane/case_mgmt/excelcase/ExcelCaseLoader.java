package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import cn.hutool.core.map.MapUtil;
import common.utils.FileUtils;
import excelcase.config.json.CaseConfigJson;
import excelcase.config.json.CaseContent;
import excelcase.config.json.ExcelCaseTemplate;
import excelcase.exportcase.CaseReportFileConfig;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.util.CollectionUtils;
import sdk.base.HttpContext;
import sdk.base.JsonResponse;
import sdk.domain.excel.ExcelEntity;
import sdk.domain.excel.ExcelReadOption;
import sdk.domain.excel.ExcelSheetData;
import sdk.entity.ExcelKit;
import sdk.entity.OperationTargetHolder;
import ui.entry.ClientView;
import ui.layout.left.display.dialogs.DialogCallback;
import ui.layout.left.display.dialogs.SelectedExcelSheetDialog;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.io.File;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static common.utils.FileUtils.getFileNameByFilePath;
import static sdk.constants.ExportTypeConstants.EXPORT_ORIGINAL_FILE;

/**
 * Excel测试用例导入
 */
@Slf4j
public class ExcelCaseLoader implements ExcelCaseImportListener {
    @Getter
    private List<String> sheetNameList;

    private final ExcelKit excelKit = OperationTargetHolder.getExcelKit();

    private final ExcelCaseTabPaneView excelCaseTabPaneView;

    private final ExcelCaseControlPanel excelCaseControlPanel;

    private final ExcelCaseRenderTabbedPane excelCaseRenderTabbedPane;
    private Map<String, Boolean> selectedSheetMap;
    private ExcelEntity excelEntity = null;
    private final MainModel mainModel;
    private final ClientView clientView;
    private JProgressBar loadingBar;
    private final ExcelCaseLoaderListener excelCaseLoaderListener;


    public ExcelCaseLoader(MainModel mainModel,
                           ClientView clientView,
                           ExcelCaseControlPanel excelCaseControlPanel,
                           ExcelCaseTabPaneView excelCaseTabPaneView,
                           ExcelCaseLoaderListener excelCaseLoaderListener) {
        this.excelCaseLoaderListener = excelCaseLoaderListener;
        this.mainModel = mainModel;
        this.clientView = clientView;
        this.excelCaseTabPaneView = excelCaseTabPaneView;
        this.excelCaseControlPanel = excelCaseControlPanel;
        this.excelCaseRenderTabbedPane = excelCaseTabPaneView.getExcelCaseRenderTabbedPane();
        sheetNameList = new ArrayList<>();
        adjustZipRatio();
    }


    /**
     * 选择Excel
     *
     * @return ExcelEntity
     */
    private ExcelEntity getExcelEntity() {
        File selectedFile = selectFile();
        return processSelectedFile(selectedFile);
    }

    private ExcelEntity getExcelEntity(File selectedFile) {
        return processSelectedFile(selectedFile);
    }

    private File selectFile() {
        // 创建主面板
        JPanel panel = new JPanel(new BorderLayout(5, 5));

        // 创建左侧标签和输入框的面板
        JPanel inputPanel = new JPanel(new BorderLayout());
        JLabel pathLabel = new JLabel("文件路径:");
        pathLabel.setPreferredSize(new Dimension(80, 25)); // 设置标签固定宽度

        JTextField pathField = new JTextField(30);
        inputPanel.add(pathLabel, BorderLayout.WEST);
        inputPanel.add(pathField, BorderLayout.CENTER);

        // 创建右侧按钮面板
        JPanel buttonPanel = new JPanel();
        JButton browseBtn = new JButton("选择文件");
        buttonPanel.add(browseBtn);

        // 组合组件
        panel.add(inputPanel, BorderLayout.CENTER);
        panel.add(buttonPanel, BorderLayout.EAST);

        // 设置默认路径
//        String projectPath = "D:\\FlyTest\\data\\client\\projects";
        String fileChooserPath = CaseConfigJson.getInstance().getOriginalExcelCaseFilePath();
        pathField.setText(fileChooserPath != null ? fileChooserPath : "");

        browseBtn.addActionListener(e -> {
            File selected = SwingUtil.getFileChooser(excelCaseRenderTabbedPane,
                    "打开Excel测试用例",
                    new FileNameExtensionFilter("Excel测试用例(.xlsx,.xls,.xlsm)", "xlsx", "xls", "xlsm"),
                    new File(pathField.getText()), false);
            if (selected != null) {
                pathField.setText(selected.getAbsolutePath());
            }
        });

        int result = JOptionPane.showConfirmDialog(excelCaseRenderTabbedPane,
                panel,
                "选择Excel文件或输入路径",
                JOptionPane.OK_CANCEL_OPTION,
                JOptionPane.PLAIN_MESSAGE);

        if (result == JOptionPane.OK_OPTION) {
            File selectedFile = new File(pathField.getText().replaceAll("\"", ""));
            if (!selectedFile.exists()) {  // 检查文件是否存在
                SwingUtil.showWarningDialog(panel,
                        "文件不存在: " + selectedFile.getAbsolutePath());
                return null;
            }
            return selectedFile;
        }
        return null;
    }


    public void adjustZipRatio() {
        // 设置最小压缩比为 0.001
        ZipSecureFile.setMinInflateRatio(0.001);
    }

    private ExcelEntity processRemoteFile(String filePath, List<String> selectedSheetNameList) {
        if (filePath == null) {
            return null;
        }
        adjustZipRatio();
        List<String> sheetNameList = excelKit.fetchExcelSheetNames(filePath).getData();
        if (sheetNameList == null) {
            log.error("Excel文件损坏！！！");
            return null;
        }
//        Map<String, Boolean> selectedSheetMap = CaseConfigJson.getInstance().getSelectedSheetMap();
        LinkedHashMap<String, Boolean> sheetMap = new LinkedHashMap<>();
        for (String sheetName : sheetNameList) {
            sheetMap.put(sheetName, selectedSheetNameList.contains(sheetName));
        }

        if (MapUtil.isEmpty(sheetMap)) {
            return null;
        }
        //选取sheetMap中true的string作为list
        List<String> selectedSheetNames = sheetMap.entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        // 自动检测 headerRowNumber
        int headerRowNumber = detectHeaderRowNumber(filePath, selectedSheetNames);
        if (!sheetMap.isEmpty()) {
            saveAndConfigureExcelEntity(filePath, sheetMap, headerRowNumber);
        }
        return excelEntity;
    }


    private ExcelEntity processSelectedFile(File selectedFile) {
        if (selectedFile == null) {
            return null;
        }
        String filePath = selectedFile.getAbsolutePath();
        adjustZipRatio();
        List<String> sheetNameList = excelKit.fetchExcelSheetNames(filePath).getData();
        if (sheetNameList == null) {
            log.error("Excel文件损坏！！！");
            return null;
        }
        Map<String, Boolean> selectedSheetMap = CaseConfigJson.getInstance().getSelectedSheetMap();
        LinkedHashMap<String, Boolean> sheetMap = new LinkedHashMap<>();
        for (String sheetName : sheetNameList) {
            selectedSheetMap = selectedSheetMap == null ? new LinkedHashMap<>() : selectedSheetMap;
            sheetMap.put(sheetName, selectedSheetMap.getOrDefault(sheetName, false));
        }

        SelectedExcelSheetDialog dialog = new SelectedExcelSheetDialog(mainModel, sheetMap);
        dialog.addCallback(new DialogCallback() {
            @Override
            public void onDialogClosed() {
                excelEntity = null;
            }

            @Override
            public void onDialogSaved(LinkedHashMap<String, Boolean> sheetMap) {
                if (MapUtil.isEmpty(sheetMap)) {
                    return;
                }
                //选取sheetMap中true的string作为list
                List<String> selectedSheetNames = sheetMap.entrySet().stream()
                        .filter(Map.Entry::getValue)
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
                // 自动检测 headerRowNumber
                int headerRowNumber = detectHeaderRowNumber(filePath, selectedSheetNames);
                if (!sheetMap.isEmpty()) {
                    saveAndConfigureExcelEntity(filePath, sheetMap, headerRowNumber);
                }
            }
        });
        dialog.showDialog();
        if (excelEntity == null) return null;
        excelEntity.setAllSheetNames(new ArrayList<>(sheetMap.keySet()));
        return excelEntity;
    }

    private int detectHeaderRowNumber(String filePath, List<String> sheetNameList) {
        int headerRowNumber = 1; // 默认值
        // 检查工作表名称列表是否为空
        if (sheetNameList == null || sheetNameList.isEmpty()) {
            log.warn("工作表名称列表为空，使用默认headerRowNumber=1");
            return headerRowNumber;
        }

        String sheetName = sheetNameList.get(0); //取第一个表判断即可
        try {
            JsonResponse<List<List<String>>> response = excelKit.readExcelSheetData(filePath, sheetName, 0); // 从第1行开始读取

            if (response.isOk()) {
                List<List<String>> sheetData = response.getData();

                // 检查数据是否为空
                if (sheetData == null || sheetData.isEmpty()) {
                    log.warn("工作表 {} 数据为空，跳过...", sheetName);
                    return headerRowNumber;
                }

                // 遍历每一行数据
                for (int rowIndex = 0; rowIndex < sheetData.size(); rowIndex++) {
                    List<String> row = sheetData.get(rowIndex); //每一行的数据
                    if (ExcelCaseTemplate.containsAllRequiredSequenceTypes(row)) {
                        headerRowNumber = rowIndex + 1; // 行号从1开始
                        log.info("在工作表 {} 中检测到headerRowNumber={}", sheetName, headerRowNumber);
                        return headerRowNumber;
                    }
                    if (rowIndex > 15) {
                        //超过可能的表头行号范围
                        break;
                    }
                }
            } else {
                log.error("读取工作表 {} 数据失败: {}", sheetName, response.getMessage());
            }
        } catch (Exception e) {
            log.error("检测headerRowNumber时发生错误（工作表={}）: {}", sheetName, e.getMessage(), e);
        }

        return headerRowNumber;
    }

    private void saveAndConfigureExcelEntity(String filePath, LinkedHashMap<String, Boolean> sheetMap, int headerRowNumber) {
        String fileName = getFileNameByFilePath(filePath);
        String copyFilePath = String.format("%s%s%s", CaseReportFileConfig.getInstance().getTemplateFile(), "\\", fileName);
        FileUtils.copyFile(filePath, copyFilePath);

        excelEntity = new ExcelEntity();
        excelEntity.setOriginalFilePath(filePath);
        excelEntity.setTemplateFilePath(copyFilePath);
        List<String> selectedSheetNames = sheetMap.entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        excelEntity.setSelectedSheetNames(selectedSheetNames);
        // 设置headerRowNumber
        excelEntity.setHeaderRowNumber(headerRowNumber);
        excelCaseRenderTabbedPane.removeAllTabbedPane();
        updateCaseConfigJson(filePath, copyFilePath, sheetMap, headerRowNumber);
    }

    private void updateCaseConfigJson(String originalFilePath, String templateFilePath, LinkedHashMap<String, Boolean> sheetMap, int headerRowNumber) {
        CaseConfigJson configJson = CaseConfigJson.getInstance();
        configJson.setOriginalExcelCaseFilePath(originalFilePath);
        configJson.setTemplateExcelCaseFilePath(templateFilePath);
        configJson.setSelectedSheetMap(sheetMap);
        configJson.setHeaderRowNumber(headerRowNumber); // 更新headerRowNumber

        List<CaseContent> newCaseContentList = new ArrayList<>();
        List<CaseContent> excelCaseContentList = configJson.getExcelCaseContentList();
        if (!CollectionUtils.isEmpty(excelCaseContentList)) {
            newCaseContentList = excelCaseContentList.stream()
                    .filter(content -> sheetMap.containsKey(content.getSheetName()))
                    .collect(Collectors.toList());
        }

        configJson.setExcelCaseContentList(newCaseContentList);
        configJson.setTestMode(configJson.getTestMode());
        configJson.setTestTimes(configJson.getTestTimes());
    }

    /**
     * 刷新原始Excel
     *
     * @return ExcelEntity
     */
    public ExcelEntity getOriginalExcelEntity() {
        String originalFilePath = CaseConfigJson.getInstance().getOriginalExcelCaseFilePath();
        String fileName = getFileNameByFilePath(originalFilePath);
        String copyFilePath = String.format("%s%s%s", CaseReportFileConfig.getInstance().getTemplateFile(), "\\", fileName);
        FileUtils.copyFile(originalFilePath, copyFilePath);
        //按照路径复制一样的文件，用于后续执行测试时，写入修改  end
        ExcelEntity excelEntity = new ExcelEntity();
        excelEntity.setOriginalFilePath(originalFilePath);
        excelEntity.setTemplateFilePath(copyFilePath);
        List<String> sheetNameList = CaseConfigJson.getInstance().getSelectedSheetMap().entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        excelEntity.setSelectedSheetNames(sheetNameList);
        excelCaseRenderTabbedPane.removeAllTabbedPane();
        //因重新导入的Excel Case的用例信息不相同，更新CaseConfigJson
        CaseConfigJson.getInstance().setTemplateExcelCaseFilePath(copyFilePath);
        return excelEntity;
    }


    /**
     * 从服务端导入数据
     *
     * @param excelEntity  ExcelEntity
     * @param manualImport 是否手动导入
     *                     重启应用和界面上加载选择的用例表，manualImport=false，按配置文件来更新选中行和表头设置
     *                     手动点击“导入用例”和“刷新用例”，manualImport=true，按Selected字段来更新选中行，按Bu的模板表头来进行表头设置
     */
    public void loadExcelDataFromServer(ExcelEntity excelEntity) {
        new SwingWorker<Boolean, Void>() {
            boolean result = false;
            @Override
            protected Boolean doInBackground() {
                startRenderUIStatus();
                //导入Excel测试案例
                if (excelEntity != null) {
                    HttpContext httpContext = new HttpContext();
                    httpContext.setMuteLog(true);
                    //FIXME:没有针对不同表设置不同的headerRowNumber
                    excelEntity.setHeaderRowNumber(CaseConfigJson.getInstance().getHeaderRowNumber());
//                    excelEntity.setReloadProject(!manualImport);
                    excelEntity.setProjectName(mainModel.getAppInfo().getProject());
                    LinkedHashMap<String, Boolean> allSheetMap = CaseConfigJson.getInstance().getSelectedSheetMap();
                    excelEntity.setAllSheetNames(allSheetMap == null ? null : new ArrayList<>(allSheetMap.keySet()));
                    JsonResponse<LinkedHashMap<String, ExcelSheetData>> response = excelKit.loadTestCasesFromExcel(excelEntity, httpContext);
                    if (response.isOk()) {
                        return excelCaseRenderTabbedPane.handleData(response.getData(), true);
                    }
                }
                return false;
            }

            @Override
            protected void done() {
                try {
                    result = get();
                    if (!result) {
                        SwingUtil.showWarningDialog(excelCaseControlPanel, "加载用例失败...");
                    } else {
                        log.info("导入Excel案例成功");
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error(e.getMessage(), e);
                } finally {
                    completeRenderUIStatus();
                }
            }
        }.execute();
    }

    /**
     * 导入带有语法规则的Excel用例
     */
    public boolean loadGrammaticalRuleExcelCase() {
        ExcelEntity excelEntity = getExcelEntity();
        if (excelEntity == null)
            return false;
        excelEntity.setReadOption(ExcelReadOption.SCRIPT_MODE.name());
        loadExcelDataFromServer(excelEntity);
        return true;
    }

    /**
     * 远程控制导入带有语法规则的Excel用例
     */
    public boolean remoteLoadGrammaticalRuleExcelCase(String filePath, List<String> selectedSheetNameList) {
        ExcelEntity excelEntity = processRemoteFile(filePath, selectedSheetNameList);
        if (excelEntity == null)
            return false;
        excelEntity.setReadOption(ExcelReadOption.SCRIPT_MODE.name());
        loadExcelDataFromServer(excelEntity);
        return true;
    }


    //从数据库里面拿case数据渲染
    public void loadExcelCaseFromDB(ExcelEntity excelEntity) {
        new SwingWorker<Boolean, Void>() {
            boolean result = false;
            @Override
            protected Boolean doInBackground() {
                startRenderUIStatus();
                //请求后端拿数据库中的TestCase数据
                JsonResponse<LinkedHashMap<String, ExcelSheetData>> response = excelKit.loadTestCasesFromDB(excelEntity, new HttpContext());
                if (response.isOk()) {
                    //在界面上渲染用例数据
                    return excelCaseRenderTabbedPane.handleData(response.getData(),false);
                }
                return false;
            }
            @Override
            protected void done() {
                try {
                    //根据handleData返回的结果，判断是否加载成功
                    result = get();
                    if (!result) {
                        SwingUtil.showWarningDialog(excelCaseControlPanel, "加载用例失败...");
                    } else {
                        log.info("导入Excel案例成功");
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error(e.getMessage(), e);
                } finally {
                    completeRenderUIStatus();
                }
            }
        }.execute();
    }

    private void startRenderUIStatus() {
        //加载用例前，先显示加载进度条及禁用测试按钮等状态
        loadingBar = excelCaseTabPaneView.getLoadingBar();
        SwingUtil.invokeLater(() -> {
            loadingBar.setVisible(true);
            if (excelCaseLoaderListener != null) {
                excelCaseLoaderListener.beforeLoad();
            }
        });
    }

    private void completeRenderUIStatus() {
        SwingUtil.invokeLater(() -> {
            //渲染完成后，隐藏进度条
            loadingBar.setVisible(false);
            //最后取消禁用测试按钮等状态
            if (excelCaseLoaderListener != null) {
                excelCaseLoaderListener.afterLoad();
            }
        });
        //判断syncHeaderTemplateMap是否为空，不为空，取第一个符合模板显示的sheet，同步字段模板单例类，并请求同步到后端，因为后端也需要字段模板
        excelCaseRenderTabbedPane.renderTableComplete();

    }


//    public void reloadExcelCase(ExcelEntity excelEntity, boolean manualImport) {
//        List<String> loadSheetNames = new ArrayList<>();
//        List<String> sheetNames = excelEntity.getSheetNames();
//        for (String sheetName : sheetNames) {
//            boolean exist = excelCaseRenderTabbedPane.checkTabbedPaneExist(sheetName);
//            if (!exist) {
//                loadSheetNames.add(sheetName);
//            }
//        }
//        excelEntity.setSheetNames(loadSheetNames);
//        loadExcelDataFromServer(excelEntity, manualImport);
//    }

    public void removeTabbedPaneBySheetNames(List<String> sheetNameList) {
        for (String sheetName : sheetNameList) {
            excelCaseRenderTabbedPane.removeTabbedPane(sheetName);
        }
    }

    public JsonResponse<String> updateExcelCaseToOriginalFile() {
        ExcelCaseTable table = excelCaseRenderTabbedPane.getSelectedExcelCaseTable();
        JsonResponse<String> result = null;
        if (table != null) {
            result  = excelCaseControlPanel.exportExcelCaseReport(EXPORT_ORIGINAL_FILE.name());
            if (result.isOk()) {
//                JOptionPane.showMessageDialog(excelCaseRenderTabbedPane, String.format("保存到本地用例成功，本地文件路径:%s", CaseConfigJson.getInstance().getOriginalExcelCaseFilePath()));
                //点击保存用例时，同步到Template文件
                FileUtils.copyFile(CaseConfigJson.getInstance().getOriginalExcelCaseFilePath(), CaseConfigJson.getInstance().getTemplateExcelCaseFilePath());
            }
        } else {
//            JOptionPane.showMessageDialog(excelCaseRenderTabbedPane, "Excel用例未加载，无法更新用例！");
            log.error("Excel用例未加载，无法更新用例！");
        }
        return result;
    }

    private void syncTemplateFileToOriginalFile() {
        String templateFilePath = CaseConfigJson.getInstance().getTemplateExcelCaseFilePath();
        String originalFilePath = CaseConfigJson.getInstance().getOriginalExcelCaseFilePath();
        FileUtils.copyFile(templateFilePath, originalFilePath);  //复制本地文件，并同步到template文件中
    }


    public void removeAllTabbedPane() {
        List<String> allTabbedPanes = excelCaseRenderTabbedPane.getAllTabbedPaneTitle();
        allTabbedPanes.forEach(excelCaseRenderTabbedPane::removeTabbedPane);
    }

    @Override
    public void importExcelCase(File file) {
        ExcelEntity excelEntity = getExcelEntity(file);
        if (excelEntity == null) return;
        excelEntity.setReadOption(ExcelReadOption.SCRIPT_MODE.name());
        excelCaseControlPanel.updateComboBox(CaseConfigJson.getInstance().getSelectedSheetMap());
        loadExcelDataFromServer(excelEntity);
    }
}
