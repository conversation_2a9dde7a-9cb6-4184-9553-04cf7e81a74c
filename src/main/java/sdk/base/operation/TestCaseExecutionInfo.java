package sdk.base.operation;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

@Data
@AllArgsConstructor
public class TestCaseExecutionInfo {
    private int actualResult;
    @NotNull
    private String endTime;
    private int failCycle;
    private int testCycle;
    //    启动集合接口先定义testSuiteId testcaseIndex
    private int testSuiteId;
    private int testcaseIndex;
    private long duration;
    @NotNull
    private String startTime;

}
