package ui.callback;

import sdk.base.execution.ExcelCaseExecutionNotification;

public interface ExcelCaseExecutionMonitorListener {
    void excelCaseExecutionTesting(ExcelCaseExecutionNotification notification);

    void excelCaseExecutionPausing(ExcelCaseExecutionNotification notification);

    void excelCaseExecutionAllCompleted(ExcelCaseExecutionNotification notification);

    void excelCaseExecutionException(ExcelCaseExecutionNotification notification);

    void excelCaseExecutionCheckResult(ExcelCaseExecutionNotification notification);

}
