package ui.layout.left.display.components.container.base;

import lombok.Getter;
import lombok.Setter;
import sdk.domain.Device;
import ui.base.BaseView;
import ui.callback.DeviceCallback;
import ui.entry.ClientView;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-28 17:57
 * @description :
 * @modified By :
 * @since : 2022-6-28
 */
//TODO:新建tabPane子类
@Getter
public abstract class DeviceContainer extends JPanel implements BaseView, DeviceCallback {
    private Device device;
    private final MainModel mainModel;

    private final ClientView clientView;

    @Setter
    private List<DeviceCallback> deviceCallbackList;

    public void addDeviceCallback(DeviceCallback deviceCallback) {
        deviceCallbackList.add(deviceCallback);
    }

    public DeviceContainer(ClientView clientView, MainModel mainModel, Device device) {
        this.device = device;
        this.clientView = clientView;
        this.mainModel = mainModel;
        deviceCallbackList = new ArrayList<>();
        addDeviceCallback(this);
    }

    public DeviceContainer(ClientView clientView, MainModel mainModel) {
        this.clientView = clientView;
        this.mainModel = mainModel;
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        if (isDeviceConnected) {
            setBorder(BorderFactory.createLineBorder(Color.GREEN, 2));
        } else {
            setBorder(BorderFactory.createLineBorder(Color.RED, 2));
        }
    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        this.device = device;
        controlDisplay(true);
    }

    @Override
    public void deviceDisconnected(Device device) {
        controlDisplay(false);
    }

}
