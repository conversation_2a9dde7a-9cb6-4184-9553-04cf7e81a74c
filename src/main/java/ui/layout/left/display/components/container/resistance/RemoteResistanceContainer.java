package ui.layout.left.display.components.container.resistance;

import sdk.base.BaseHttpClient;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationResult;
import sdk.constants.methods.DeviceMethods;
import sdk.domain.Device;
import sdk.entity.RemoteResistanceDevice;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.border.LineBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;

/**
 * @author: QinHao
 * @description: 程控电阻仪
 * @date: 2024/7/30 14:04
 */
public class RemoteResistanceContainer extends DeviceContainer {
    private final JTextField ipTextField;
    private final RemoteResistanceDevice remoteResistanceDevice;
    private final JTextField resistanceTextField;
    private final JTextField ipSetTextField;
    private final JComboBox<String> channelSettingComboBox;
    private final JSpinner resistanceSpinner;
    private final JButton resistanceSettingButton;
    private final JButton addToScriptButton;
    private final JComboBox<String> channelSearchComboBox;
    private final JButton resistanceSearchButton;
    private final JButton ipChangeButton;


    public RemoteResistanceContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        ipTextField = new JTextField();
        remoteResistanceDevice = (RemoteResistanceDevice) device;
        resistanceTextField = new JTextField();
        ipSetTextField = new JTextField();
        channelSettingComboBox = new JComboBox<>();
        resistanceSpinner = new JSpinner(new SpinnerNumberModel(100, 1, 16000, 100));

        addToScriptButton = SwingUtil.getDebugButton();
        resistanceSettingButton = SwingUtil.getAddToScriptButton();
        channelSearchComboBox = new JComboBox<>();
        resistanceSearchButton = new JButton("查询");
        ipChangeButton = new JButton("修改");
        createView();
        createActions();

    }

    public void createView() {
        setLayout(new BorderLayout(0, 0));
        JPanel panel = new JPanel();
        add(panel, BorderLayout.NORTH);
        panel.setLayout(new GridLayout(0, 1, 0, 0));
        JPanel panel_0 = new JPanel();
        panel_0.setBorder(new TitledBorder(new LineBorder(new Color(0, 0, 0)), "阻值设定", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        panel_0.setLayout(new GridLayout(2,1,0,0));
        JPanel panelIp = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panelIp.add(new JLabel("IP地址："));
        panelIp.add(ipTextField);
        ipTextField.setColumns(15);
        JPanel panel_1 = new JPanel();
        FlowLayout flowLayout = (FlowLayout) panel_1.getLayout();
        flowLayout.setAlignment(FlowLayout.LEFT);
        panel_1.add(new JLabel("通道："));
        channelSettingComboBox.setModel(new DefaultComboBoxModel<>(new String[]{"通道1", "通道2", "通道3", "通道4"}));
        panel_1.add(channelSettingComboBox);
        panel_1.add(new JLabel("阻值："));
        panel_1.add(resistanceSpinner);
        panel_1.add(resistanceSettingButton);
        panel_1.add(addToScriptButton);

        panel_0.add(panelIp);
        panel_0.add(panel_1);

        panel.add(panel_0);

        JPanel panel_2 = new JPanel();
        panel_2.setBorder(new TitledBorder(new LineBorder(new Color(0, 0, 0)), "阻值查询", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        FlowLayout flowLayout_1 = (FlowLayout) panel_2.getLayout();
        flowLayout_1.setAlignment(FlowLayout.LEFT);
        panel.add(panel_2);

        panel_2.add(new JLabel("通道："));
        channelSearchComboBox.setModel(new DefaultComboBoxModel<>(new String[]{"通道1", "通道2", "通道3", "通道4"}));
        panel_2.add(channelSearchComboBox);
        panel_2.add(new JLabel("阻值："));
        resistanceTextField.setEnabled(false);
        panel_2.add(resistanceTextField);
        resistanceTextField.setColumns(10);
        panel_2.add(new JLabel("Ω"));
        panel_2.add(resistanceSearchButton);

        JPanel panel_3 = new JPanel();
        panel_3.setBorder(new TitledBorder(new LineBorder(new Color(0, 0, 0)), "IP地址设定", TitledBorder.LEADING, TitledBorder.TOP, null, null));
        FlowLayout flowLayout_2 = (FlowLayout) panel_3.getLayout();
        flowLayout_2.setAlignment(FlowLayout.LEFT);
        panel.add(panel_3);

        panel_3.add(new JLabel("IP地址："));
        panel_3.add(ipSetTextField);
        ipSetTextField.setColumns(20);
        panel_3.add(ipChangeButton);
        panel_3.add(new JLabel("注：修改IP地址后需重新连接设备"));
        ipChangeButton.setEnabled(false);
    }

    public void createActions() {
        resistanceSettingButton.addActionListener(e -> {
            if (!isValidIPv4(ipTextField.getText())) {
                SwingUtil.showWebMessageDialog(this, "请输入正确的IP");
                return;
            }
            ResistanceData resistanceData = ResistanceData.builder()
                    .channel(channelSettingComboBox.getSelectedIndex() + 10)
                    .resistance((int) resistanceSpinner.getValue())
                    .deviceIP(ipTextField.getText())
                    .build();
            Operation operation = Operation.buildOperation(remoteResistanceDevice);
            operation.setOperationMethod(DeviceMethods.resistanceSet);
            operation.setOperationObject(resistanceData);
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isFailed()) {
                SwingUtil.showWebMessageDialog(this, operationResult.getMessage());
            }
        });
        addToScriptButton.addActionListener(e -> {
            if (!isValidIPv4(ipTextField.getText())) {
                SwingUtil.showWebMessageDialog(this, "请输入正确的IP");
                return;
            }
            ResistanceData resistanceData = ResistanceData.builder()
                    .channel(channelSettingComboBox.getSelectedIndex() + 10)
                    .resistance((int) resistanceSpinner.getValue())
                    .deviceIP(ipTextField.getText())
                    .build();
            Operation operation = Operation.buildOperation(remoteResistanceDevice);
            operation.setOperationMethod(DeviceMethods.resistanceSet);
            operation.setOperationObject(resistanceData);
            getMainModel().getOperationModel().updateOperation(operation);
        });

        resistanceSearchButton.addActionListener(e -> {
            if (!isValidIPv4(ipTextField.getText())) {
                SwingUtil.showWebMessageDialog(this, "请输入正确的IP");
                return;
            }
            int channelNum = channelSearchComboBox.getSelectedIndex() + 10;
            String ip = ipTextField.getText();
            ResistanceData resistanceData = ResistanceData.builder().channel(channelNum).deviceIP(ip).build();
            Operation operation = Operation.buildOperation(remoteResistanceDevice);
            operation.setOperationMethod(DeviceMethods.resistanceGet);
            operation.setOperationObject(resistanceData);
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isOk()) {
                resistanceTextField.setText(operationResult.getData().toString());
            }
        });

        ipChangeButton.addActionListener(e -> {
            if (!isValidIPv4(ipSetTextField.getText())) {
                SwingUtil.showWebMessageDialog(this, "请输入正确的IP");
                return;
            }
            String ipAddress = ipSetTextField.getText();
            Operation operation = Operation.buildOperation(remoteResistanceDevice);
            operation.setOperationMethod(DeviceMethods.resistanceSetIP);
            operation.setOperationObject(ipAddress);
            OperationResult operationResult = BaseHttpClient.executeOperation(operation);
            if (operationResult.isFailed()) {
                SwingUtil.showWebMessageDialog(this, operationResult.getMessage());
            }
        });
    }

    public static boolean isValidIPv4(String ip) {
        String regex = "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\."
                + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\."
                + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\."
                + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return ip.matches(regex);
    }


}
