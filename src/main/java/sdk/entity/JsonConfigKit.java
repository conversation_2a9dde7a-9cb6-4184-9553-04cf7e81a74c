package sdk.entity;

import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import sdk.base.BaseHttpClient;
import sdk.base.JsonResponse;
import sdk.constants.UrlConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * JSON配置工具类
 * 用于与后端通信，保存和获取JSON格式的配置信息
 */
@Slf4j
public class JsonConfigKit extends BaseHttpClient {

    /**
     * 保存JSON配置到服务器
     *
     * @param project    项目名称
     * @param filename   文件名
     * @param jsonConfig JSON配置内容
     * @return 保存结果
     */
    public static JsonResponse<String> saveConfig(String project, String filename, Map<String, Object> jsonConfig) {
        Map<String, Object> params = new HashMap<>();
        params.put("project", project);
        params.put("filename", filename);

        try {
            return postJsonResponseWithParams(
                    UrlConstants.ConfigUrls.SAVE_CONFIG,
                    jsonConfig,
                    new TypeReference<JsonResponse<String>>() {
                    },
                    params
            );
        } catch (Exception e) {
            log.error("保存配置失败: {}", e.getMessage(), e);
            return JsonResponse.fail("保存配置失败: " + e.getMessage(), null);
        }
    }

    /**
     * 从服务器获取JSON配置
     *
     * @param project  项目名称
     * @param filename 文件名
     * @return 配置内容
     */
    public static JsonResponse<String> getConfig(String project, String filename) {
        Map<String, Object> params = new HashMap<>();
        params.put("project", project);
        params.put("filename", filename);

        try {
            return getJsonResponseWithParams(
                    UrlConstants.ConfigUrls.GET_CONFIG,
                    new TypeReference<JsonResponse<String>>() {
                    },
                    params
            );
        } catch (Exception e) {
            log.error("获取配置失败: {}", e.getMessage(), e);
            return JsonResponse.fail("获取配置失败: " + e.getMessage(), null);
        }
    }

    /**
     * 保存特定类型的配置对象到服务器
     *
     * @param project  项目名称
     * @param filename 文件名
     * @param config   配置对象
     * @param <T>      配置类型
     * @return 保存结果
     */
    public static <T> JsonResponse<String> saveTypedConfig(String project, String filename, T config) {
        Map<String, Object> params = new HashMap<>();
        params.put("project", project);
        params.put("filename", filename);

        try {
            return postJsonResponseWithParams(
                    UrlConstants.ConfigUrls.SAVE_CONFIG,
                    config,
                    new TypeReference<JsonResponse<String>>() {
                    },
                    params
            );
        } catch (Exception e) {
            log.error("保存配置失败: {}", e.getMessage(), e);
            return JsonResponse.fail("保存配置失败: " + e.getMessage(), null);
        }
    }

    /**
     * 从服务器获取特定类型的配置对象
     *
     * @param project       项目名称
     * @param filename      文件名
     * @param typeReference 类型引用
     * @param <T>           配置类型
     * @return 配置对象
     */
    public static <T> JsonResponse<T> getTypedConfig(String project, String filename, TypeReference<JsonResponse<T>> typeReference) {
        Map<String, Object> params = new HashMap<>();
        params.put("project", project);
        params.put("filename", filename);

        try {
            return getJsonResponseWithParams(
                    UrlConstants.ConfigUrls.GET_CONFIG,
                    typeReference,
                    params
            );
        } catch (Exception e) {
            log.error("获取配置失败: {}", e.getMessage(), e);
            return JsonResponse.fail("获取配置失败: " + e.getMessage(), null);
        }
    }
}
