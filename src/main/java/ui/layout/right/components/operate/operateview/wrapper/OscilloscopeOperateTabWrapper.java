package ui.layout.right.components.operate.operateview.wrapper;

import sdk.constants.DeviceType;
import ui.layout.right.components.operate.operateview.base.DeviceOperateTabWrapper;
import ui.layout.right.components.operate.operateview.panel.OscilloscopeOperatePanel;
import ui.model.MainModel;

import static sdk.constants.DeviceModel.Oscilloscope.*;

public class OscilloscopeOperateTabWrapper extends DeviceOperateTabWrapper {
    public OscilloscopeOperateTabWrapper(MainModel mainModel) {
        super(mainModel);
    }

    @Override
    protected String getDeviceType() {
        return DeviceType.DEVICE_OSCILLOSCOPE;
    }

    @Override
    protected void initTabs(MainModel mainModel) {
        addOperatePanel(RIGOL_800, new OscilloscopeOperatePanel(mainModel, RIGOL_800));
        addOperatePanel(SDS_5000X, new OscilloscopeOperatePanel(mainModel, SDS_5000X));
        addOperatePanel(SDS_6000X, new OscilloscopeOperatePanel(mainModel, SDS_6000X));
    }
}
